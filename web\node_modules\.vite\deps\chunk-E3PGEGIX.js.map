{"version": 3, "sources": ["../../@tiptap/extension-text-style/src/text-style.ts"], "sourcesContent": ["import {\n  Mark,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface TextStyleOptions {\n  /**\n   * HTML attributes to add to the span element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n  /**\n   * When enabled, merges the styles of nested spans into the child span during HTML parsing.\n   * This prioritizes the style of the child span.\n   * Used when parsing content created in other editors.\n   * (Fix for ProseMirror's default behavior.)\n   * @default false\n   */\n  mergeNestedSpanStyles: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textStyle: {\n      /**\n       * Remove spans without inline style attributes.\n       * @example editor.commands.removeEmptyTextStyle()\n       */\n      removeEmptyTextStyle: () => ReturnType,\n    }\n  }\n}\n\nconst mergeNestedSpanStyles = (element: HTMLElement) => {\n  if (!element.children.length) { return }\n  const childSpans = element.querySelectorAll('span')\n\n  if (!childSpans) { return }\n\n  childSpans.forEach(childSpan => {\n    const childStyle = childSpan.getAttribute('style')\n    const closestParentSpanStyleOfChild = childSpan.parentElement?.closest('span')?.getAttribute('style')\n\n    childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`)\n\n  })\n}\n\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `textColor` and `backgroundColor` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nexport const TextStyle = Mark.create<TextStyleOptions>({\n  name: 'textStyle',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      mergeNestedSpanStyles: false,\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'span',\n        getAttrs: element => {\n          const hasStyles = (element as HTMLElement).hasAttribute('style')\n\n          if (!hasStyles) {\n            return false\n          }\n          if (this.options.mergeNestedSpanStyles) { mergeNestedSpanStyles(element) }\n\n          return {}\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      removeEmptyTextStyle: () => ({ tr }) => {\n\n        const { selection } = tr\n\n        // Gather all of the nodes within the selection range.\n        // We would need to go through each node individually\n        // to check if it has any inline style attributes.\n        // Otherwise, calling commands.unsetMark(this.name)\n        // removes everything from all the nodes\n        // within the selection range.\n        tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n\n          // Check if it's a paragraph element, if so, skip this node as we apply\n          // the text style to inline text nodes only (span).\n          if (node.isTextblock) {\n            return true\n          }\n\n          // Check if the node has no inline style attributes.\n          // Filter out non-`textStyle` marks.\n          if (\n            !node.marks.filter(mark => mark.type === this.type).some(mark => Object.values(mark.attrs).some(value => !!value))) {\n            // Proceed with the removal of the `textStyle` mark for this node only\n            tr.removeMark(pos, pos + node.nodeSize, this.type)\n          }\n        })\n\n        return true\n      },\n    }\n  },\n\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAkCA,IAAM,wBAAwB,CAAC,YAAwB;AACrD,MAAI,CAAC,QAAQ,SAAS,QAAQ;AAAE;;AAChC,QAAM,aAAa,QAAQ,iBAAiB,MAAM;AAElD,MAAI,CAAC,YAAY;AAAE;;AAEnB,aAAW,QAAQ,eAAY;;AAC7B,UAAM,aAAa,UAAU,aAAa,OAAO;AACjD,UAAM,iCAAgC,MAAA,KAAA,UAAU,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,MAAM,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,OAAO;AAEpG,cAAU,aAAa,SAAS,GAAG,6BAA6B,IAAI,UAAU,EAAE;EAElF,CAAC;AACH;AAOa,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,UAAU;EAEV,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;MAChB,uBAAuB;;;EAI3B,YAAS;AACP,WAAO;MACL;QACE,KAAK;QACL,UAAU,aAAU;AAClB,gBAAM,YAAa,QAAwB,aAAa,OAAO;AAE/D,cAAI,CAAC,WAAW;AACd,mBAAO;;AAET,cAAI,KAAK,QAAQ,uBAAuB;AAAE,kCAAsB,OAAO;;AAEvE,iBAAO,CAAA;;MAEV;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGjF,cAAW;AACT,WAAO;MACL,sBAAsB,MAAM,CAAC,EAAE,GAAE,MAAM;AAErC,cAAM,EAAE,UAAS,IAAK;AAQtB,WAAG,IAAI,aAAa,UAAU,MAAM,UAAU,IAAI,CAAC,MAAM,QAAO;AAI9D,cAAI,KAAK,aAAa;AACpB,mBAAO;;AAKT,cACE,CAAC,KAAK,MAAM,OAAO,UAAQ,KAAK,SAAS,KAAK,IAAI,EAAE,KAAK,UAAQ,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK,WAAS,CAAC,CAAC,KAAK,CAAC,GAAG;AAEpH,eAAG,WAAW,KAAK,MAAM,KAAK,UAAU,KAAK,IAAI;;QAErD,CAAC;AAED,eAAO;;;;AAKd,CAAA;", "names": []}