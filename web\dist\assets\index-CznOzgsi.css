@charset "UTF-8";.theme-priority{transition:none!important;will-change:background-color,color}[data-theme-refresh=true],[data-force-update]{transition:none!important;animation:instant-refresh .01s}@keyframes instant-refresh{0%{opacity:.99}to{opacity:1}}.bilibili-iframe[data-v-b50d6dee]{position:absolute;top:0;left:0;width:100%;height:100%;border:none}.bilibili-error[data-v-b50d6dee]{display:flex;align-items:center;justify-content:center;height:200px;background-color:var(--color-border-light);color:var(--color-text-secondary);border-radius:4px}.bilibili-error p[data-v-b50d6dee]{margin:0;font-size:14px}.bilibili-container[data-v-b50d6dee]{outline:none;border:none}.code-block-header[data-v-7684eaa4]{display:flex;justify-content:space-between;align-items:center;background-color:var(--creamy-white-2, #e4e1d8);padding:.4rem .8rem;border-bottom:1px solid var(--creamy-white-3, #dcd8ca);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;font-size:.85rem;color:var(--gray-5, rgba(28, 25, 23, 60%));border-top-left-radius:4px;border-top-right-radius:4px;flex-shrink:0}.code-block-language[data-v-7684eaa4]{color:var(--gray-5, rgba(28, 25, 23, 60%));font-weight:500;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;text-transform:lowercase;flex-shrink:0}.code-block-toolbar[data-v-7684eaa4]{display:flex;gap:.5rem}.code-wrap-button[data-v-7684eaa4],.code-copy-button[data-v-7684eaa4]{width:24px;height:24px;padding:4px;background-color:transparent;border:none;border-radius:4px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.code-wrap-button svg[data-v-7684eaa4],.code-copy-button svg[data-v-7684eaa4]{width:16px;height:16px;stroke:var(--gray-5, rgba(28, 25, 23, 60%));transition:all .2s ease}.code-wrap-button[data-v-7684eaa4]:hover,.code-copy-button[data-v-7684eaa4]:hover{background-color:var(--creamy-white-3, #dcd8ca)}.code-wrap-button:hover svg[data-v-7684eaa4],.code-copy-button:hover svg[data-v-7684eaa4]{stroke:var(--black, #2e2b29)}.code-wrap-button.active[data-v-7684eaa4],.code-copy-button.active[data-v-7684eaa4]{background-color:var(--creamy-white-3, #dcd8ca);box-shadow:inset 0 1px 2px #0000001a}.code-wrap-button.active svg[data-v-7684eaa4],.code-copy-button.active svg[data-v-7684eaa4]{stroke:var(--black, #2e2b29)}.code-copy-button.copied[data-v-7684eaa4]{background-color:#22c55e1a;box-shadow:inset 0 1px 2px #0000000d}.code-copy-button.copied svg[data-v-7684eaa4]{stroke:var(--green, #22c55e)}.code-wrap-button.active[data-v-7684eaa4]{background-color:#4ba3fd1a;box-shadow:inset 0 1px 2px #0000000d}.code-wrap-button.active svg[data-v-7684eaa4]{stroke:var(--blue, #4ba3fd)}.code-block-container[data-v-6fd5c5d6]{position:relative;background-color:var(--creamy-white-1, #eeece4);border-radius:4px;padding:0;margin:1rem 0;overflow:hidden;font-family:Consolas,Source Code Pro,Courier New,monospace;font-size:.9rem;line-height:1.5;border:1px solid var(--creamy-white-3, #dcd8ca);box-shadow:0 2px 6px #0000000d;max-width:100%;width:100%}.code-block-container.editable-mode[data-v-6fd5c5d6],.code-block-container.editable-mode [data-v-6fd5c5d6] .n-scrollbar-content{pointer-events:auto}.code-block-container.editable-mode [data-v-6fd5c5d6] code[contenteditable=true]{pointer-events:auto;cursor:text}.code-block-container.editable-mode [data-v-6fd5c5d6] code[contenteditable=true]:focus{outline:none;box-shadow:none}.code-scrollbar-container[data-v-6fd5c5d6]{width:100%;max-width:100%;overflow:hidden;position:relative;min-height:3rem}[data-v-6fd5c5d6] .n-scrollbar{width:100%;height:auto;max-width:100%;box-sizing:border-box}[data-v-6fd5c5d6]:not(.code-wrap) .n-scrollbar-content{width:max-content;min-width:100%;max-width:none}.code-wrap[data-v-6fd5c5d6] .n-scrollbar-content{width:100%;max-width:100%}[data-v-6fd5c5d6] .n-scrollbar-rail--horizontal{height:8px;background-color:transparent;border-radius:4px;bottom:2px}[data-v-6fd5c5d6] .n-scrollbar-rail--horizontal.n-scrollbar-rail--disabled{display:block!important;opacity:1!important;pointer-events:auto!important}[data-v-6fd5c5d6] .n-scrollbar-thumb--horizontal{background-color:var(--gray-4, rgba(53, 38, 28, 40%));border-radius:4px;transition:background-color .2s ease;min-width:20px;height:6px}[data-v-6fd5c5d6] .n-scrollbar-thumb--horizontal:hover{background-color:var(--gray-5, rgba(28, 25, 23, 60%))}.code-wrap[data-v-6fd5c5d6] .n-scrollbar-rail--horizontal{display:none!important}.image-wrapper[data-v-9df24d95]{position:relative;display:inline-block;max-width:100%;box-sizing:border-box}.image-wrapper.resizing[data-v-9df24d95]{-webkit-user-select:none;user-select:none}.image-wrapper.resizing img[data-v-9df24d95]{pointer-events:none}.image-wrapper.resizing .resize-handle[data-v-9df24d95]{transition:none}.image-wrapper .resize-handle[data-v-9df24d95]{position:absolute;background:#2d8cf0;border:1px solid white;border-radius:50%;width:8px;height:8px;cursor:pointer;z-index:100;opacity:0;transition:opacity .2s ease}.image-wrapper .resize-handle.handle-top-left[data-v-9df24d95]{top:-4px;left:-4px;cursor:nw-resize}.image-wrapper .resize-handle.handle-top-right[data-v-9df24d95]{top:-4px;right:-4px;cursor:ne-resize}.image-wrapper .resize-handle.handle-bottom-left[data-v-9df24d95]{bottom:-4px;left:-4px;cursor:sw-resize}.image-wrapper .resize-handle.handle-bottom-right[data-v-9df24d95]{bottom:-4px;right:-4px;cursor:se-resize}.image-wrapper .resize-handle.handle-top[data-v-9df24d95]{top:-4px;left:calc(50% - 6px);cursor:n-resize;width:12px;height:4px;border-radius:2px}.image-wrapper .resize-handle.handle-right[data-v-9df24d95]{right:-4px;top:calc(50% - 6px);cursor:e-resize;width:4px;height:12px;border-radius:2px}.image-wrapper .resize-handle.handle-bottom[data-v-9df24d95]{bottom:-4px;left:calc(50% - 6px);cursor:s-resize;width:12px;height:4px;border-radius:2px}.image-wrapper .resize-handle.handle-left[data-v-9df24d95]{left:-4px;top:calc(50% - 6px);cursor:w-resize;width:4px;height:12px;border-radius:2px}.image-wrapper .resize-info[data-v-9df24d95]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#000000bf;color:#fff;padding:3px 8px;border-radius:4px;font-size:11px;font-weight:400;white-space:nowrap;z-index:101;pointer-events:none;box-shadow:0 1px 4px #0003;-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);line-height:1.2}.image-wrapper.ProseMirror-selectednode .resize-handle[data-v-9df24d95],.image-wrapper.resizing .resize-handle[data-v-9df24d95]{display:block!important;visibility:visible!important;opacity:1!important}.image-wrapper.readonly-image .resize-handle[data-v-9df24d95]{display:none!important;visibility:hidden!important;opacity:0!important}.mention-empty[data-v-9733f8bd]{display:flex;align-items:center;font-weight:500;font-size:.875rem;height:1.25rem;padding:0 .75rem;color:var(--text-muted, #6b7280)}.mention-button[data-v-9733f8bd]{-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;user-select:none;cursor:pointer;display:flex;align-items:center;justify-content:flex-start;line-height:1;font-weight:500;font-size:.875rem;border:none;outline:none;width:100%;height:1.9rem;border-radius:.375rem;background-color:transparent;color:var(--text-color, #374151);transition:all .15s ease}.mention-button[data-v-9733f8bd]:hover,.mention-button[data-v-9733f8bd]:focus,.mention-button[data-active][data-v-9733f8bd]{color:var(--text-active, #1f2937);background-color:var(--bg-hover, #f3f4f6)}.mention-button-icon[data-v-9733f8bd]{margin-right:.25rem;display:flex;align-items:center;justify-content:center}.mention-avatar[data-v-9733f8bd]{max-width:initial;min-width:initial;display:inline-block;margin:0;box-shadow:none;border-radius:50%;height:1.5rem;width:1.5rem;vertical-align:middle;transition:none;object-fit:cover;flex-shrink:0}.mention--avatar-fallback[data-v-9733f8bd]{background-color:var(--purple, #6a00f5);color:#fff;display:flex;align-items:center;justify-content:center;font-size:.65rem;font-weight:600}.mention-name[data-v-9733f8bd]{font-size:.9rem;color:var(--purple);font-weight:500;flex-shrink:0;flex-grow:1;text-align:start}:root{--tiptap-primary-color: #2d8cf0;--tiptap-primary-hover: #57a3f3;--tiptap-primary-active: #2b85e4;--tiptap-bg-primary: #fff;--tiptap-bg-secondary: #f8f9fa;--tiptap-bg-tertiary: #e9ecef;--tiptap-bg-code: var(--creamy-white-1, #eeece4);--tiptap-bg-code-dark: #2d3748;--tiptap-text-primary: #1a202c;--tiptap-text-secondary: #4a5568;--tiptap-text-muted: #718096;--tiptap-text-inverse: #fff;--tiptap-border-light: #e2e8f0;--tiptap-border-medium: #cbd5e0;--tiptap-border-dark: #a0aec0;--tiptap-border-code: var(--creamy-white-3, #dcd8ca);--tiptap-success: #48bb78;--tiptap-warning: #ed8936;--tiptap-error: #f56565;--tiptap-info: #4299e1;--tiptap-shadow-sm: 0 1px 3px rgba(0, 0, 0, 10%);--tiptap-shadow-md: 0 4px 6px rgba(0, 0, 0, 10%);--tiptap-shadow-lg: 0 10px 15px rgba(0, 0, 0, 10%);--tiptap-shadow-code: 0 2px 6px rgba(0, 0, 0, 5%);--tiptap-radius-sm: 2px;--tiptap-radius-md: 4px;--tiptap-radius-lg: 6px;--tiptap-radius-xl: 8px;--tiptap-spacing-xs: .25rem;--tiptap-spacing-sm: .5rem;--tiptap-spacing-md: .75rem;--tiptap-spacing-lg: 1rem;--tiptap-spacing-xl: 1.5rem;--tiptap-spacing-2xl: 2rem;--tiptap-font-family: -apple-system, blinkmacsystemfont, "Segoe UI", roboto, sans-serif;--tiptap-font-family-mono: consolas, "Source Code Pro", "Courier New", monospace;--tiptap-font-size-xs: .75rem;--tiptap-font-size-sm: .875rem;--tiptap-font-size-md: 1rem;--tiptap-font-size-lg: 1.125rem;--tiptap-font-size-xl: 1.25rem;--tiptap-line-height-tight: 1.25;--tiptap-line-height-normal: 1.5;--tiptap-line-height-relaxed: 1.75;--tiptap-transition-fast: .15s ease;--tiptap-transition-normal: .2s ease;--tiptap-transition-slow: .3s ease;--tiptap-z-dropdown: 1000;--tiptap-z-sticky: 1020;--tiptap-z-fixed: 1030;--tiptap-z-modal-backdrop: 1040;--tiptap-z-modal: 1050;--tiptap-z-popover: 1060;--tiptap-z-tooltip: 1070;--tiptap-image-border-selected: 2px solid var(--tiptap-primary-color);--tiptap-image-shadow-selected: 0 0 0 3px rgba(45, 140, 240, 20%);--tiptap-image-handle-size: 8px;--tiptap-image-handle-color: var(--tiptap-primary-color);--tiptap-code-bg: var(--tiptap-bg-code);--tiptap-code-border: var(--tiptap-border-code);--tiptap-code-font-size: .9rem;--tiptap-code-line-height: 1.5;--tiptap-code-padding: .8rem 1rem;--tiptap-toolbar-bg: var(--tiptap-bg-primary);--tiptap-toolbar-border: var(--tiptap-border-light);--tiptap-toolbar-button-size: 32px;--tiptap-toolbar-button-radius: var(--tiptap-radius-md);--tiptap-menu-bg: var(--tiptap-bg-primary);--tiptap-menu-border: var(--tiptap-border-light);--tiptap-menu-shadow: var(--tiptap-shadow-lg);--tiptap-menu-radius: var(--tiptap-radius-lg);--tiptap-scrollbar-size: 8px;--tiptap-scrollbar-track: transparent;--tiptap-scrollbar-thumb: var(--gray-4, rgba(53, 38, 28, 40%));--tiptap-scrollbar-thumb-hover: var(--gray-5, rgba(28, 25, 23, 60%))}[data-theme=dark]{--tiptap-bg-primary: #1a202c;--tiptap-bg-secondary: #2d3748;--tiptap-bg-tertiary: #4a5568;--tiptap-bg-code: #2d3748;--tiptap-text-primary: #f7fafc;--tiptap-text-secondary: #e2e8f0;--tiptap-text-muted: #a0aec0;--tiptap-border-light: #4a5568;--tiptap-border-medium: #718096;--tiptap-border-dark: #a0aec0;--tiptap-toolbar-bg: #2d3748;--tiptap-menu-bg: #2d3748}.tiptap-editor-wrapper .editor-content{background-color:inherit!important}.tiptap-editor-wrapper .editor-content .ProseMirrorNoneOutline{outline:none}.tiptap-editor-wrapper .editor-content .ProseMirrorInput{border:1px solid #b3b3b3!important;border-radius:4px;background-color:#fff;transition:all .3s ease}.tiptap-editor-wrapper .editor-content .ProseMirror:focus-within p.is-editor-empty:first-child:before{display:none}.tiptap-editor-wrapper .editor-content .ProseMirror{display:flex;flex-direction:column;width:100%;min-height:30px;font-size:16px;background-color:inherit!important}.dark-theme .user-comment-container-fixed .tiptap-editor-wrapper .editor-content .ProseMirror,.dark-theme .comment-flash .tiptap-editor-wrapper .editor-content .ProseMirror{background-color:var(--blue-light)!important}.tiptap-editor-wrapper .editor-content .ProseMirror:before{display:none}.tiptap-editor-wrapper .editor-content .ProseMirror ::selection{background-color:#2d8cf04d;color:inherit}.tiptap-editor-wrapper .editor-content .ProseMirror p{margin:.25rem .5rem;position:relative;transition:background-color .2s ease;line-height:1.5;min-height:1.5em}.tiptap-editor-wrapper .editor-content .ProseMirror p:focus,.tiptap-editor-wrapper .editor-content .ProseMirror p.has-focus{background-color:#5ad6960d;border-radius:4px}.tiptap-editor-wrapper .editor-content .ProseMirror p code{background-color:#f6f2ff;border-radius:.4rem;color:#181818;font-size:.85rem;padding:.25em .3em}.tiptap-editor-wrapper .editor-content .ProseMirror p a{color:#56a9ff;cursor:pointer}.tiptap-editor-wrapper .editor-content .ProseMirror p.is-editor-empty:first-child:before{color:#adb5bd;content:attr(data-placeholder);float:left;height:0;pointer-events:none}.tiptap-editor-wrapper .editor-content .ProseMirror h1,.tiptap-editor-wrapper .editor-content .ProseMirror h2,.tiptap-editor-wrapper .editor-content .ProseMirror h3,.tiptap-editor-wrapper .editor-content .ProseMirror h4,.tiptap-editor-wrapper .editor-content .ProseMirror h5,.tiptap-editor-wrapper .editor-content .ProseMirror h6{margin:.5rem}.tiptap-editor-wrapper .editor-content .ProseMirror blockquote{border-left:3px solid #d8d5d3;color:#868686;margin:.5rem;padding-left:1rem}.tiptap-editor-wrapper .editor-content .ProseMirror hr{border:none;border-top:1px solid #e7e4e2;cursor:pointer;margin:2rem 0}.tiptap-editor-wrapper .editor-content .ProseMirror hr.ProseMirror-selectednode{border-top:1px solid #e7e4e2}.tiptap-fullscreen{position:fixed;top:0;left:0;width:100vw!important;width:100dvw!important;height:100vh!important;height:100dvh!important;z-index:9999;background-color:inherit;color:inherit;padding:1rem;box-sizing:border-box;display:flex;flex-direction:column}.tiptap-fullscreen :deep(.editor-content){flex-grow:1;overflow-y:auto;max-height:calc(100vh - 6rem);max-height:calc(100dvh - 6rem);background-color:inherit}.tiptap-fullscreen :deep(.editor-toolbar){background-color:inherit!important}.ProseMirror-focused{outline:none}.ProseMirror-focused p:focus-visible,.ProseMirror-focused li:focus-visible,.ProseMirror-focused h1:focus-visible,.ProseMirror-focused h2:focus-visible,.ProseMirror-focused h3:focus-visible,.ProseMirror-focused h4:focus-visible,.ProseMirror-focused h5:focus-visible,.ProseMirror-focused h6:focus-visible{background-color:#5ad6960d;border-radius:4px;outline:none}.ProseMirror p,.ProseMirror li,.ProseMirror blockquote,.ProseMirror h1,.ProseMirror h2,.ProseMirror h3,.ProseMirror h4,.ProseMirror h5,.ProseMirror h6{background-color:inherit!important}.ProseMirror p:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror li:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror blockquote:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h1:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h2:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h3:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h4:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h5:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h6:focus-within:not(:has(.ProseMirror-selectednode)){position:relative;border-radius:4px;transition:background-color .2s ease}.ProseMirror p:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror li:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror blockquote:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror h1:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror h2:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror h3:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror h4:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror h5:focus-within:not(:has(.ProseMirror-selectednode)):after,.ProseMirror h6:focus-within:not(:has(.ProseMirror-selectednode)):after{content:"";position:absolute;left:-5px;top:0;height:100%;width:3px;background-color:#5ad69680;border-radius:2px;opacity:0;transition:opacity .2s ease}.ProseMirror p:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror li:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror blockquote:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror h1:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror h2:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror h3:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror h4:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror h5:focus-within:not(:has(.ProseMirror-selectednode)):hover:after,.ProseMirror h6:focus-within:not(:has(.ProseMirror-selectednode)):hover:after{opacity:1}@supports not (selector(:has(*))){.ProseMirror p:focus-within,.ProseMirror li:focus-within,.ProseMirror blockquote:focus-within,.ProseMirror h1:focus-within,.ProseMirror h2:focus-within,.ProseMirror h3:focus-within,.ProseMirror h4:focus-within,.ProseMirror h5:focus-within,.ProseMirror h6:focus-within{background-color:#5ad6960d;border-radius:4px}}.editor-readonly .ProseMirrorInput,.editor-readonly .ProseMirror{-webkit-user-select:text;user-select:text}.editor-readonly .ProseMirrorInput .ProseMirror-selectednode,.editor-readonly .ProseMirror .ProseMirror-selectednode,.editor-readonly .ProseMirrorInput p.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput li.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput div.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h1.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h2.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h3.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h4.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h5.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h6.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput blockquote.ProseMirror-selectednode,.editor-readonly .ProseMirror p.ProseMirror-selectednode,.editor-readonly .ProseMirror li.ProseMirror-selectednode,.editor-readonly .ProseMirror div.ProseMirror-selectednode,.editor-readonly .ProseMirror h1.ProseMirror-selectednode,.editor-readonly .ProseMirror h2.ProseMirror-selectednode,.editor-readonly .ProseMirror h3.ProseMirror-selectednode,.editor-readonly .ProseMirror h4.ProseMirror-selectednode,.editor-readonly .ProseMirror h5.ProseMirror-selectednode,.editor-readonly .ProseMirror h6.ProseMirror-selectednode,.editor-readonly .ProseMirror blockquote.ProseMirror-selectednode{outline:none;border:none;box-shadow:none;animation:none;transition:none}.editor-readonly .ProseMirrorInput p .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput p .image-wrapper:hover,.editor-readonly .ProseMirrorInput p .image-wrapper:focus,.editor-readonly .ProseMirrorInput p .image-wrapper:active,.editor-readonly .ProseMirrorInput p img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput p img:hover,.editor-readonly .ProseMirrorInput p img:focus,.editor-readonly .ProseMirrorInput p img:active,.editor-readonly .ProseMirrorInput li .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput li .image-wrapper:hover,.editor-readonly .ProseMirrorInput li .image-wrapper:focus,.editor-readonly .ProseMirrorInput li .image-wrapper:active,.editor-readonly .ProseMirrorInput li img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput li img:hover,.editor-readonly .ProseMirrorInput li img:focus,.editor-readonly .ProseMirrorInput li img:active,.editor-readonly .ProseMirrorInput div .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput div .image-wrapper:hover,.editor-readonly .ProseMirrorInput div .image-wrapper:focus,.editor-readonly .ProseMirrorInput div .image-wrapper:active,.editor-readonly .ProseMirrorInput div img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput div img:hover,.editor-readonly .ProseMirrorInput div img:focus,.editor-readonly .ProseMirrorInput div img:active,.editor-readonly .ProseMirrorInput h1 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h1 .image-wrapper:hover,.editor-readonly .ProseMirrorInput h1 .image-wrapper:focus,.editor-readonly .ProseMirrorInput h1 .image-wrapper:active,.editor-readonly .ProseMirrorInput h1 img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h1 img:hover,.editor-readonly .ProseMirrorInput h1 img:focus,.editor-readonly .ProseMirrorInput h1 img:active,.editor-readonly .ProseMirrorInput h2 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h2 .image-wrapper:hover,.editor-readonly .ProseMirrorInput h2 .image-wrapper:focus,.editor-readonly .ProseMirrorInput h2 .image-wrapper:active,.editor-readonly .ProseMirrorInput h2 img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h2 img:hover,.editor-readonly .ProseMirrorInput h2 img:focus,.editor-readonly .ProseMirrorInput h2 img:active,.editor-readonly .ProseMirrorInput h3 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h3 .image-wrapper:hover,.editor-readonly .ProseMirrorInput h3 .image-wrapper:focus,.editor-readonly .ProseMirrorInput h3 .image-wrapper:active,.editor-readonly .ProseMirrorInput h3 img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h3 img:hover,.editor-readonly .ProseMirrorInput h3 img:focus,.editor-readonly .ProseMirrorInput h3 img:active,.editor-readonly .ProseMirrorInput h4 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h4 .image-wrapper:hover,.editor-readonly .ProseMirrorInput h4 .image-wrapper:focus,.editor-readonly .ProseMirrorInput h4 .image-wrapper:active,.editor-readonly .ProseMirrorInput h4 img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h4 img:hover,.editor-readonly .ProseMirrorInput h4 img:focus,.editor-readonly .ProseMirrorInput h4 img:active,.editor-readonly .ProseMirrorInput h5 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h5 .image-wrapper:hover,.editor-readonly .ProseMirrorInput h5 .image-wrapper:focus,.editor-readonly .ProseMirrorInput h5 .image-wrapper:active,.editor-readonly .ProseMirrorInput h5 img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h5 img:hover,.editor-readonly .ProseMirrorInput h5 img:focus,.editor-readonly .ProseMirrorInput h5 img:active,.editor-readonly .ProseMirrorInput h6 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h6 .image-wrapper:hover,.editor-readonly .ProseMirrorInput h6 .image-wrapper:focus,.editor-readonly .ProseMirrorInput h6 .image-wrapper:active,.editor-readonly .ProseMirrorInput h6 img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput h6 img:hover,.editor-readonly .ProseMirrorInput h6 img:focus,.editor-readonly .ProseMirrorInput h6 img:active,.editor-readonly .ProseMirrorInput blockquote .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput blockquote .image-wrapper:hover,.editor-readonly .ProseMirrorInput blockquote .image-wrapper:focus,.editor-readonly .ProseMirrorInput blockquote .image-wrapper:active,.editor-readonly .ProseMirrorInput blockquote img.ProseMirror-selectednode,.editor-readonly .ProseMirrorInput blockquote img:hover,.editor-readonly .ProseMirrorInput blockquote img:focus,.editor-readonly .ProseMirrorInput blockquote img:active,.editor-readonly .ProseMirror p .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror p .image-wrapper:hover,.editor-readonly .ProseMirror p .image-wrapper:focus,.editor-readonly .ProseMirror p .image-wrapper:active,.editor-readonly .ProseMirror p img.ProseMirror-selectednode,.editor-readonly .ProseMirror p img:hover,.editor-readonly .ProseMirror p img:focus,.editor-readonly .ProseMirror p img:active,.editor-readonly .ProseMirror li .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror li .image-wrapper:hover,.editor-readonly .ProseMirror li .image-wrapper:focus,.editor-readonly .ProseMirror li .image-wrapper:active,.editor-readonly .ProseMirror li img.ProseMirror-selectednode,.editor-readonly .ProseMirror li img:hover,.editor-readonly .ProseMirror li img:focus,.editor-readonly .ProseMirror li img:active,.editor-readonly .ProseMirror div .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror div .image-wrapper:hover,.editor-readonly .ProseMirror div .image-wrapper:focus,.editor-readonly .ProseMirror div .image-wrapper:active,.editor-readonly .ProseMirror div img.ProseMirror-selectednode,.editor-readonly .ProseMirror div img:hover,.editor-readonly .ProseMirror div img:focus,.editor-readonly .ProseMirror div img:active,.editor-readonly .ProseMirror h1 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror h1 .image-wrapper:hover,.editor-readonly .ProseMirror h1 .image-wrapper:focus,.editor-readonly .ProseMirror h1 .image-wrapper:active,.editor-readonly .ProseMirror h1 img.ProseMirror-selectednode,.editor-readonly .ProseMirror h1 img:hover,.editor-readonly .ProseMirror h1 img:focus,.editor-readonly .ProseMirror h1 img:active,.editor-readonly .ProseMirror h2 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror h2 .image-wrapper:hover,.editor-readonly .ProseMirror h2 .image-wrapper:focus,.editor-readonly .ProseMirror h2 .image-wrapper:active,.editor-readonly .ProseMirror h2 img.ProseMirror-selectednode,.editor-readonly .ProseMirror h2 img:hover,.editor-readonly .ProseMirror h2 img:focus,.editor-readonly .ProseMirror h2 img:active,.editor-readonly .ProseMirror h3 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror h3 .image-wrapper:hover,.editor-readonly .ProseMirror h3 .image-wrapper:focus,.editor-readonly .ProseMirror h3 .image-wrapper:active,.editor-readonly .ProseMirror h3 img.ProseMirror-selectednode,.editor-readonly .ProseMirror h3 img:hover,.editor-readonly .ProseMirror h3 img:focus,.editor-readonly .ProseMirror h3 img:active,.editor-readonly .ProseMirror h4 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror h4 .image-wrapper:hover,.editor-readonly .ProseMirror h4 .image-wrapper:focus,.editor-readonly .ProseMirror h4 .image-wrapper:active,.editor-readonly .ProseMirror h4 img.ProseMirror-selectednode,.editor-readonly .ProseMirror h4 img:hover,.editor-readonly .ProseMirror h4 img:focus,.editor-readonly .ProseMirror h4 img:active,.editor-readonly .ProseMirror h5 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror h5 .image-wrapper:hover,.editor-readonly .ProseMirror h5 .image-wrapper:focus,.editor-readonly .ProseMirror h5 .image-wrapper:active,.editor-readonly .ProseMirror h5 img.ProseMirror-selectednode,.editor-readonly .ProseMirror h5 img:hover,.editor-readonly .ProseMirror h5 img:focus,.editor-readonly .ProseMirror h5 img:active,.editor-readonly .ProseMirror h6 .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror h6 .image-wrapper:hover,.editor-readonly .ProseMirror h6 .image-wrapper:focus,.editor-readonly .ProseMirror h6 .image-wrapper:active,.editor-readonly .ProseMirror h6 img.ProseMirror-selectednode,.editor-readonly .ProseMirror h6 img:hover,.editor-readonly .ProseMirror h6 img:focus,.editor-readonly .ProseMirror h6 img:active,.editor-readonly .ProseMirror blockquote .image-wrapper.ProseMirror-selectednode,.editor-readonly .ProseMirror blockquote .image-wrapper:hover,.editor-readonly .ProseMirror blockquote .image-wrapper:focus,.editor-readonly .ProseMirror blockquote .image-wrapper:active,.editor-readonly .ProseMirror blockquote img.ProseMirror-selectednode,.editor-readonly .ProseMirror blockquote img:hover,.editor-readonly .ProseMirror blockquote img:focus,.editor-readonly .ProseMirror blockquote img:active{outline:none;border:none;box-shadow:none}@keyframes office-cursor-blink{0%,to{opacity:1}50%{opacity:0}}@keyframes office-cursor-appear{0%{opacity:0;height:0}to{opacity:1;height:1.2em}}.ProseMirror{caret-color:#2d8cf0;position:relative}.ProseMirror .ProseMirror-gapcursor{display:none;pointer-events:none;position:absolute}.ProseMirror .ProseMirror-gapcursor:after{content:"";display:block;position:absolute;width:3px;height:1.2em;background-color:#2d8cf0;border-radius:0;animation:office-cursor-blink 1s ease-in-out infinite;box-shadow:0 0 3px #2d8cf080}.ProseMirror p:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h1:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h2:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h3:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h4:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h5:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror h6:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror li:focus-within:not(:has(.ProseMirror-selectednode)),.ProseMirror blockquote:focus-within:not(:has(.ProseMirror-selectednode)){position:relative}.ProseMirror p:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror h1:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror h2:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror h3:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror h4:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror h5:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror h6:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror li:focus-within:not(:has(.ProseMirror-selectednode)):before,.ProseMirror blockquote:focus-within:not(:has(.ProseMirror-selectednode)):before{display:none}.ProseMirror .has-cursor-animation{animation:text-appear .15s ease-out forwards}@keyframes text-appear{0%{opacity:.7;transform:translateY(1px)}to{opacity:1;transform:translateY(0)}}.ProseMirror-focused .ProseMirror-gapcursor{display:block;animation:office-cursor-appear .2s ease-out forwards}@supports (-moz-appearance: none){.ProseMirror-focused{caret-color:#2d8cf0}}@supports (-webkit-appearance: none){.ProseMirror-focused{caret-color:#2d8cf0}}.ProseMirror ul,.ProseMirror ol{margin:.5rem}.ProseMirror ul li,.ProseMirror ol li{transition:background-color .2s ease}.ProseMirror ul li:focus-within,.ProseMirror ol li:focus-within{background-color:#5ad6960d;border-radius:4px}.ProseMirror ul li p,.ProseMirror ol li p{margin-top:.15em;margin-bottom:.15em;position:relative}.ProseMirror ul{padding:0 1rem;list-style-position:outside;list-style-type:disc}.ProseMirror ul ul{list-style-type:circle}.ProseMirror ul ul ul{list-style-type:square}.ProseMirror ul ul ul ul{list-style-type:disc}.ProseMirror ul ul ul ul ul{list-style-type:circle}.ProseMirror ul ul ul ul ul ul{list-style-type:square}.ProseMirror ol{padding-left:1.25rem;list-style-position:outside;list-style-type:decimal}.ProseMirror ol ol{padding-left:1.25rem;list-style-type:lower-alpha}.ProseMirror ol ol ol{list-style-type:lower-roman}.ProseMirror ol ol ol ol{list-style-type:upper-alpha}.ProseMirror ol ol ol ol ol{list-style-type:upper-roman}.ProseMirror ol ol ol ol ol ol{list-style-type:decimal}.ProseMirror ul[data-type=taskList]{list-style:none;margin-left:0;padding:0}.ProseMirror ul[data-type=taskList] li{align-items:flex-start;display:flex;position:relative;padding:2px 4px;border-radius:4px;transition:background-color .2s ease,box-shadow .2s ease}.ProseMirror ul[data-type=taskList] li:focus-within{background-color:#5ad6960d;box-shadow:0 0 0 1px #5ad69633}.ProseMirror ul[data-type=taskList] li>label{flex:0 0 auto;-webkit-user-select:none;user-select:none}.ProseMirror ul[data-type=taskList] li>div{flex:1 1 auto;position:relative}.ProseMirror ul[data-type=taskList] input[type=checkbox]{cursor:pointer;position:relative}.ProseMirror ul[data-type=taskList] input[type=checkbox]:checked{accent-color:#2d8cf0}.ProseMirror ul[data-type=taskList] ul[data-type=taskList]{margin:0}.editor-readonly .ProseMirrorInput ul[data-type=taskList] input[type=checkbox],.editor-readonly .ProseMirror ul[data-type=taskList] input[type=checkbox]{pointer-events:none;box-shadow:none;outline:none}.editor-readonly .ProseMirrorInput ul[data-type=taskList] .cst-task-checkbox-wrapper,.editor-readonly .ProseMirror ul[data-type=taskList] .cst-task-checkbox-wrapper{pointer-events:none;cursor:not-allowed;transition:none!important;transform:none!important}.editor-readonly .ProseMirrorInput ul[data-type=taskList] .cst-task-checkbox-wrapper:hover,.editor-readonly .ProseMirror ul[data-type=taskList] .cst-task-checkbox-wrapper:hover{transform:none!important}.editor-readonly .ProseMirrorInput ul[data-type=taskList]>label,.editor-readonly .ProseMirror ul[data-type=taskList]>label{transition:none!important}.editor-readonly .ProseMirrorInput ul[data-type=taskList]>label:hover,.editor-readonly .ProseMirror ul[data-type=taskList]>label:hover{transform:none!important}.ProseMirror p code{background-color:var(--creamy-white-1, #eeece4);border-radius:.3rem;color:var(--code-text, var(--black, #2e2b29));font-family:Consolas,Source Code Pro,Courier New,monospace;font-size:.85rem;padding:.2em .4em;border:1px solid var(--creamy-white-3, #dcd8ca)}@keyframes upload-success{0%{opacity:0;transform:scale(.9)}30%{opacity:.7;transform:scale(1)}to{opacity:0;transform:scale(1.05)}}@keyframes selected-node-pulse{0%{box-shadow:0 0 0 1px #2d8cf080,0 0 5px #2d8cf033}50%{box-shadow:0 0 0 1px #2d8cf080,0 0 10px #2d8cf066}to{box-shadow:0 0 0 1px #2d8cf080,0 0 5px #2d8cf033}}.ProseMirror-selectednode:not(.bilibili-container){transition:outline .3s ease,box-shadow .3s ease;box-shadow:0 0 0 1px #2d8cf080,0 0 8px #2d8cf04d}.image-wrapper.ProseMirror-selectednode .resize-handle{opacity:1;transition:transform .2s ease}.editor-bubble-menu{background-color:var(--bg-color, white);border:1px solid var(--border-color, #e5e7eb);border-radius:.5rem;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -2px #0000001a;display:flex;flex-wrap:wrap;z-index:100;max-width:min(16rem,100%);transform-origin:center bottom;animation:slide-up-fade-in .15s ease-out}.editor-floating-menu{background-color:var(--bg-color, white);border:1px solid var(--border-color, #e5e7eb);border-radius:.5rem;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -2px #0000001a;display:flex;z-index:100;transform-origin:top center;animation:slide-down-fade-in .15s ease-out}.tiptap-editor-wrapper .editor-toolbar{display:flex;align-items:center;flex-wrap:wrap;margin-bottom:4px;padding:4px 0;position:sticky;top:0;z-index:100;width:100%}.tiptap-editor-wrapper .editor-toolbar-bgc{background-color:#fff}.tiptap-editor-wrapper.tiptap-fullscreen .editor-toolbar-bgc{background-color:inherit!important}.dark-theme .editor-bubble-menu,.dark-theme .editor-floating-menu{--bg-color: #1f2937;--border-color: #374151;background-color:var(--bg-color);border:1px solid var(--border-color);box-shadow:0 4px 6px -1px #0000004d,0 2px 4px -2px #0003}.dark-theme .tiptap-editor-wrapper .editor-toolbar-bgc{background-color:var(--white-2)}.dark-theme .tiptap-editor-wrapper.tiptap-fullscreen .editor-toolbar-bgc{background-color:var(--white-2)!important}.ProseMirror.ProseMirror p>img,.ProseMirror.ProseMirror p>.image-wrapper{max-width:80%;min-width:5rem;margin:0;padding:0;display:inline-block;width:auto;border-radius:.25rem;position:relative;text-align:center;vertical-align:middle;transform:translateY(0);transition:none;line-height:normal;will-change:transform;white-space:nowrap;font-size:inherit}.ProseMirror.ProseMirror p>img img,.ProseMirror.ProseMirror p>.image-wrapper img{height:auto;border-radius:.25rem;box-shadow:0 2px 4px #0000001a;position:relative;z-index:1;display:block;margin:0;padding:0;transform:translateY(0);transition:none;will-change:transform;line-height:normal;font-size:inherit}.ProseMirror.ProseMirror p>img img:hover,.ProseMirror.ProseMirror p>.image-wrapper img:hover{box-shadow:0 4px 8px #00000026}.ProseMirror.ProseMirror p>img.ProseMirror-selectednode,.ProseMirror.ProseMirror p>.image-wrapper.ProseMirror-selectednode{outline:3px solid var(--blue);outline-offset:0;border:none;box-shadow:0 0 0 1px #2d8cf080,0 0 12px #2d8cf04d;z-index:2}.ProseMirror.ProseMirror p>img .resize-handle,.ProseMirror.ProseMirror p>.image-wrapper .resize-handle{display:none;opacity:0;transition:opacity .2s ease,transform .2s ease}.ProseMirror.ProseMirror p>img.ProseMirror-selectednode .resize-handle,.ProseMirror.ProseMirror p>.image-wrapper.ProseMirror-selectednode .resize-handle{display:block;opacity:1;animation:handle-fade-in .2s ease forwards}@keyframes handle-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@media (width <= 768px){.ProseMirror.ProseMirror p>img,.ProseMirror.ProseMirror p>.image-wrapper{max-width:100%;min-width:unset}.ProseMirror.ProseMirror p>img img,.ProseMirror.ProseMirror p>.image-wrapper img{max-width:100%;width:auto;object-fit:contain}}.ProseMirror.ProseMirror p:only-child>img,.ProseMirror.ProseMirror p:only-child>.image-wrapper{position:relative;display:inline-block}.ProseMirror.ProseMirror p:only-child>img.ProseMirror-selectednode,.ProseMirror.ProseMirror p:only-child>.image-wrapper.ProseMirror-selectednode{outline:2px solid #2d8cf0}.ProseMirror.ProseMirror p:only-child>img.ProseMirror-selectednode .resize-handle,.ProseMirror.ProseMirror p:only-child>.image-wrapper.ProseMirror-selectednode .resize-handle{display:block;position:absolute;z-index:200}.ProseMirror.ProseMirror ul li p img,.ProseMirror.ProseMirror ul li p .image-wrapper,.ProseMirror.ProseMirror ol li p img,.ProseMirror.ProseMirror ol li p .image-wrapper{max-width:100%;min-width:5rem;margin:0;display:inline-block;border-radius:.25rem;vertical-align:middle;position:relative;transition:outline .2s ease,box-shadow .2s ease}.ProseMirror.ProseMirror ul li p img.ProseMirror-selectednode,.ProseMirror.ProseMirror ul li p .image-wrapper.ProseMirror-selectednode,.ProseMirror.ProseMirror ol li p img.ProseMirror-selectednode,.ProseMirror.ProseMirror ol li p .image-wrapper.ProseMirror-selectednode{outline:2px solid #2d8cf0;outline-offset:0;box-shadow:0 0 0 1px #2d8cf080,0 0 8px #2d8cf04d;transform:translateZ(0)}.ProseMirror.ProseMirror ul li p img.ProseMirror-selectednode .resize-handle,.ProseMirror.ProseMirror ul li p .image-wrapper.ProseMirror-selectednode .resize-handle,.ProseMirror.ProseMirror ol li p img.ProseMirror-selectednode .resize-handle,.ProseMirror.ProseMirror ol li p .image-wrapper.ProseMirror-selectednode .resize-handle{display:block;position:absolute;z-index:200;animation:handle-appear .2s ease forwards}@keyframes handle-appear{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}.ProseMirror.ProseMirror ul[data-type=taskList] li>div img,.ProseMirror.ProseMirror ul[data-type=taskList] li>div .image-wrapper{max-width:100%;min-width:5rem;margin:0;display:inline-block;border-radius:.25rem;vertical-align:middle;position:relative;transition:all .2s ease}.ProseMirror.ProseMirror ul[data-type=taskList] li>div img.ProseMirror-selectednode,.ProseMirror.ProseMirror ul[data-type=taskList] li>div .image-wrapper.ProseMirror-selectednode{outline:2px solid #2d8cf0;outline-offset:0;box-shadow:0 0 0 1px #2d8cf080,0 0 8px #2d8cf04d;transform:translateZ(0)}.ProseMirror.ProseMirror ul[data-type=taskList] li>div img.ProseMirror-selectednode .resize-handle,.ProseMirror.ProseMirror ul[data-type=taskList] li>div .image-wrapper.ProseMirror-selectednode .resize-handle{display:block;position:absolute;z-index:200;animation:resize-handle-in .2s ease forwards}@keyframes resize-handle-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}.ProseMirror.ProseMirror .image-wrapper+.ProseMirror-separator,.ProseMirror.ProseMirror img+.ProseMirror-trailingBreak{visibility:hidden}.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper{outline:none!important;border:none!important;box-shadow:none!important;cursor:pointer}.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode .resize-handle,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] .resize-handle,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode .resize-handle,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] .resize-handle,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper .resize-handle[class*=handle-],.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper .resize-handle,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper .resize-handle[class*=handle-]{display:none!important;visibility:hidden!important;pointer-events:none!important;opacity:0!important}.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper:hover{outline:none!important;border:none!important;box-shadow:none!important}.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img[class*=ProseMirror]{outline:none!important;border:none!important;box-shadow:none!important;cursor:pointer}.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput .image-wrapper[class*=ProseMirror] img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput div>.image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput li>.image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror .image-wrapper[class*=ProseMirror] img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>.image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>.image-wrapper img[class*=ProseMirror]:active{outline:none!important;border:none!important;box-shadow:none!important}.editor-readonly.editor-readonly .ProseMirrorInput>img,.editor-readonly.editor-readonly .ProseMirrorInput p>img,.editor-readonly.editor-readonly .ProseMirrorInput li>img,.editor-readonly.editor-readonly .ProseMirrorInput div>img,.editor-readonly.editor-readonly .ProseMirrorInput img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirrorInput img[class*=ProseMirror],.editor-readonly.editor-readonly .ProseMirror.ProseMirror>img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>img,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img.ProseMirror-selectednode,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img[class*=ProseMirror]{outline:none!important;border:none!important;box-shadow:none!important;cursor:pointer}.editor-readonly.editor-readonly .ProseMirrorInput>img:hover,.editor-readonly.editor-readonly .ProseMirrorInput>img:focus,.editor-readonly.editor-readonly .ProseMirrorInput>img:active,.editor-readonly.editor-readonly .ProseMirrorInput p>img:hover,.editor-readonly.editor-readonly .ProseMirrorInput p>img:focus,.editor-readonly.editor-readonly .ProseMirrorInput p>img:active,.editor-readonly.editor-readonly .ProseMirrorInput li>img:hover,.editor-readonly.editor-readonly .ProseMirrorInput li>img:focus,.editor-readonly.editor-readonly .ProseMirrorInput li>img:active,.editor-readonly.editor-readonly .ProseMirrorInput div>img:hover,.editor-readonly.editor-readonly .ProseMirrorInput div>img:focus,.editor-readonly.editor-readonly .ProseMirrorInput div>img:active,.editor-readonly.editor-readonly .ProseMirrorInput img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirrorInput img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirrorInput img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirrorInput img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirrorInput img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirrorInput img[class*=ProseMirror]:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror>img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror>img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror>img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror li>img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>img:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror div>img:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img.ProseMirror-selectednode:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img.ProseMirror-selectednode:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img.ProseMirror-selectednode:active,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img[class*=ProseMirror]:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img[class*=ProseMirror]:focus,.editor-readonly.editor-readonly .ProseMirror.ProseMirror img[class*=ProseMirror]:active{outline:none!important;border:none!important;box-shadow:none!important}.editor-readonly.editor-readonly .ProseMirrorInput p>img:hover,.editor-readonly.editor-readonly .ProseMirrorInput p>.image-wrapper img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>img:hover,.editor-readonly.editor-readonly .ProseMirror.ProseMirror p>.image-wrapper img:hover{box-shadow:none!important}@media (width <= 768px){.ProseMirror.ProseMirror .image-wrapper.ProseMirror-selectednode{outline-width:2px}.ProseMirror.ProseMirror p>img,.ProseMirror.ProseMirror p>.image-wrapper,.ProseMirror.ProseMirror li>img,.ProseMirror.ProseMirror li>.image-wrapper,.ProseMirror.ProseMirror div>img,.ProseMirror.ProseMirror div>.image-wrapper{max-width:100%;min-width:unset;margin:0;padding:0}.ProseMirror.ProseMirror p>img img,.ProseMirror.ProseMirror p>.image-wrapper img,.ProseMirror.ProseMirror li>img img,.ProseMirror.ProseMirror li>.image-wrapper img,.ProseMirror.ProseMirror div>img img,.ProseMirror.ProseMirror div>.image-wrapper img{max-width:100%;margin:0;padding:0;width:auto;object-fit:contain}.modal-overlay{padding:1rem}.modal-overlay img{max-width:100%}.loading-spinner{width:2.5rem;height:2.5rem}}.image-wrapper{position:relative;display:inline-block;max-width:100%;box-sizing:border-box}.image-wrapper.resizing{-webkit-user-select:none;user-select:none}.image-wrapper.resizing img{pointer-events:none}.image-wrapper.resizing .resize-handle{transition:none}.image-wrapper .resize-handle{position:absolute;background:#2d8cf0;border:1px solid white;border-radius:50%;width:8px;height:8px;cursor:pointer;z-index:100;opacity:0;transition:opacity .2s ease}.image-wrapper .resize-handle.handle-top-left{top:-4px;left:-4px;cursor:nw-resize}.image-wrapper .resize-handle.handle-top-right{top:-4px;right:-4px;cursor:ne-resize}.image-wrapper .resize-handle.handle-bottom-left{bottom:-4px;left:-4px;cursor:sw-resize}.image-wrapper .resize-handle.handle-bottom-right{bottom:-4px;right:-4px;cursor:se-resize}.image-wrapper .resize-handle.handle-top{top:-4px;left:calc(50% - 6px);cursor:n-resize;width:12px;height:4px;border-radius:2px}.image-wrapper .resize-handle.handle-right{right:-4px;top:calc(50% - 6px);cursor:e-resize;width:4px;height:12px;border-radius:2px}.image-wrapper .resize-handle.handle-bottom{bottom:-4px;left:calc(50% - 6px);cursor:s-resize;width:12px;height:4px;border-radius:2px}.image-wrapper .resize-handle.handle-left{left:-4px;top:calc(50% - 6px);cursor:w-resize;width:4px;height:12px;border-radius:2px}.image-wrapper .resize-info{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#000000bf;color:#fff;padding:3px 8px;border-radius:4px;font-size:11px;font-weight:400;white-space:nowrap;z-index:101;pointer-events:none;box-shadow:0 1px 4px #0003;-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);line-height:1.2}.mention-menu{white-space:nowrap;pointer-events:all;max-height:15rem;padding:.25rem;overflow:hidden auto;border:1px solid var(--border-color, #e5e7eb);border-radius:.5rem;background-color:var(--bg-color, white);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -2px #0000001a;z-index:10000}.mention-menu.mention-menu-above{transform-origin:bottom center;animation:slide-up-fade-in .15s ease-out}.mention-menu.mention-menu-below{transform-origin:top center;animation:slide-down-fade-in .15s ease-out}.mention-menu-empty{display:flex;align-items:center;font-weight:500;font-size:.875rem;height:2.25rem;padding:0 .75rem;color:var(--text-muted, #6b7280)}.mention-menu-button{-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;user-select:none;cursor:pointer;display:flex;align-items:center;justify-content:flex-start;line-height:1;font-weight:500;font-size:.875rem;border:none;outline:none;width:100%;height:2rem;padding:.25rem .5rem;border-radius:.375rem;background-color:transparent;color:var(--text-color, #374151);transition:all .15s ease}.mention-menu-button:hover,.mention-menu-button:focus,.mention-menu-button[data-active]{color:var(--text-active, #1f2937);background-color:var(--bg-hover, #f3f4f6)}.mention-menu-button-icon{margin-left:.25rem;margin-right:.75rem;width:1.5rem;height:1.5rem;display:flex;align-items:center;justify-content:center}.mention-menu-button-icon img{width:1.5rem;height:1.5rem;border-radius:50%;object-fit:cover}.mention-menu-button-icon .avatar-fallback{width:1.5rem;height:1.5rem;border-radius:50%;background-color:var(--purple, #6a00f5);color:#fff;display:flex;align-items:center;justify-content:center;font-size:.75rem;font-weight:600}.mention-menu-button-name{flex-grow:1;text-align:start;color:var(--purple, #6a00f5);font-weight:600}.dark-theme .mention-menu{--bg-color: #1f2937;--border-color: #374151;--text-color: #f9fafb;--text-active: #fff;--text-muted: #9ca3af;--bg-hover: #374151;box-shadow:0 4px 6px -1px #0000004d,0 2px 4px -2px #0003}.mention-menu::-webkit-scrollbar{width:5px}.mention-menu::-webkit-scrollbar-track{background:transparent}.mention-menu::-webkit-scrollbar-thumb{background:var(--border-color, #e5e7eb);border-radius:5px}.mention-menu *{scrollbar-width:thin;scrollbar-color:var(--border-color, #e5e7eb) transparent}.ProseMirror .mention,.ProseMirror span[data-type=mention],.mention,span[data-type=mention]{display:inline-flex;align-items:center;background:var(--gray-2);border-radius:.2rem;padding:.15rem .3rem;margin:0 .1rem;gap:.3rem;text-decoration:none;cursor:pointer;transition:background-color .2s ease;white-space:nowrap}.mention:hover,span[data-type=mention]:hover{background:var(--gray-3)}.ProseMirror .mention img,.ProseMirror .mention .mention-avatar,.ProseMirror span[data-type=mention] img,.ProseMirror span[data-type=mention] .mention-avatar,.mention img,.mention .mention-avatar,span[data-type=mention] img,span[data-type=mention] .mention-avatar{max-width:initial;min-width:initial;display:inline-block;margin:0;box-shadow:none;border-radius:50%;height:1.25rem;width:1.25rem;vertical-align:middle;transition:none;order:2;object-fit:cover;flex-shrink:0}.mention img:hover,.mention .mention-avatar:hover,span[data-type=mention] img:hover,span[data-type=mention] .mention-avatar:hover{transform:none}.ProseMirror .mention .mention-name,.ProseMirror span[data-type=mention] .mention-name,.mention .mention-name,span[data-type=mention] .mention-name{font-size:.85rem;color:var(--purple);order:1;font-weight:500;flex-shrink:0}span[data-type=mention]:empty:before{content:"@" attr(data-label);font-size:.85rem;color:var(--purple);font-weight:500}span[data-type=mention][contenteditable=false]{-webkit-user-select:none;user-select:none}.dropdown-menu{background-color:#fff;border-radius:.5rem;box-shadow:0 0 0 1px #0000000d,0 2px 8px #0000001a;overflow:hidden;padding:.25rem;max-height:15rem;overflow-y:auto}.dropdown-menu button{background:none;border:none;display:flex;align-items:center;border-radius:.25rem;gap:.5rem;width:100%;text-align:left;cursor:pointer;transition:background-color .2s}.dropdown-menu button:hover,.dropdown-menu button.is-selected{background-color:#0000000d}.dropdown-menu button .dropdown-avatar{width:1.5rem;height:1.5rem;border-radius:50%;object-fit:cover}.dropdown-menu .item{padding:.25rem;color:#888}.dark-theme .dropdown-menu{background-color:var(--white-2);box-shadow:0 0 0 1px #ffffff0d,0 2px 8px #0000004d}.dark-theme .dropdown-menu button:hover,.dark-theme .dropdown-menu button.is-selected{background-color:#ffffff1a}.dark-theme .dropdown-menu .item{color:#aaa}.bilibili-container{position:relative;width:100%;margin:1rem 0}.bilibili-wrapper{position:relative;padding-top:56.25%;height:0;overflow:hidden;border-radius:.5rem;box-shadow:0 2px 8px #0000001a;background-color:#f8f8f8;transition:box-shadow .3s ease}.bilibili-wrapper:hover{box-shadow:0 4px 12px #00000026}.bilibili-wrapper iframe{position:absolute;top:0;left:0;width:100%;height:100%;border:none}.bilibili-loading{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fffc;z-index:1}.bilibili-loading .loading-spinner{width:40px;height:40px;border:4px solid rgba(0,161,214,.3);border-radius:50%;border-top-color:#00a1d6;animation:spin 1s ease-in-out infinite}@keyframes spin{to{transform:rotate(360deg)}}.bilibili-error{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:#f8f8f8;color:#666;padding:1rem;text-align:center}.bilibili-error .error-icon{font-size:2rem;color:#ff6b6b;margin-bottom:.5rem}.bilibili-error .error-message{font-size:.9rem}.bilibili-error .retry-button{margin-top:.5rem;padding:.25rem .75rem;background-color:#00a1d6;color:#fff;border:none;border-radius:.25rem;cursor:pointer;font-size:.8rem;transition:background-color .2s}.bilibili-error .retry-button:hover{background-color:#0091c2}.dark-theme .bilibili-wrapper{background-color:#2a2a2a;box-shadow:0 2px 8px #0000004d}.dark-theme .bilibili-wrapper:hover{box-shadow:0 4px 12px #0006}.dark-theme .bilibili-loading{background-color:#2a2a2acc}.dark-theme .bilibili-error{background-color:#2a2a2a;color:#bbb}.hljs-comment,.hljs-quote{color:var(--code-comment);font-style:italic}.hljs-keyword,.hljs-selector-tag{color:var(--code-keyword)}.hljs-subst{color:var(--code-text)}.hljs-number,.hljs-literal,.hljs-variable,.hljs-template-variable{color:var(--code-number)}.hljs-string,.hljs-doctag{color:var(--code-string)}.hljs-title,.hljs-section,.hljs-selector-id,.hljs-type,.hljs-class .hljs-title{color:var(--code-function);font-weight:600}.hljs-tag,.hljs-name,.hljs-attribute{color:var(--code-tag)}.hljs-regexp,.hljs-link{color:var(--code-string)}.hljs-symbol,.hljs-bullet{color:var(--code-number)}.hljs-built_in,.hljs-builtin-name{color:var(--code-builtin)}.hljs-meta{color:var(--code-meta)}.hljs-deletion{color:var(--code-deletion-color);background:var(--code-deletion-bg)}.hljs-addition{color:var(--code-addition-color);background:var(--code-addition-bg)}.hljs-emphasis{font-style:italic}.hljs-strong{font-weight:600}.function_{color:var(--code-function)}.operator{color:var(--code-keyword)}.property{color:var(--code-variable)}.hljs-attr{color:var(--code-attribute)}.hljs-params{color:var(--code-variable)}.hljs-selector-class,.hljs-selector-pseudo{color:var(--code-function)}.hljs-selector-attr{color:var(--code-attribute)}.hljs-tag .hljs-name{color:var(--code-tag)}.hljs-tag .hljs-attr{color:var(--code-attribute)}.hljs-decorator{color:var(--code-function)}.hljs-annotation{color:var(--code-meta)}.hljs-code{color:var(--code-string)}.hljs-formula{color:var(--code-function)}.ProseMirror pre code{color:var(--code-text)}[data-theme=light] .hljs-variable{color:var(--code-variable)}[data-theme=light] .hljs-title.function_{color:var(--code-function)}[data-theme=dark] .hljs-variable{color:var(--code-variable)}[data-theme=dark] .hljs-title.function_{color:var(--code-function)}[data-theme=dark] .hljs-punctuation{color:var(--code-text)}.ProseMirror pre{position:relative;background-color:var(--creamy-white-1, #eeece4);border-radius:4px;padding:0;margin:1rem 0;overflow:hidden;font-family:Consolas,Source Code Pro,Courier New,monospace;font-size:.9rem;line-height:1.5;border:1px solid var(--creamy-white-3, #dcd8ca);box-shadow:0 2px 6px #0000000d;max-width:100%;width:100%;box-sizing:border-box;-webkit-user-select:none;user-select:none;-webkit-user-drag:none;-khtml-user-drag:none;-moz-user-drag:none;-o-user-drag:none;pointer-events:none}.ProseMirror pre.editable-mode{pointer-events:auto}.ProseMirror pre .code-block-header{display:flex;justify-content:space-between;align-items:center;background-color:var(--creamy-white-2, #e4e1d8);padding:.4rem .8rem;border-bottom:1px solid var(--creamy-white-3, #dcd8ca);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;font-size:.85rem;color:var(--gray-5, rgba(28, 25, 23, .6));border-top-left-radius:4px;border-top-right-radius:4px;flex-shrink:0;pointer-events:auto}.ProseMirror pre .code-block-header .code-block-language{font-weight:500;text-transform:lowercase;flex-shrink:0;-webkit-user-select:none!important;user-select:none!important;pointer-events:none!important;cursor:default!important}.ProseMirror pre .code-block-header .code-block-toolbar{display:flex;gap:.5rem;flex-shrink:0}.ProseMirror pre code{display:block;color:var(--code-text, var(--black, #2e2b29));padding:.8rem 1rem;background:none;font-family:inherit;white-space:pre;-moz-tab-size:4;tab-size:4;overflow:visible;max-width:100%;box-sizing:border-box}.ProseMirror pre .code-scrollbar-container{width:100%;max-width:100%;overflow:hidden;transition:none;box-sizing:border-box;position:relative}.ProseMirror pre .code-scrollbar-container .n-scrollbar{width:100%;max-width:100%;transition:none}.ProseMirror pre .code-scrollbar-container .n-scrollbar .n-scrollbar-rail--horizontal{height:8px;background-color:transparent;border-radius:4px;bottom:2px;display:block!important;opacity:1!important}.ProseMirror pre .code-scrollbar-container .n-scrollbar .n-scrollbar-rail--horizontal .n-scrollbar-thumb{background-color:var(--gray-4, rgba(53, 38, 28, .3));border-radius:4px;transition:background-color .2s ease;min-width:20px}.ProseMirror pre .code-scrollbar-container .n-scrollbar .n-scrollbar-rail--horizontal .n-scrollbar-thumb:hover{background-color:var(--gray-5, rgba(28, 25, 23, .5))}.ProseMirror pre .code-scrollbar-container .n-scrollbar:not(.code-wrap) .n-scrollbar-rail--horizontal{display:block!important;opacity:1!important}.ProseMirror pre .code-scrollbar-container .n-scrollbar.code-wrap .n-scrollbar-rail--horizontal{display:none!important}.ProseMirror pre .code-scrollbar-container .n-scrollbar .n-scrollbar-rail--vertical{width:8px;background-color:transparent;border-radius:4px;right:2px}.ProseMirror pre .code-scrollbar-container .n-scrollbar .n-scrollbar-rail--vertical .n-scrollbar-thumb{background-color:var(--gray-4, rgba(53, 38, 28, .3));border-radius:4px;transition:background-color .2s ease;min-height:20px}.ProseMirror pre .code-scrollbar-container .n-scrollbar .n-scrollbar-rail--vertical .n-scrollbar-thumb:hover{background-color:var(--gray-5, rgba(28, 25, 23, .5))}.ProseMirror pre .code-scrollbar-container.editable-mode code[contenteditable=true]{outline:none;border:none;background:transparent;cursor:text}.ProseMirror pre .code-scrollbar-container.editable-mode code[contenteditable=true]:focus{outline:none;box-shadow:none}.ProseMirror pre .code-scrollbar-container.editable-mode code[contenteditable=true]::selection{background-color:#4ba3fd33;color:inherit}.ProseMirror pre .code-scrollbar-container.readonly-mode code{cursor:default;-webkit-user-select:text;user-select:text}.ProseMirror pre .code-scrollbar-container.readonly-mode code.code-selectable{-webkit-user-select:text!important;user-select:text!important;pointer-events:auto!important;cursor:text}.ProseMirror pre .code-scrollbar-container.readonly-mode code.code-selectable::selection{background-color:#4ba3fd40;color:inherit}.ProseMirror pre.code-wrap code,.ProseMirror pre code.code-wrap-enabled{white-space:pre-wrap;word-break:break-all;overflow:visible}.ProseMirror pre:not(.code-wrap) code{overflow:visible}.ProseMirror pre .code-copy-button,.ProseMirror pre .code-wrap-button{width:24px;height:24px;padding:4px;background-color:transparent;border:none;border-radius:4px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.ProseMirror pre .code-copy-button svg,.ProseMirror pre .code-wrap-button svg{width:16px;height:16px;stroke:var(--gray-5, rgba(28, 25, 23, .6));transition:all .2s ease}.ProseMirror pre .code-copy-button:hover,.ProseMirror pre .code-wrap-button:hover{background-color:var(--creamy-white-3, #dcd8ca)}.ProseMirror pre .code-copy-button:hover svg,.ProseMirror pre .code-wrap-button:hover svg{stroke:var(--black, #2e2b29)}.ProseMirror pre .code-copy-button.active,.ProseMirror pre .code-wrap-button.active{background-color:var(--creamy-white-3, #dcd8ca);box-shadow:inset 0 1px 2px #0000001a}.ProseMirror pre .code-copy-button.active svg,.ProseMirror pre .code-wrap-button.active svg{stroke:var(--black, #2e2b29)}.ProseMirror pre .code-copy-button.copied{background-color:#22c55e1a;box-shadow:inset 0 1px 2px #0000000d}.ProseMirror pre .code-copy-button.copied svg{stroke:var(--green, #22c55e)}.ProseMirror pre .code-wrap-button.active{background-color:#4ba3fd1a;box-shadow:inset 0 1px 2px #0000000d}.ProseMirror pre .code-wrap-button.active svg{stroke:var(--blue, #4ba3fd)}.ProseMirror.ProseMirror-readonly pre{background-color:var(--creamy-white-1, #eeece4);border-color:var(--creamy-white-3, #dcd8ca)}.ProseMirror pre.code-block-readonly{-webkit-user-select:none!important;user-select:none!important;pointer-events:none!important}.ProseMirror pre.code-block-readonly .code-block-header{pointer-events:auto!important}.ProseMirror pre.code-block-readonly.readonly-mode code.code-selectable{pointer-events:auto!important;-webkit-user-select:text!important;user-select:text!important;cursor:text!important}.ProseMirror pre.code-block-readonly.editable-mode code{pointer-events:none!important;-webkit-user-select:none!important;user-select:none!important;cursor:default!important}.slash-menu{white-space:nowrap;pointer-events:all;width:20rem;max-height:15rem;padding:.25rem;overflow:hidden auto;border:1px solid var(--border-color, #e5e7eb);border-radius:.5rem;background-color:var(--bg-color, white);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -2px #0000001a;z-index:10000}.slash-menu.slash-menu-above{transform-origin:bottom center;animation:slide-up-fade-in .15s ease-out}.slash-menu.slash-menu-below{transform-origin:top center;animation:slide-down-fade-in .15s ease-out}.slash-menu-empty{display:flex;align-items:center;font-weight:500;font-size:.875rem;height:2.25rem;padding:0 .75rem;color:var(--text-muted, #6b7280)}.slash-menu-divider{display:block;height:1px;margin:.25rem -.25rem;background-color:var(--border-color, #e5e7eb)}.slash-menu-button{-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;user-select:none;cursor:pointer;display:flex;align-items:center;justify-content:flex-start;line-height:1;font-weight:500;font-size:.875rem;border:none;outline:none;width:100%;height:2.25rem;padding:.375rem .5rem;border-radius:.375rem;background-color:transparent;color:var(--text-color, #374151);transition:all .15s ease}.slash-menu-button:hover,.slash-menu-button:focus,.slash-menu-button[data-active]{color:var(--text-active, #1f2937);background-color:var(--bg-hover, #f3f4f6)}.slash-menu-button-icon{margin-left:.25rem;margin-right:.75rem;width:1rem;height:1rem;display:flex;align-items:center;justify-content:center}.slash-menu-button-icon svg{width:1rem;height:1rem}.slash-menu-button-name{flex-grow:1;text-align:start}.slash-menu-button-shortcut{color:var(--text-muted, #6b7280);font-family:var(--font-mono, "Fira Code", monospace);font-size:.625rem;letter-spacing:.2em;text-transform:uppercase}.slash-menu-placeholder:before{content:attr(data-placeholder);display:block;pointer-events:none;padding-left:.125rem;height:0;opacity:0;color:var(--text-muted, #6b7280);font-size:.9em;transition:opacity .15s ease}.ProseMirror-focused .slash-menu-placeholder:before{opacity:1}.ProseMirror[contenteditable=false] .slash-menu-placeholder:before{opacity:0}.dark-theme .slash-menu{--bg-color: #1f2937;--border-color: #374151;--text-color: #f9fafb;--text-active: #fff;--text-muted: #9ca3af;--bg-hover: #374151;box-shadow:0 4px 6px -1px #0000004d,0 2px 4px -2px #0003}.dark-theme .slash-menu-placeholder:before{color:var(--text-muted, #9ca3af)}.slash-menu::-webkit-scrollbar{width:5px}.slash-menu::-webkit-scrollbar-track{background:transparent}.slash-menu::-webkit-scrollbar-thumb{background:var(--border-color, #e5e7eb);border-radius:5px}.slash-menu *{scrollbar-width:thin;scrollbar-color:var(--border-color, #e5e7eb) transparent}@keyframes slide-down-fade-in{0%{opacity:0;transform:translateY(-8px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@keyframes slide-up-fade-in{0%{opacity:0;transform:translateY(8px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}.tiptap-btn-wrapper[data-v-0648a6e6]{display:inline-flex;align-items:center;justify-content:center;line-height:1;height:1.75rem;width:1.75rem}.is-active[data-v-0648a6e6]{background-color:#0080ff1a;color:#0080ff}[data-v-43be4655] .n-color-picker-trigger{border:none!important;width:0!important;height:0!important;padding:0!important;margin:0!important;overflow:hidden!important}.flex-column-gap12[data-v-ccec921c]{display:flex;flex-direction:column;gap:12px}.long-press-wrapper[data-v-5f07a55b]{display:inline-block;cursor:pointer}.slot-content[data-v-5f07a55b]{transition:transform .2s ease}.long-press-active[data-v-5f07a55b]{animation:long-press-animation-5f07a55b .5s infinite alternate}@keyframes long-press-animation-5f07a55b{0%{transform:scale(1)}to{transform:scale(1.1)}}[data-v-4e360fe8] .n-color-picker-trigger{border:none!important;width:0!important;height:0!important;padding:0!important;margin:0!important;overflow:hidden!important}.fullscreen-close-button[data-v-4e360fe8]{position:absolute;top:.5rem;right:.5rem;z-index:1000;cursor:pointer;border-radius:50%;width:32px;height:32px;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.fullscreen-close-button[data-v-4e360fe8]:hover{transform:scale(1.1)}.character-count[data-v-4e360fe8]{text-align:right;font-size:.8rem;color:#888;margin-top:.25rem;padding-right:.5rem}.article-modal.n-modal{width:100vw;max-width:100vw}.article-modal.n-dialog{margin:0}.article-modal-content{max-width:100%;padding:0 2.5rem;border:var(--border-1);border-radius:.25rem;min-height:66.5vh;min-height:66.5dvh;max-height:66.5vh;max-height:66.5dvh;overflow:hidden;box-sizing:border-box}.dark-theme .article-modal-content{background-color:var(--white-2)}.article-modal-content .tiptap-editor-wrapper{max-width:100%;height:100%;box-sizing:border-box;display:flex;flex-direction:column}.article-modal-content .tiptap-editor-wrapper .editor-toolbar{position:sticky;top:0;z-index:10}.article-modal-content .tiptap-editor-wrapper .editor-content{flex:1;overflow-y:auto;max-width:100%;box-sizing:border-box}.notification-btn[data-v-3d75049d]{position:relative;display:inline-block}.notification-table[data-v-d44bf846] .n-pagination{display:flex;align-items:center;justify-content:flex-end;flex-wrap:nowrap}.notification-table[data-v-d44bf846] .n-pagination-prefix{white-space:nowrap;margin-right:.5rem}.notification-pagination[data-v-d44bf846]{display:flex;flex-wrap:wrap;margin-top:1rem;justify-content:center;row-gap:.5rem}.notification-popover-content[data-v-d44bf846]{max-width:500px}.cursor-pointer[data-v-d44bf846]{cursor:pointer}.flex-column-end[data-v-d44bf846]{display:flex;align-items:center}.notification-popselect[data-v-72f4bad8]{margin-left:8px}.notification-container[data-v-9f38a6c7]{text-align:end}[data-v-9f38a6c7] .dark-notification-content{color:var(--black)}[data-v-9f38a6c7] .dark-notification-avatar{border:1px solid var(--gray-3)}[data-v-9f38a6c7] .dark-notification-button{color:var(--blue)}.theme-toggle-scene[data-v-fbb97caf]{position:relative;width:48px;height:28px;border-radius:14px;cursor:pointer;overflow:hidden;transition:all .2s ease;box-shadow:0 2px 4px #0003;background:linear-gradient(180deg,#4a90e2,#a5d6ff)}.theme-toggle-scene.is-dark[data-v-fbb97caf]{background:linear-gradient(180deg,#000814,#001440)}.sky[data-v-fbb97caf]{position:relative;width:100%;height:100%;overflow:hidden}.sun[data-v-fbb97caf],.moon[data-v-fbb97caf]{position:absolute;width:18px;height:18px;transition:all .25s ease-out}.sun[data-v-fbb97caf]{color:gold;left:14px;top:5px;opacity:1;z-index:2}.sun-set[data-v-fbb97caf]{transform:translateY(30px);opacity:0}.moon[data-v-fbb97caf]{color:#fff;right:12px;top:4px;opacity:0;z-index:2;filter:drop-shadow(0 0 3px rgba(255,255,255,80%));width:20px;height:20px}.moon-rise[data-v-fbb97caf]{opacity:1}.theme-toggle-scene[data-v-fbb97caf]:before{content:"";position:absolute;width:10px;height:4px;background-color:#ffffffb3;border-radius:4px;left:5px;top:18px;transition:all .25s ease;opacity:1;z-index:1}.theme-toggle-scene[data-v-fbb97caf]:after{content:"";position:absolute;width:8px;height:3px;background-color:#ffffff7f;border-radius:3px;right:7px;top:6px;transition:all .25s ease;opacity:1;z-index:1}.theme-toggle-scene.is-dark[data-v-fbb97caf]:before,.theme-toggle-scene.is-dark[data-v-fbb97caf]:after{width:2px;height:2px;border-radius:50%;background-color:#ffffffe5}.theme-toggle-scene.is-dark[data-v-fbb97caf]:before{left:12px;top:8px}.theme-toggle-scene.is-dark[data-v-fbb97caf]:after{right:10px;top:16px}.theme-toggle-scene.is-dark .sky[data-v-fbb97caf]:before,.theme-toggle-scene.is-dark .sky[data-v-fbb97caf]:after{content:"";position:absolute;width:1px;height:1px;background-color:#fff;border-radius:50%;opacity:.9;z-index:1}.theme-toggle-scene.is-dark .sky[data-v-fbb97caf]:before{left:20px;top:10px}.theme-toggle-scene.is-dark .sky[data-v-fbb97caf]:after{left:32px;top:6px}.theme-toggle-scene[data-v-fbb97caf]:hover{transform:scale(1.02)}.theme-toggle-scene[data-v-fbb97caf]:active{transform:scale(.98)}.avatar-container[data-v-82e981e2]{display:flex;justify-content:center;align-items:center}.user-info[data-v-82e981e2]{font-size:.8rem;padding:.25rem .5rem;min-width:8rem}.info-row[data-v-82e981e2]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.info-row strong[data-v-82e981e2]{margin-right:.3rem;min-width:3.5rem;flex-shrink:0}.actions-row[data-v-82e981e2]{display:flex;justify-content:space-between;align-items:center;padding-top:.5rem;border-top:1px solid rgba(0,0,0,10%)}.user-info-group[data-v-e7094eb5]{display:flex;align-items:center;margin-top:.75rem}.online-notification-container[data-v-e7094eb5]{width:3rem;margin-right:12%}.online-info[data-v-e7094eb5]{display:flex;justify-content:flex-end}.comment-title-container[data-v-13563250]{box-sizing:border-box;padding:1.25rem 1.25rem .5rem;border-bottom:var(--border-1);display:flex;flex-direction:column;height:176px}.comment-title-container .comment-header-top[data-v-13563250]{display:flex;justify-content:space-between;align-items:center;height:7.5rem;flex-wrap:wrap}.comment-title-container .comment-header-bottom[data-v-13563250]{display:flex;justify-content:flex-start;align-items:center}.comment-title-container[data-v-13563250] .n-breadcrumb{padding:.5rem 0;transition:all .2s ease}.comment-title-container[data-v-13563250] .n-breadcrumb .n-breadcrumb-item{position:relative}.comment-title-container[data-v-13563250] .n-breadcrumb .n-breadcrumb-item:last-child{font-weight:700}.comment-title-container .breadcrumb-text[data-v-13563250]{display:flex;align-items:center;font-size:1rem;transition:all .25s ease;padding:.3rem 0;position:relative;overflow:hidden}.comment-title-container .breadcrumb-text[data-v-13563250]:before{content:"";position:absolute;left:0;bottom:0;width:0;height:2px;background-color:var(--blue);transition:width .3s ease}.comment-title-container .breadcrumb-text[data-v-13563250]:hover:before{width:100%}.comment-title-container .breadcrumb-text .n-avatar[data-v-13563250]{margin:0 .25rem;border:1px solid rgba(200,200,200,.3)}@media (width <= 768px){.comment-title-container[data-v-13563250] .n-breadcrumb{padding:.3rem 0}.comment-title-container .breadcrumb-text[data-v-13563250]{font-size:.9rem;padding:.2rem 0}}.comment-controls-container[data-v-c085b415]{display:flex;justify-content:space-between;align-items:center;margin-top:.5rem}.comment-controls-container .comment-reply-info[data-v-c085b415]{display:flex;align-items:center;gap:.25rem}.comment-controls-container .comment-interaction-btn[data-v-c085b415]{display:flex;align-items:center;gap:.5rem}.comment-controls-container .n-icon[data-v-c085b415]{transition:transform .2s ease}.comment-controls-container .n-icon[data-v-c085b415]:hover{transform:scale(1.2)}.comment-controls-container .comment-reply-list-btn[data-v-c085b415]{font-size:.9rem;padding:0 .5rem}.user-comment-container[data-v-1e1a0c32]{border:var(--border-1);border-radius:.6rem;padding:.6rem 1.25rem;margin-bottom:3%;background-color:var(--comment-container-bg, var(--white-1))}.user-comment-container-fixed[data-v-1e1a0c32]{border:var(--border-1);border-radius:.6rem;padding:.6rem 1.25rem;margin-bottom:3%;background-color:var(--comment-fixed-bg, var(--blue-light));position:sticky;top:0;z-index:2;opacity:1}.comment-flash[data-v-1e1a0c32]{animation:flash-1e1a0c32 1s ease-in-out}.comment-flash[data-v-1e1a0c32] .tiptap-editor-wrapper,.comment-flash[data-v-1e1a0c32] .editor-content,.comment-flash[data-v-1e1a0c32] .ProseMirror,.comment-flash[data-v-1e1a0c32] p,.comment-flash[data-v-1e1a0c32] blockquote,.comment-flash[data-v-1e1a0c32] h1,.comment-flash[data-v-1e1a0c32] h2,.comment-flash[data-v-1e1a0c32] h3,.comment-flash[data-v-1e1a0c32] h4,.comment-flash[data-v-1e1a0c32] h5,.comment-flash[data-v-1e1a0c32] h6{background-color:var(--blue-light)!important}.user-comment-container-fixed[data-v-1e1a0c32] .tiptap-editor-wrapper,.user-comment-container-fixed[data-v-1e1a0c32] .editor-content,.user-comment-container-fixed[data-v-1e1a0c32] .ProseMirror,.user-comment-container-fixed[data-v-1e1a0c32] p,.user-comment-container-fixed[data-v-1e1a0c32] blockquote,.user-comment-container-fixed[data-v-1e1a0c32] h1,.user-comment-container-fixed[data-v-1e1a0c32] h2,.user-comment-container-fixed[data-v-1e1a0c32] h3,.user-comment-container-fixed[data-v-1e1a0c32] h4,.user-comment-container-fixed[data-v-1e1a0c32] h5,.user-comment-container-fixed[data-v-1e1a0c32] h6,.comment-flash[data-v-1e1a0c32] .tiptap-editor-wrapper,.comment-flash[data-v-1e1a0c32] .editor-content,.comment-flash[data-v-1e1a0c32] .ProseMirror,.comment-flash[data-v-1e1a0c32] p,.comment-flash[data-v-1e1a0c32] blockquote,.comment-flash[data-v-1e1a0c32] h1,.comment-flash[data-v-1e1a0c32] h2,.comment-flash[data-v-1e1a0c32] h3,.comment-flash[data-v-1e1a0c32] h4,.comment-flash[data-v-1e1a0c32] h5,.comment-flash[data-v-1e1a0c32] h6{background-color:var(--comment-fixed-bg, var(--blue-light))}.user-comment-container-fixed .comment-reply-row[data-v-1e1a0c32],.comment-flash .comment-reply-row[data-v-1e1a0c32],.user-comment-container-fixed .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .tiptap-editor-wrapper,.user-comment-container-fixed .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .editor-content,.comment-flash .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .tiptap-editor-wrapper,.comment-flash .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .editor-content{background-color:var(--comment-fixed-reply-bg, var(--blue-light))}.user-comment-container-fixed .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .ProseMirrorInput,.comment-flash .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .ProseMirrorInput{background-color:var(--comment-fixed-prosemirror-bg, var(--blue-light));border:1px solid var(--gray-3);opacity:1}.user-comment-container[data-v-1e1a0c32] .tiptap-editor-wrapper,.user-comment-container[data-v-1e1a0c32] .editor-content,.user-comment-container[data-v-1e1a0c32] .ProseMirror,.user-comment-container[data-v-1e1a0c32] p,.user-comment-container[data-v-1e1a0c32] blockquote,.user-comment-container[data-v-1e1a0c32] h1,.user-comment-container[data-v-1e1a0c32] h2,.user-comment-container[data-v-1e1a0c32] h3,.user-comment-container[data-v-1e1a0c32] h4,.user-comment-container[data-v-1e1a0c32] h5,.user-comment-container[data-v-1e1a0c32] h6{background-color:var(--comment-container-bg, var(--white-1))}.user-comment-container .comment-reply-row[data-v-1e1a0c32],.user-comment-container .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .tiptap-editor-wrapper,.user-comment-container .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .editor-content{background-color:var(--comment-reply-bg, var(--white-1))}.user-comment-container .comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32] .ProseMirrorInput{background-color:var(--comment-reply-prosemirror-bg, var(--white-1));border:1px solid var(--gray-3)}.user-info-row[data-v-1e1a0c32]{display:flex;align-items:center;margin-bottom:.6rem}.user-info-row .user-detail-col[data-v-1e1a0c32]{margin-left:.6rem}.user-info-row .user-detail-col .user-nickname[data-v-1e1a0c32]{display:block;font-weight:700}.user-info-row .user-detail-col .user-extra-info[data-v-1e1a0c32]{display:block}.user-info-row .user-detail-col .user-extra-info .time-clickable[data-v-1e1a0c32]{cursor:pointer;transition:all .3s ease}.user-info-row .user-detail-col .user-extra-info .time-clickable[data-v-1e1a0c32]:hover{opacity:.8}.comment-content-row[data-v-1e1a0c32]{margin-bottom:.25rem;font-size:1rem}.comment-interaction-reply[data-v-1e1a0c32]{display:flex}.comment-interaction-reply .comment-reply-info[data-v-1e1a0c32]{display:flex;align-items:center;width:60%;margin-right:1rem;font-size:.8rem;gap:.3rem}.comment-interaction-reply .comment-reply-info .comment-reply-list-btn[data-v-1e1a0c32]{margin-right:1%}.comment-interaction-reply .comment-interaction-btn[data-v-1e1a0c32]{display:flex;align-items:center;gap:.4rem;font-size:.8rem}.comment-reply-row[data-v-1e1a0c32]{display:flex;justify-content:center;align-items:end;background-color:inherit;padding-top:.5rem}.comment-reply-row .comment-reply-tiptap-editor[data-v-1e1a0c32]{max-width:80%;margin-right:1.25rem}.comment-reply-send-btn[data-v-1e1a0c32]{margin-bottom:1.5rem}.user-comment-container-fixed .comment-reply-send-btn[data-v-1e1a0c32],.comment-flash .comment-reply-send-btn[data-v-1e1a0c32]{background-color:var(--comment-fixed-reply-btn-bg, var(--blue-light));opacity:1}.user-comment-container .comment-reply-send-btn[data-v-1e1a0c32]{background-color:inherit}@keyframes flash-1e1a0c32{0%{background-color:var(--blue-light)}50%{background-color:var(--blue-light)}to{background-color:var(--blue-light)}}.comment-list-container[data-v-29b50c04]{flex:1;overflow-y:auto;position:relative;padding:1.25rem 0 1.25rem 1.25rem;display:flex;flex-direction:column;min-height:0;height:100%}.comment-list-container.has-input-box[data-v-29b50c04]{padding-bottom:calc(1.25rem + 120px)}.comment-list-container .comment-scroll[data-v-29b50c04]{flex:1;padding-right:1.25rem;display:flex;flex-direction:column;min-height:0;height:100%}.comment-list-container .comment-scroll .comment-list-footer[data-v-29b50c04]{padding:1.25rem 0;display:flex;justify-content:center;flex-shrink:0}@media (width <= 768px){.comment-list-container[data-v-29b50c04]{height:calc(100vh - 60px)}}.comment-input-affix[data-v-160c36db]{width:100%;box-shadow:0 -2px 10px #0000000d;position:absolute;bottom:0;left:0;background-color:var(--comment-input-bg, var(--creamy-white-1));z-index:1500;transition:transform .3s ease}@media (width <= 768px){.comment-input-affix[data-v-160c36db]{position:fixed;width:100vw}}.comment-input-row[data-v-160c36db]{display:flex;align-items:end;justify-content:center;background-color:var(--comment-input-bg, var(--creamy-white-1));border-top:var(--border-1);padding:.5rem 1.25rem;width:100%;box-sizing:border-box}@media (width <= 768px){.comment-input-row[data-v-160c36db]{padding:.5rem}}.comment-input-row .comment-tiptap-editor[data-v-160c36db]{max-width:75%;margin-right:1.25rem}@media (width <= 768px){.comment-input-row .comment-tiptap-editor[data-v-160c36db]{max-width:70%}}.comment-input-row .comment-tiptap-editor[data-v-160c36db] .tiptap-editor-wrapper,.comment-input-row .comment-tiptap-editor[data-v-160c36db] .editor-content,.comment-input-row .comment-tiptap-editor[data-v-160c36db] .ProseMirrorInput{background-color:var(--comment-input-prosemirror-bg, var(--creamy-white-1))}.comment-reply-send-btn[data-v-160c36db]{margin-bottom:1.7rem}@media (width <= 768px){.comment-reply-send-btn[data-v-160c36db]{margin-bottom:1.7rem}}.comment-info-container[data-v-099b34b5]{box-sizing:border-box;flex:0 0 35vw;flex:0 0 35dvw;width:35vw;width:35dvw;height:100vh;height:100dvh;background-color:var(--comment-info-bg, var(--creamy-white-1));display:flex;flex-direction:column;position:relative}@media (width <= 768px){.comment-info-container[data-v-099b34b5]{width:100vw;width:100dvw;flex:0 0 100vw;flex:0 0 100dvw;height:100vh;position:relative}}.article-layout[data-v-016c23d7]{display:flex;height:100vh;height:100dvh;width:100vw;width:100dvw;background-color:var(--gray-3);overflow-x:hidden}.article-layout[data-v-016c23d7] .article-info-container{flex:0 0 65vw;flex:0 0 65dvw;width:65%;padding:1.25rem;box-sizing:border-box;display:flex;flex-direction:column;align-items:center;height:100vh;height:100dvh}.article-layout[data-v-016c23d7] .article-info-container .article-header{display:flex;flex-direction:column;align-items:center;width:100%;position:relative;margin:1.25rem 0}.article-layout[data-v-016c23d7] .article-info-container .article-header .article-header-content-wrapper{margin-top:3rem}.article-layout[data-v-016c23d7] .article-info-container .article-header .article-header-content-wrapper .article-header-content{display:flex;flex-direction:column;text-align:center}.article-layout[data-v-016c23d7] .article-info-container .article-header .article-header-content-wrapper .article-header-content .article-tag-container{display:flex;gap:.5rem;justify-content:center}.article-layout[data-v-016c23d7] .article-info-container .article-header .article-header-content-wrapper .article-header-content .article-tag-container .article-tag{margin-left:0}.article-layout[data-v-016c23d7] .article-info-container .time-clickable{cursor:pointer;transition:all .3s ease}.article-layout[data-v-016c23d7] .article-info-container .time-clickable:hover{opacity:.8;text-decoration:underline}.article-layout[data-v-016c23d7] .article-info-container .action-buttons-container{display:flex;flex-direction:column;align-items:flex-end;position:absolute;top:-5%;right:1%}.article-layout[data-v-016c23d7] .article-info-container .action-buttons-container .edit-button-container{margin-bottom:.5rem;display:flex;gap:.25rem}.article-layout[data-v-016c23d7] .article-info-container .action-buttons-container .interaction-container{display:flex;font-size:.8rem;gap:.4rem;align-items:center}.article-layout[data-v-016c23d7] .article-info-container .action-buttons-container .comment-count-container{margin-top:.5rem;margin-right:.25rem;font-size:.8rem;display:flex}.article-layout[data-v-016c23d7] .article-info-container .article-content{padding:1rem 0 1rem 1rem;border-radius:.5rem;width:90%;overflow-y:auto;background-color:var(--white-1)}.article-layout[data-v-016c23d7] .article-info-container .article-content :deep(.image-wrapper),.article-layout[data-v-016c23d7] .article-info-container .article-content :deep(img){max-width:100%;height:auto;object-fit:contain}@media (width <= 55rem){.article-layout[data-v-016c23d7]{flex-direction:column}.article-layout .article-info-container[data-v-016c23d7],.article-layout .comment-info-container[data-v-016c23d7]{flex:0 0 100vw;flex:0 0 100dvw;width:100%}.article-layout .article-info-container .article-content[data-v-016c23d7]{width:95%;padding:1rem 0 1rem 1rem}.article-layout .article-info-container .article-content[data-v-016c23d7] .ProseMirror p>.image-wrapper,.article-layout .article-info-container .article-content[data-v-016c23d7] .ProseMirror p>img{max-width:100%!important;min-width:unset!important;width:auto!important;height:auto!important}.article-layout .article-info-container .article-header[data-v-016c23d7]{margin:.75rem 0}.article-layout .article-info-container .article-header h2[data-v-016c23d7]{font-size:1.4rem;word-break:break-word}}.card-item[data-v-dc274bb4]{border-radius:.5rem;margin-top:1.25rem;box-sizing:border-box;max-width:100vw}.card-item[data-v-dc274bb4]:hover{transform:translateY(-.6rem);box-shadow:var(--shadow)}.card-item.dragging[data-v-dc274bb4]{opacity:.3;pointer-events:none}.card-item.drag-over-before[data-v-dc274bb4]:before,.card-item.drag-over-after[data-v-dc274bb4]:after,.card-item.drag-over-before-vertical[data-v-dc274bb4]:before,.card-item.drag-over-after-vertical[data-v-dc274bb4]:after{content:"";position:absolute;background-color:var(--blue);z-index:10;border-radius:2px}.card-item.drag-over-before[data-v-dc274bb4]:before,.card-item.drag-over-after[data-v-dc274bb4]:after{top:0;bottom:0;width:4px}.card-item.drag-over-before[data-v-dc274bb4]:before{left:-12px}.card-item.drag-over-after[data-v-dc274bb4]:after{right:-12px}.card-item.drag-over-before-vertical[data-v-dc274bb4]:before,.card-item.drag-over-after-vertical[data-v-dc274bb4]:after{left:0;right:0;height:4px}.card-item.drag-over-before-vertical[data-v-dc274bb4]:before{top:-12px}.card-item.drag-over-after-vertical[data-v-dc274bb4]:after{bottom:-12px}.card-item .article-header[data-v-dc274bb4]{display:flex;align-items:center;padding:.15rem 0}.card-item .article-header .scope-icon-wrapper[data-v-dc274bb4]{margin-right:.6rem;padding:.3rem;border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all .2s ease;flex-shrink:0;color:var(--black);cursor:pointer}.card-item .article-header .scope-icon-wrapper[data-v-dc274bb4]:hover{opacity:.8;transform:scale(1.1)}.card-item .article-header .article-title[data-v-dc274bb4]{cursor:pointer;font-size:1.1rem;font-weight:700;color:var(--black);transition:color .2s ease;flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.card-item .article-header .article-title[data-v-dc274bb4]:hover{color:var(--blue);text-decoration:underline}.card-item .card-tag[data-v-dc274bb4]{margin:.6rem .1rem}.card-item .article-content[data-v-dc274bb4]{border-radius:.25rem;padding:.25rem;height:19rem;background-color:var(--white-1)}.card-item .article-content[data-v-dc274bb4] .image-wrapper,.card-item .article-content[data-v-dc274bb4] img{max-width:100%;height:auto!important;object-fit:contain}@media (width <= 768px){.card-item .article-content[data-v-dc274bb4] .ProseMirror p>.image-wrapper,.card-item .article-content[data-v-dc274bb4] .ProseMirror p>img{max-width:100%!important;min-width:unset!important;width:auto!important}}.card-item .article-avatar[data-v-dc274bb4]{cursor:grab;user-select:none;-webkit-user-select:none;touch-action:none}.card-item .article-avatar[data-v-dc274bb4]:active{cursor:grabbing}.trash-bin[data-v-4296f384]{position:fixed;bottom:2rem;left:50%;transform:translate(-50%);display:flex;flex-direction:column;align-items:center;justify-content:center;width:8rem;height:8rem;background-color:#fffffff2;border-radius:1rem;box-shadow:0 4px 12px #00000026;transition:all .3s ease;z-index:9998}.trash-bin.trash-bin-active[data-v-4296f384]{background-color:#ff44441a;transform:translate(-50%) scale(1.1);box-shadow:0 6px 20px #ff44444d}.trash-bin .trash-bin-text[data-v-4296f384]{margin-top:.5rem;font-size:.875rem;color:#666;font-weight:500}.trash-bin.trash-bin-active .trash-bin-text[data-v-4296f384]{color:#f44}.trash-bin-fade-enter-active[data-v-4296f384],.trash-bin-fade-leave-active[data-v-4296f384]{transition:opacity .3s ease,transform .3s ease}.trash-bin-fade-enter-from[data-v-4296f384],.trash-bin-fade-leave-to[data-v-4296f384]{opacity:0;transform:translate(-50%) translateY(2rem)}.article-container[data-v-133450a3]{position:relative;height:100%;width:100%;overflow-x:hidden}.article-container .infinite-scroll-container[data-v-133450a3]{width:100%;max-width:100%;overflow-x:hidden;height:100%;display:flex;flex-direction:column}.article-container .infinite-load-info[data-v-133450a3]{width:100%;padding:1.25rem 0;text-align:center;flex-shrink:0;display:flex;justify-content:center;align-items:center}.vue-danmaku{position:relative;overflow:hidden}.vue-danmaku .danmus{position:absolute;left:0;top:0;width:100%;height:100%;opacity:0;transition:all .3s;transform:translateZ(0);backface-visibility:hidden}.vue-danmaku .danmus.show{opacity:1}.vue-danmaku .danmus.paused .dm.move{animation-play-state:paused}.vue-danmaku .danmus .dm{position:absolute;font-size:1.25rem;color:var(--white-2);white-space:pre;transform:translateZ(0);will-change:transform;height:3rem;display:flex;align-items:center}.vue-danmaku .danmus .dm.move{animation-name:move-left;animation-timing-function:linear;animation-play-state:running}.vue-danmaku .danmus .dm.pause{animation-play-state:paused;z-index:100}@keyframes move-left{0%{transform:translate(0)}to{transform:translate(var(--dm-scroll-width))}}.danmaku-renderer{font-size:inherit;line-height:inherit;color:var(--black);word-break:break-word;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%;display:inline-block;vertical-align:bottom}.danmaku-renderer .mention{font-weight:700;vertical-align:bottom;margin-bottom:.15rem;background:#c8c8c84d}.danmaku-renderer .mention:hover{background:#b4b4b466}.danmaku-renderer .mention-avatar{overflow:hidden;aspect-ratio:1/1}.danmaku-renderer .image-placeholder{color:var(--gray);background-color:#0000000d;padding:0 4px;border-radius:3px}.danmaku-renderer code{background-color:#f6f2ff;border-radius:.4rem;color:#181818;font-size:.85em;padding:.25em .3em}.danmaku-renderer a{color:#56a9ff;text-decoration:none}.danmaku-renderer a:hover{text-decoration:underline}.danmaku-renderer .danmaku-image{display:inline-block;vertical-align:bottom;margin:0 3px;border-radius:3px;object-fit:contain;line-height:1;max-height:3rem;cursor:pointer}.danmaku-renderer strong{font-weight:700}.danmaku-renderer em{font-style:italic}.danmaku-renderer u{text-decoration:underline}.danmaku-renderer s{text-decoration:line-through}.modal-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#000000d9;display:flex;justify-content:center;align-items:center;z-index:9999;opacity:0;visibility:hidden;cursor:zoom-out;transition:opacity .3s ease,visibility .3s ease;padding:2rem;box-sizing:border-box;-webkit-backdrop-filter:blur(3px);backdrop-filter:blur(3px)}.modal-overlay-active{opacity:1;visibility:visible}.modal-overlay img{max-width:95%;max-height:90vh;object-fit:contain;border-radius:.25rem;box-shadow:0 4px 20px #00000040;transform:scale(.95);opacity:0;transition:opacity .5s ease,transform .3s ease}.modal-overlay-active img{opacity:1;transform:scale(1)}.loading-spinner{border:.25rem solid rgba(255,255,255,.2);border-top:.25rem solid #f0f0f0;border-radius:50%;width:3rem;height:3rem;animation:spin 1s linear infinite;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}@keyframes spin{0%{transform:translate(-50%,-50%) rotate(0)}to{transform:translate(-50%,-50%) rotate(360deg)}}@media (width <= 768px){.modal-overlay{padding:1rem}.modal-overlay img{max-width:100%}.loading-spinner{width:2.5rem;height:2.5rem}}.comment-container[data-v-e4604f80]{height:calc(100vh - 10rem);height:calc(100dvh - 10rem);width:100%;position:absolute;left:0;overflow:hidden}.comment-container .comment-danmaku[data-v-e4604f80]{width:100%;height:100%}.comment-container .comment-danmaku .comment-danmaku-item[data-v-e4604f80]{display:flex;align-items:flex-end;width:fit-content}.comment-container .comment-danmaku .comment-danmaku-item .comment-danmaku-publisher[data-v-e4604f80]{display:flex;align-items:center;margin-bottom:.2rem;font-size:1.2rem;color:var(--black)}.comment-container .comment-danmaku .comment-danmaku-item .comment-danmaku-publisher .n-avatar[data-v-e4604f80]{margin-right:4px}.comment-container .comment-danmaku .comment-danmaku-item .comment-danmaku-publisher .n-avatar[data-v-e4604f80] .n-avatar__img{object-fit:cover;aspect-ratio:1/1}.comment-container .comment-danmaku .comment-danmaku-item .comment-danmaku-content[data-v-e4604f80]{max-width:31rem;display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:1.2rem;line-height:1.5;vertical-align:bottom;margin-bottom:.25rem;color:var(--black)}.create-button[data-v-ed9878be]{transition:transform 1.5s cubic-bezier(.34,1.56,.64,1);transform-origin:center;will-change:transform}.create-button.is-rotating[data-v-ed9878be]{animation:rotate-and-scale-ed9878be 1.5s cubic-bezier(.34,1.56,.64,1) forwards}@keyframes rotate-and-scale-ed9878be{0%{transform:rotate(0) scale(1)}50%{transform:rotate(180deg) scale(1.5)}to{transform:rotate(360deg) scale(1)}}.search-container[data-v-771c8282]{margin-left:10%;margin-right:10%;margin-bottom:-2rem;width:31rem}.tag-bar-container[data-v-a6b87578]{display:flex;flex-wrap:wrap;gap:.5rem;padding:.25rem 1.25rem .5rem;background-color:var(--creamy-white-2);justify-content:center}.tag-bar-container .hot-tag[data-v-a6b87578]{cursor:pointer;transition:all .2s ease}.tag-bar-container .hot-tag[data-v-a6b87578]:hover{transform:scale(1.05)}.toggle-button-container[data-v-5e65680c]{display:inline-flex;align-items:center;justify-content:center;position:relative;margin:0 .5rem;perspective:1000px;width:3rem;height:3rem}.toggle-button-container .toggle-card[data-v-5e65680c]{position:relative;width:100%;height:100%;text-align:center;transition:transform .8s cubic-bezier(.34,1.56,.64,1);transform-style:preserve-3d;cursor:pointer}.toggle-button-container .toggle-card .toggle-card-front[data-v-5e65680c],.toggle-button-container .toggle-card .toggle-card-back[data-v-5e65680c]{position:absolute;top:0;left:0;width:100%;height:100%;backface-visibility:hidden;display:flex;align-items:center;justify-content:center}.toggle-button-container .toggle-card .toggle-card-front[data-v-5e65680c]{z-index:2}.toggle-button-container .toggle-card .toggle-card-back[data-v-5e65680c]{transform:rotateY(180deg)}.toggle-button-container .toggle-card.is-flipping[data-v-5e65680c]{transform:rotateY(180deg) scale(1.2)}.toggle-button-container .toggle-card[data-v-5e65680c]:hover{transform:scale(1.05)}.toggle-button-container .toggle-card.is-flipping[data-v-5e65680c]:hover{transform:rotateY(180deg) scale(1.2)}.common-layout[data-v-9cae50d3]{display:flex;flex-direction:column;height:100vh;height:100dvh;width:100vw;width:100dvw}.common-layout .common-layout-top[data-v-9cae50d3]{padding:1.25rem;background:linear-gradient(to bottom,var(--creamy-white-3),var(--creamy-white-2));display:flex;justify-content:flex-end;align-items:center;height:7.5rem;flex-wrap:wrap}.common-layout .common-layout-top .left-controls-container[data-v-9cae50d3]{display:flex;flex-direction:column;justify-content:flex-start;align-items:flex-start;position:absolute;left:1.25rem;top:7rem}.common-layout .common-layout-top .left-controls-container .control-item[data-v-9cae50d3]{display:flex;align-items:center;color:var(--black);font-size:.9rem}.common-layout .common-layout-top .left-controls-container .control-item .control-label[data-v-9cae50d3]{margin-right:0}.common-layout .common-layout-top .middle-controls-container[data-v-9cae50d3]{display:flex;justify-content:center;align-items:center;margin:auto;padding-left:4.5%;max-width:100%}.common-layout .tag-bar-wrapper[data-v-9cae50d3]{min-height:2.5rem;padding-bottom:.25rem;background-color:var(--creamy-white-2);display:flex;align-items:center;justify-content:center}.common-layout .common-layout-content[data-v-9cae50d3]{height:calc(100vh - 12.5rem);height:calc(100dvh - 12.5rem);background-color:var(--creamy-white-1);position:relative;overflow:hidden;width:100%;display:flex;flex-direction:column;padding-top:.25rem}.fade-slide-enter-active[data-v-9cae50d3],.fade-slide-leave-active[data-v-9cae50d3]{transition:opacity .3s ease,transform .3s ease}.fade-slide-enter-from[data-v-9cae50d3],.fade-slide-leave-to[data-v-9cae50d3]{opacity:0;transform:translate(-20px)}.background-animation[data-v-9f5b47f0]{position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;overflow:hidden}.static-elements[data-v-9f5b47f0]{position:absolute;width:100%;height:100%}.clouds-container[data-v-9f5b47f0]{position:absolute;width:100%;height:100%;overflow:hidden}.cloud[data-v-9f5b47f0]{position:absolute;background-color:transparent;animation:cloud-float-9f5b47f0 180s linear infinite;border:none!important;outline:none!important;filter:blur(3px);box-shadow:none!important;background-clip:padding-box}.cloud[data-v-9f5b47f0]:before{content:"";position:absolute;width:80%;height:80%;top:10%;left:10%;background-color:#ffffffd9;border-radius:50%;box-shadow:0 0 60px 15px #ffffff73;background:radial-gradient(circle at center,#ffffffe5,#fffc,#fff6 80%,#fff0)}.cloud[data-v-9f5b47f0]:after{content:"";position:var(--pos-1, absolute);width:var(--width-1, 0);height:var(--height-1, 0);top:var(--top-1, 0);left:var(--left-1, 0);background-color:#ffffffd9;border-radius:var(--radius-1, 50%);box-shadow:0 0 50px 10px #ffffff59;background:radial-gradient(circle at center,#ffffffd9,#ffffffb3 50%,#ffffff4d 85%,#fff0)}.cloud[style*="--cloud-type: 0"][data-v-9f5b47f0]{box-shadow:none!important;animation-duration:200s}.cloud[style*="--cloud-type: 1"][data-v-9f5b47f0]{box-shadow:none!important;animation-duration:190s}.cloud[style*="--cloud-type: 2"][data-v-9f5b47f0]{box-shadow:none!important;animation-duration:210s}.cloud[style*="--cloud-type: 3"][data-v-9f5b47f0]{box-shadow:none!important;animation-duration:170s}.cloud[style*="--cloud-type: 4"][data-v-9f5b47f0]{box-shadow:none!important;animation-duration:160s}.stars-container[data-v-9f5b47f0]{position:absolute;width:100%;height:100%}.star[data-v-9f5b47f0]{position:absolute;background-color:#fff;border-radius:50%;box-shadow:0 0 1px #fff,0 0 2px #fff}.dynamic-elements[data-v-9f5b47f0]{position:absolute;width:100%;height:100%}.dandelions-container[data-v-9f5b47f0]{position:absolute;width:100%;height:100%;perspective:1000px}.dandelion-seed[data-v-9f5b47f0]{position:absolute;background-color:#ffffffe5;border-radius:50%;animation:float-dandelion-sky-9f5b47f0 var(--animation-duration, 25s) cubic-bezier(.4,0,.2,1) infinite;opacity:0;transform-style:preserve-3d;box-shadow:0 0 2px #fff9}.dandelion-seed[data-v-9f5b47f0]:before,.dandelion-seed[data-v-9f5b47f0]:after{content:"";position:absolute;width:calc(var(--core-size, 3px) * 6);height:calc(var(--core-size, 3px) * 6);left:50%;top:50%;transform:translate(-50%,-50%);background:radial-gradient(ellipse at center,#ffffffe5,#ffffffb3,#fff6,#ffffff1a,#fff0);border-radius:50%;animation:seed-puff-sway-9f5b47f0 3s ease-in-out infinite;opacity:.9;pointer-events:none}.dandelion-seed[data-v-9f5b47f0]:after{width:calc(var(--core-size, 3px) * 4.2);height:calc(var(--core-size, 3px) * 4.2);opacity:.7;animation-delay:-1.5s}@keyframes seed-puff-sway-9f5b47f0{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.05)}}.dandelion-seed .main-stem[data-v-9f5b47f0]{position:absolute;width:calc(var(--core-size, 3px) * .6);height:calc(var(--core-size, 3px) * 10);background:linear-gradient(to bottom,#ffffffe5,#fff0);transform-origin:bottom center;top:calc(var(--core-size, 3px) * -10);left:calc(50% - var(--core-size, 3px) * .3);border-radius:30% 30% 0 0;animation:stem-sway-9f5b47f0 calc(var(--animation-duration, 25s) / 5) ease-in-out infinite alternate}.dandelion-seed[style*="--seed-type: 0"] .main-stem[data-v-9f5b47f0]{height:calc(var(--core-size, 3px) * 12);top:calc(var(--core-size, 3px) * -12);width:calc(var(--core-size, 3px) * .7)}.dandelion-seed[style*="--seed-type: 1"] .main-stem[data-v-9f5b47f0]{height:calc(var(--core-size, 3px) * 14);top:calc(var(--core-size, 3px) * -14);width:calc(var(--core-size, 3px) * .8);border-radius:40% 40% 0 0}.dandelion-seed[style*="--seed-type: 2"] .main-stem[data-v-9f5b47f0]{height:calc(var(--core-size, 3px) * 13);top:calc(var(--core-size, 3px) * -13);width:calc(var(--core-size, 3px) * .5);border-radius:20% 20% 0 0}@keyframes stem-sway-9f5b47f0{0%,to{transform:rotate(-8deg)}25%{transform:rotate(3deg)}75%{transform:rotate(-5deg)}}@keyframes float-dandelion-sky-9f5b47f0{0%{transform:translateY(0) translate(0) rotate(0) translateZ(0);opacity:0}5%{opacity:var(--max-opacity, .8);transform:translateY(-5vh) translate(0) rotate(calc(var(--rotation, 360deg) * .1)) translateZ(0)}25%{transform:translateY(calc(-.25 * var(--float-height, 100vh))) translate(calc(var(--float-side-wave, 10vw) * .5)) rotate(calc(var(--rotation, 360deg) * .3)) translateZ(20px)}50%{transform:translateY(calc(-.5 * var(--float-height, 100vh))) translate(calc(var(--float-side, 20vw) * .7)) rotate(calc(var(--rotation, 360deg) * .6)) translateZ(40px)}75%{transform:translateY(calc(-.75 * var(--float-height, 100vh))) translate(calc(var(--float-side-wave, 10vw) * .2)) rotate(calc(var(--rotation, 360deg) * .8)) translateZ(60px);opacity:var(--max-opacity, .8)}95%{opacity:.2}to{transform:translateY(calc(-1 * var(--float-height, 100vh))) translate(var(--float-side, 20vw)) rotate(var(--rotation, 360deg)) translateZ(80px);opacity:0}}@keyframes cloud-float-9f5b47f0{0%{transform:translate(-3%) translateY(0)}50%{transform:translate(0) translateY(-.5%)}to{transform:translate(3%) translateY(0)}}@keyframes firefly-lifecycle-9f5b47f0{0%{opacity:0;transform:translate(0)}5%{opacity:.7;transform:translate(0)}15%{opacity:.7;transform:translate(0)}35%{opacity:.7;transform:translate(var(--move-x),var(--move-y))}50%{opacity:.7;transform:translate(var(--move-x),var(--move-y))}55%{opacity:0;transform:translate(var(--move-x),var(--move-y))}to{opacity:0;transform:translate(var(--move-x),var(--move-y))}}@keyframes firefly-pulse-9f5b47f0{0%,to{box-shadow:0 0 var(--glow-size) var(--glow-color)}50%{box-shadow:0 0 calc(var(--glow-size) * 2) var(--glow-color),0 0 calc(var(--glow-size) * 3.5) #ffffff59}}.fireflies-container[data-v-9f5b47f0]{position:absolute;width:100%;height:100%}.firefly[data-v-9f5b47f0]{position:absolute;border-radius:50%;opacity:0;box-shadow:0 0 var(--glow-size) var(--glow-color);animation:firefly-lifecycle-9f5b47f0 var(--total-duration) ease-in-out infinite,firefly-pulse-9f5b47f0 var(--pulse-duration, 2s) ease-in-out infinite;will-change:transform,opacity}@keyframes twinkle-9f5b47f0{0%,to{opacity:.3}50%{opacity:1}}.layout-container[data-v-bb701e4a]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;height:100dvh;position:relative;z-index:1}.card-container[data-v-bb701e4a]{display:flex;align-items:center;justify-content:center;z-index:2}.login-form[data-v-bb701e4a]{padding:.6rem}.register-form[data-v-bb701e4a]{padding:.6rem;transform:rotateY(180deg)}.login-form-ipt[data-v-bb701e4a],.register-form-ipt[data-v-bb701e4a]{width:11rem;background-color:var(--creamy-white-1)}.login-form-btn[data-v-bb701e4a],.register-form-btn[data-v-bb701e4a]{display:flex;align-items:center;justify-content:center;gap:.5rem 1.25rem}.login-btn[data-v-bb701e4a],.register-btn[data-v-bb701e4a],.flip-btn[data-v-bb701e4a]{width:6.25rem}.header-container[data-v-bb701e4a]{display:flex;align-items:center;justify-content:center;width:22.5rem;height:200px;min-height:200px;margin-bottom:1rem;z-index:2;filter:drop-shadow(0 0 10px rgba(0,0,0,10%))}.footer-container[data-v-bb701e4a]{display:flex;align-items:center;justify-content:center;margin-top:1.5rem;z-index:2}.display-block{display:block}.cursor-pointer{cursor:pointer}.display-flex{display:flex}.display-none{display:none}.flex-column-start{display:flex;flex-direction:column;align-items:flex-start}.flex-column-end{display:flex;flex-direction:column;align-items:flex-end}.flex-column-center{display:flex;flex-direction:column;align-items:center}.flex-column-gap12{display:flex;flex-direction:column;gap:.75rem}.flex-column-gap24{display:flex;flex-direction:column;gap:1.5rem}.flex-between-center{display:flex;justify-content:space-between;align-items:center}.padding-0{padding:0}.padding-2{padding:2px}.padding-4{padding:4px}.margin-0{margin:0}.mr-1{margin-right:1rem}.mr-2{margin-right:2rem}.background-white{background-color:#fff}:root{--white: #fff;--white-1: #f0f0f0;--white-2: #ddd;--creamy-white-1: #eeece4;--creamy-white-2: #e4e1d8;--creamy-white-3: #dcd8ca;--black: #2e2b29;--black-contrast: #110f0e;--gray-1: rgba(61, 37, 20, 5%);--gray-2: rgba(61, 37, 20, 8%);--gray-3: rgba(61, 37, 20, 12%);--gray-4: rgba(53, 38, 28, 30%);--gray-5: rgba(28, 25, 23, 60%);--green: #22c55e;--blue: #4ba3fd;--blue-light: #e6f3ff;--purple: #6a00f5;--purple-contrast: #5800cc;--purple-light: rgba(88, 5, 255, 5%);--yellow-contrast: #facc15;--yellow: rgba(250, 204, 21, 40%);--yellow-light: #fffae5;--red: #ff5c33;--red-light: #ffebe5;--border-1: .1rem solid var(--gray-3);--shadow: 0 .25rem .6rem rgba(0, 0, 0, 10%)}html,body{margin:0;padding:0;overflow:hidden;overscroll-behavior:none}html{position:fixed;height:100%;width:100%;touch-action:manipulation}input,textarea,select{font-size:16px;max-height:100%}.article-container,.comment-container{overscroll-behavior:none}.dark-theme{--white: #121212;--white-1: #242424;--white-2: #363636;--creamy-white-1: #1a1a1a;--creamy-white-2: #262626;--creamy-white-3: #333;--black: #e0e0e0;--black-contrast: #fff;--gray-1: rgba(200, 200, 200, 5%);--gray-2: rgba(200, 200, 200, 8%);--gray-3: rgba(200, 200, 200, 12%);--gray-4: rgba(200, 200, 200, 30%);--gray-5: rgba(200, 200, 200, 60%);--green: #22c55e;--blue: #4ba3fd;--blue-light: #2a3745;--purple: #9d6dff;--purple-contrast: #8a5cf5;--purple-light: rgba(154, 92, 255, 15%);--yellow-contrast: #facc15;--yellow: rgba(250, 204, 21, 40%);--yellow-light: #3f3a14;--red: #ff5c33;--red-light: #3d1a12;--border-1: .1rem solid var(--gray-3);--shadow: 0 .25rem .6rem rgba(255, 255, 255, 10%);--comment-info-bg: var(--white-1);--comment-list-bg: var(--white-1);--comment-container-bg: var(--white-2);--comment-fixed-bg: var(--blue-light);--comment-input-bg: var(--white-1);--comment-reply-bg: var(--white-2);--comment-reply-btn-bg: var(--white-2);--comment-fixed-reply-bg: var(--blue-light);--comment-fixed-reply-btn-bg: var(--blue-light);--prosemirror-input-bg: var(--white-2);--comment-input-prosemirror-bg: var(--white-1);--comment-reply-prosemirror-bg: var(--white-2);--comment-fixed-prosemirror-bg: var(--blue-light)}.dark-theme .article-content{background-color:var(--white-1);color:var(--black)}.dark-theme .card-item{background-color:var(--white-2)}.dark-theme .article-title{color:var(--black)}.dark-theme .article-title:hover{color:var(--blue)}.dark-theme .comment-danmaku-item{color:var(--black)}.dark-theme .article-layout{background-color:var(--creamy-white-1)}.dark-theme .article-info-container .article-header{color:var(--black)}.dark-theme .article-info-container .article-content{background-color:var(--white-1)}.dark-theme .ProseMirror,.dark-theme .ProseMirror h1,.dark-theme .ProseMirror h2,.dark-theme .ProseMirror h3,.dark-theme .ProseMirror h4,.dark-theme .ProseMirror h5,.dark-theme .ProseMirror h6{color:var(--black)}.dark-theme .ProseMirror a{color:var(--blue)}.dark-theme .ProseMirror blockquote{border-left-color:var(--gray-3);background-color:var(--white-2)}.dark-theme .ProseMirror pre{background-color:var(--white-2)}.dark-theme .ProseMirror code{background-color:var(--white-2);color:var(--black)}.dark-theme .comment-info-container{background-color:var(--comment-info-bg);color:var(--black)}.dark-theme .comment-list-container{background-color:var(--comment-list-bg)}.dark-theme .user-comment-container{background-color:var(--comment-container-bg);border-color:var(--gray-3)}.dark-theme .user-comment-container-fixed,.dark-theme .comment-flash{background-color:var(--comment-fixed-bg);border-color:var(--gray-3);opacity:1}.dark-theme .user-nickname,.dark-theme .user-extra-info,.dark-theme .comment-content-row,.dark-theme .comment-interaction-reply{color:var(--black)}.dark-theme .user-comment-container .comment-content-row .ProseMirror,.dark-theme .user-comment-container .comment-content-row .tiptap-editor-wrapper,.dark-theme .user-comment-container .comment-content-row .editor-content,.dark-theme .user-comment-container .comment-content-row p,.dark-theme .user-comment-container .comment-content-row blockquote{background-color:var(--comment-container-bg)}.dark-theme .user-comment-container-fixed .comment-content-row .ProseMirror,.dark-theme .user-comment-container-fixed .comment-content-row .tiptap-editor-wrapper,.dark-theme .user-comment-container-fixed .comment-content-row .editor-content,.dark-theme .user-comment-container-fixed .comment-content-row p,.dark-theme .user-comment-container-fixed .comment-content-row blockquote,.dark-theme .comment-flash .comment-content-row .ProseMirror,.dark-theme .comment-flash .comment-content-row .tiptap-editor-wrapper,.dark-theme .comment-flash .comment-content-row .editor-content,.dark-theme .comment-flash .comment-content-row p,.dark-theme .comment-flash .comment-content-row blockquote{background-color:var(--comment-fixed-bg);opacity:1}.dark-theme .comment-input-row,.dark-theme .comment-reply-row{border-color:var(--gray-3)}.dark-theme .user-comment-container .comment-reply-row{background-color:var(--comment-reply-bg)}.dark-theme .user-comment-container .comment-reply-send-btn{background-color:var(--comment-reply-btn-bg)}.dark-theme .user-comment-container-fixed .comment-reply-row,.dark-theme .comment-flash .comment-reply-row{background-color:var(--comment-fixed-reply-bg);border-color:var(--gray-3);opacity:1}.dark-theme .user-comment-container-fixed .comment-reply-row .tiptap-editor-wrapper,.dark-theme .user-comment-container-fixed .comment-reply-row .editor-content,.dark-theme .comment-flash .comment-reply-row .tiptap-editor-wrapper,.dark-theme .comment-flash .comment-reply-row .editor-content{background-color:var(--comment-fixed-reply-bg);opacity:1}.dark-theme .user-comment-container-fixed .comment-reply-send-btn,.dark-theme .comment-flash .comment-reply-send-btn{background-color:var(--comment-fixed-reply-btn-bg);opacity:1}.dark-theme .user-comment-container .comment-reply-row .tiptap-editor-wrapper,.dark-theme .user-comment-container .comment-reply-row .editor-content{background-color:var(--comment-reply-bg)}.dark-theme .user-comment-container .comment-reply-row .ProseMirrorInput{background-color:var(--comment-reply-prosemirror-bg);border-color:var(--gray-4);color:var(--black)}.dark-theme .comment-input-row{border-top:1px solid var(--gray-4);box-shadow:0 -2px 8px #0003;background-color:var(--comment-input-bg)}.dark-theme .comment-input-row .tiptap-editor-wrapper,.dark-theme .comment-input-row .editor-content{background-color:var(--comment-input-bg)}.dark-theme .comment-input-row .ProseMirrorInput{background-color:var(--comment-input-prosemirror-bg);border-color:var(--gray-3);color:var(--black)}.dark-theme .user-comment-container-fixed .comment-reply-row .ProseMirrorInput,.dark-theme .comment-flash .comment-reply-row .ProseMirrorInput{background-color:var(--comment-fixed-prosemirror-bg);border-color:var(--gray-4);color:var(--black)}.dark-theme .comment-reply-row{border-top:1px solid var(--gray-3);margin-top:.5rem}.dark-theme .comment-title-container{color:var(--black)}.dark-theme .breadcrumb-text{color:var(--blue)}.dark-theme .comment-flash{background-color:var(--blue-light)}.dark-theme .n-input{background-color:var(--white-2);color:var(--black)}.dark-theme .n-scrollbar-rail{background-color:var(--white-2)}.dark-theme .n-scrollbar-thumb{background-color:var(--gray-4)}.dark-theme .ProseMirrorInput{background-color:var(--prosemirror-input-bg);border-color:var(--gray-3);color:var(--black)}.dark-theme .tiptap-fullscreen{background-color:var(--white-2);color:var(--black)}.dark-theme .tiptap-fullscreen .editor-content{background-color:var(--white-2)}.dark-theme .comment-input-row .tiptap-editor-wrapper .editor-content,.dark-theme .comment-reply-row .tiptap-editor-wrapper .editor-content{background-color:var(--white-1)}.dark-theme .comment-content-row .tiptap-editor-wrapper .editor-content{background-color:var(--white-2)}.dark-theme .tiptap-editor-wrapper .editor-content{background-color:transparent}.dark-theme .tiptap-toolbar{background-color:var(--white-2);border-color:var(--gray-3)}.dark-theme .tiptap-toolbar button{color:var(--black)}.dark-theme .tiptap-toolbar button:hover{background-color:var(--gray-3)}.dark-theme .character-count{color:var(--gray-5)}.dark-theme .common-layout-top{background-color:var(--creamy-white-2);color:var(--black)}.dark-theme .common-layout-content{background-color:var(--creamy-white-1)}.dark-theme .online-info,.dark-theme .search-container,.dark-theme .n-tabs .n-tab{color:var(--black)}.dark-theme .n-tabs .n-tab--active{color:var(--blue)}.dark-theme .n-tabs-tab-wrapper{border-bottom-color:var(--gray-3)}.dark-theme .user-info{background-color:var(--white-2);color:var(--black)}.dark-theme .info-row{border-color:var(--gray-3)}.dark-theme .n-switch{--n-rail-color: var(--white-1);--n-rail-color-active: var(--blue)}.dark-theme .n-card{background-color:var(--white-2);color:var(--black)}.dark-theme .n-card-header{border-bottom-color:var(--gray-3)}.dark-theme .n-modal,.dark-theme .n-drawer-content,.dark-theme .n-dialog{background-color:var(--white-2);color:var(--black)}.dark-theme .n-dialog__title{color:var(--black-contrast)}.dark-theme .n-popover,.dark-theme .n-dropdown-menu{background-color:var(--white-2);color:var(--black)}.dark-theme .n-dropdown-option{color:var(--black)}.dark-theme .n-dropdown-option:hover{background-color:var(--gray-3)}.dark-theme .n-button--ghost{border-color:var(--gray-3);color:var(--black)}.dark-theme .n-button--ghost:hover{border-color:var(--blue);color:var(--blue)}.dark-theme .n-button--text{color:var(--blue)}.dark-theme .layout-container{background-color:var(--creamy-white-3)}.dark-theme .login-form-ipt,.dark-theme .register-form-ipt{background-color:var(--white-1);color:var(--black)}.dark-theme .notification-table .n-data-table-th{background-color:var(--white-1);color:var(--black-contrast)}.dark-theme .notification-table .n-data-table-td{background-color:#2c2c2c;color:var(--black-contrast)}.dark-theme .notification-table .n-data-table__pagination{background-color:var(--creamy-white-2)}.dark-theme .notification-container .notification-btn{color:var(--black)}.dark-theme .n-badge .n-badge-sup{background-color:var(--red);color:var(--black-contrast)}.dark-theme .n-notification{background-color:var(--white-2);color:var(--black);border:1px solid var(--gray-3)}.dark-theme .n-notification .n-notification-main .n-notification-main__content{color:var(--black)}.dark-theme .n-notification .n-notification-main .n-notification-main__title{color:var(--blue)}.dark-theme .n-notification .n-notification-main .n-notification-main__avatar .n-avatar{border:1px solid var(--gray-3)}.dark-theme .n-notification .n-notification-main .n-notification-main__action .n-button{color:var(--blue)}.dark-theme .n-notification .n-notification-main .n-notification-main__close{color:var(--gray-5)}.dark-theme .n-notification .n-notification-main .n-notification-main__close:hover{color:var(--black)}:root{transition:background-color .1s ease-out,color .1s ease-out}.search-container,.n-input,.user-info-group,.user-info,.avatar-container,.n-dropdown-menu,.notification-container{transition:none!important}.comment-info-container,.comment-list-container,.user-comment-container,.user-comment-container-fixed,.comment-content-row,.comment-input-row,.comment-reply-row,.tiptap-editor-wrapper,.editor-content,.ProseMirror,.ProseMirrorInput,.article-content,.article-info-container,.tiptap-fullscreen{transition:none!important;will-change:background-color,color}[data-theme=dark]{--comment-info-bg: var(--white-1);--comment-list-bg: var(--white-1);--comment-container-bg: var(--white-2);--comment-fixed-bg: var(--blue-light);--comment-input-bg: var(--white-1);--comment-reply-bg: var(--white-2);--comment-reply-btn-bg: var(--white-2);--comment-fixed-reply-bg: var(--blue-light);--comment-fixed-reply-btn-bg: var(--blue-light);--prosemirror-input-bg: var(--white-2);--comment-input-prosemirror-bg: var(--white-1);--comment-reply-prosemirror-bg: var(--white-2);--comment-fixed-prosemirror-bg: var(--blue-light)}[data-theme=light]{--comment-info-bg: var(--creamy-white-1);--comment-list-bg: var(--creamy-white-1);--comment-container-bg: var(--white-1);--comment-fixed-bg: var(--blue-light);--comment-input-bg: var(--creamy-white-1);--comment-reply-bg: var(--white-1);--comment-reply-btn-bg: var(--white-1);--comment-fixed-reply-bg: var(--blue-light);--comment-fixed-reply-btn-bg: var(--blue-light);--prosemirror-input-bg: var(--white-1);--comment-input-prosemirror-bg: var(--creamy-white-1);--comment-reply-prosemirror-bg: var(--white-1);--comment-fixed-prosemirror-bg: var(--blue-light)}[data-theme] .comment-info-container{background-color:var(--comment-info-bg)}[data-theme] .comment-list-container{background-color:var(--comment-list-bg)}[data-theme] .user-comment-container{background-color:var(--comment-container-bg)}[data-theme] .user-comment-container-fixed,[data-theme] .comment-flash{background-color:var(--comment-fixed-bg);opacity:1}[data-theme] .comment-content-row .ProseMirror,[data-theme] .comment-input-row .ProseMirror,[data-theme] .comment-reply-row .ProseMirror{background-color:inherit;color:var(--black)}[data-theme] .user-comment-container .comment-reply-row{background-color:var(--comment-reply-bg)}[data-theme] .user-comment-container .comment-reply-send-btn{background-color:var(--comment-reply-btn-bg)}[data-theme] .user-comment-container .comment-reply-row .tiptap-editor-wrapper,[data-theme] .user-comment-container .comment-reply-row .editor-content{background-color:var(--comment-reply-bg)}[data-theme] .user-comment-container-fixed .comment-reply-row,[data-theme] .comment-flash .comment-reply-row,[data-theme] .user-comment-container-fixed .comment-reply-send-btn,[data-theme] .comment-flash .comment-reply-send-btn,[data-theme] .user-comment-container-fixed .comment-reply-row .tiptap-editor-wrapper,[data-theme] .user-comment-container-fixed .comment-reply-row .editor-content,[data-theme] .comment-flash .comment-reply-row .tiptap-editor-wrapper,[data-theme] .comment-flash .comment-reply-row .editor-content{background-color:var(--comment-fixed-reply-bg);opacity:1}[data-theme] .user-comment-container-fixed .comment-reply-row .ProseMirrorInput,[data-theme] .comment-flash .comment-reply-row .ProseMirrorInput{background-color:var(--comment-fixed-prosemirror-bg);border-color:var(--gray-3)}[data-theme] .user-comment-container .comment-reply-row .ProseMirrorInput{background-color:var(--comment-reply-prosemirror-bg);border-color:var(--gray-3)}[data-theme] .comment-input-row{background-color:var(--comment-input-bg)}[data-theme] .comment-input-row .tiptap-editor-wrapper,[data-theme] .comment-input-row .editor-content,[data-theme] .comment-input-row .ProseMirrorInput{background-color:var(--comment-input-prosemirror-bg)}[data-theme=light] .user-comment-container .tiptap-editor-wrapper,[data-theme=light] .user-comment-container .editor-content,[data-theme=light] .user-comment-container .ProseMirror,[data-theme=light] .user-comment-container p,[data-theme=light] .user-comment-container blockquote,[data-theme=light] .user-comment-container h1,[data-theme=light] .user-comment-container h2,[data-theme=light] .user-comment-container h3,[data-theme=light] .user-comment-container h4,[data-theme=light] .user-comment-container h5,[data-theme=light] .user-comment-container h6{background-color:var(--comment-container-bg)}.card-item{transition:transform .3s ease,box-shadow .3s ease}.card-item:hover{transform:translateY(-.6rem);box-shadow:var(--shadow)}.theme-toggle-scene *{transition:none!important}.article-container,.comment-container{width:100%!important;overflow:hidden!important}
