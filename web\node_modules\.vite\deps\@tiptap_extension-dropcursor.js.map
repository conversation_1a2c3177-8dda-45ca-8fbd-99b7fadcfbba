{"version": 3, "sources": ["../../@tiptap/pm/dropcursor/dist/index.js", "../../prosemirror-dropcursor/dist/index.js", "../../@tiptap/extension-dropcursor/src/dropcursor.ts"], "sourcesContent": ["// dropcursor/index.ts\nexport * from \"prosemirror-dropcursor\";\n", "import { Plugin } from 'prosemirror-state';\nimport { dropPoint } from 'prosemirror-transform';\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        let editorDOM = this.editorView.dom, editorRect = editorDOM.getBoundingClientRect();\n        let scaleX = editorRect.width / editorDOM.offsetWidth, scaleY = editorRect.height / editorDOM.offsetHeight;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    let halfWidth = (this.width / 2) * scaleY;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - halfWidth, bottom: top + halfWidth };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            let halfWidth = (this.width / 2) * scaleX;\n            rect = { left: coords.left - halfWidth, right: coords.left + halfWidth, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            let parentScaleX = rect.width / parent.offsetWidth, parentScaleY = rect.height / parent.offsetHeight;\n            parentLeft = rect.left - parent.scrollLeft * parentScaleX;\n            parentTop = rect.top - parent.scrollTop * parentScaleY;\n        }\n        this.element.style.left = (rect.left - parentLeft) / scaleX + \"px\";\n        this.element.style.top = (rect.top - parentTop) / scaleY + \"px\";\n        this.element.style.width = (rect.right - rect.left) / scaleX + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) / scaleY + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\"\n            ? disableDropCursor(this.editorView, pos, event)\n            : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = dropPoint(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (!this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\nexport { dropCursor };\n", "import { Extension } from '@tiptap/core'\nimport { dropCursor } from '@tiptap/pm/dropcursor'\n\nexport interface DropcursorOptions {\n  /**\n   * The color of the drop cursor\n   * @default 'currentColor'\n   * @example 'red'\n   */\n  color: string | undefined,\n\n  /**\n   * The width of the drop cursor\n   * @default 1\n   * @example 2\n  */\n  width: number | undefined,\n\n  /**\n   * The class of the drop cursor\n   * @default undefined\n   * @example 'drop-cursor'\n  */\n  class: string | undefined,\n}\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nexport const Dropcursor = Extension.create<DropcursorOptions>({\n  name: 'dropCursor',\n\n  addOptions() {\n    return {\n      color: 'currentColor',\n      width: 1,\n      class: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      dropCursor(this.options),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAaA,SAAS,WAAW,UAAU,CAAC,GAAG;AAC9B,SAAO,IAAI,OAAO;AAAA,IACd,KAAK,YAAY;AAAE,aAAO,IAAI,eAAe,YAAY,OAAO;AAAA,IAAG;AAAA,EACvE,CAAC;AACL;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,YAAY,SAAS;AAC7B,QAAI;AACJ,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,KAAK;AACnE,SAAK,QAAQ,QAAQ,UAAU,QAAQ,SAAa,QAAQ,SAAS;AACrE,SAAK,QAAQ,QAAQ;AACrB,SAAK,WAAW,CAAC,YAAY,WAAW,QAAQ,WAAW,EAAE,IAAI,UAAQ;AACrE,UAAI,UAAU,CAAC,MAAM;AAAE,aAAK,IAAI,EAAE,CAAC;AAAA,MAAG;AACtC,iBAAW,IAAI,iBAAiB,MAAM,OAAO;AAC7C,aAAO,EAAE,MAAM,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACL;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ,CAAC,EAAE,MAAM,QAAQ,MAAM,KAAK,WAAW,IAAI,oBAAoB,MAAM,OAAO,CAAC;AAAA,EACvG;AAAA,EACA,OAAO,YAAY,WAAW;AAC1B,QAAI,KAAK,aAAa,QAAQ,UAAU,OAAO,WAAW,MAAM,KAAK;AACjE,UAAI,KAAK,YAAY,WAAW,MAAM,IAAI,QAAQ;AAC9C,aAAK,UAAU,IAAI;AAAA;AAEnB,aAAK,cAAc;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,UAAU,KAAK;AACX,QAAI,OAAO,KAAK;AACZ;AACJ,SAAK,YAAY;AACjB,QAAI,OAAO,MAAM;AACb,WAAK,QAAQ,WAAW,YAAY,KAAK,OAAO;AAChD,WAAK,UAAU;AAAA,IACnB,OACK;AACD,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,QAAI,OAAO,KAAK,WAAW,MAAM,IAAI,QAAQ,KAAK,SAAS;AAC3D,QAAI,UAAU,CAAC,KAAK,OAAO,eAAe;AAC1C,QAAI,YAAY,KAAK,WAAW,KAAK,aAAa,UAAU,sBAAsB;AAClF,QAAI,SAAS,WAAW,QAAQ,UAAU,aAAa,SAAS,WAAW,SAAS,UAAU;AAC9F,QAAI,SAAS;AACT,UAAI,SAAS,KAAK,YAAY,QAAQ,KAAK;AAC3C,UAAI,UAAU,OAAO;AACjB,YAAI,OAAO,KAAK,WAAW,QAAQ,KAAK,aAAa,SAAS,OAAO,WAAW,EAAE;AAClF,YAAI,MAAM;AACN,cAAI,WAAW,KAAK,sBAAsB;AAC1C,cAAI,MAAM,SAAS,SAAS,SAAS,SAAS;AAC9C,cAAI,UAAU;AACV,mBAAO,MAAM,KAAK,WAAW,QAAQ,KAAK,SAAS,EAAE,sBAAsB,EAAE,OAAO;AACxF,cAAI,YAAa,KAAK,QAAQ,IAAK;AACnC,iBAAO,EAAE,MAAM,SAAS,MAAM,OAAO,SAAS,OAAO,KAAK,MAAM,WAAW,QAAQ,MAAM,UAAU;AAAA,QACvG;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,MAAM;AACP,UAAI,SAAS,KAAK,WAAW,YAAY,KAAK,SAAS;AACvD,UAAI,YAAa,KAAK,QAAQ,IAAK;AACnC,aAAO,EAAE,MAAM,OAAO,OAAO,WAAW,OAAO,OAAO,OAAO,WAAW,KAAK,OAAO,KAAK,QAAQ,OAAO,OAAO;AAAA,IACnH;AACA,QAAI,SAAS,KAAK,WAAW,IAAI;AACjC,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,UAAU,OAAO,YAAY,SAAS,cAAc,KAAK,CAAC;AAC/D,UAAI,KAAK;AACL,aAAK,QAAQ,YAAY,KAAK;AAClC,WAAK,QAAQ,MAAM,UAAU;AAC7B,UAAI,KAAK,OAAO;AACZ,aAAK,QAAQ,MAAM,kBAAkB,KAAK;AAAA,MAC9C;AAAA,IACJ;AACA,SAAK,QAAQ,UAAU,OAAO,gCAAgC,OAAO;AACrE,SAAK,QAAQ,UAAU,OAAO,iCAAiC,CAAC,OAAO;AACvE,QAAI,YAAY;AAChB,QAAI,CAAC,UAAU,UAAU,SAAS,QAAQ,iBAAiB,MAAM,EAAE,YAAY,UAAU;AACrF,mBAAa,CAAC;AACd,kBAAY,CAAC;AAAA,IACjB,OACK;AACD,UAAIC,QAAO,OAAO,sBAAsB;AACxC,UAAI,eAAeA,MAAK,QAAQ,OAAO,aAAa,eAAeA,MAAK,SAAS,OAAO;AACxF,mBAAaA,MAAK,OAAO,OAAO,aAAa;AAC7C,kBAAYA,MAAK,MAAM,OAAO,YAAY;AAAA,IAC9C;AACA,SAAK,QAAQ,MAAM,QAAQ,KAAK,OAAO,cAAc,SAAS;AAC9D,SAAK,QAAQ,MAAM,OAAO,KAAK,MAAM,aAAa,SAAS;AAC3D,SAAK,QAAQ,MAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS;AAC/D,SAAK,QAAQ,MAAM,UAAU,KAAK,SAAS,KAAK,OAAO,SAAS;AAAA,EACpE;AAAA,EACA,gBAAgB,SAAS;AACrB,iBAAa,KAAK,OAAO;AACzB,SAAK,UAAU,WAAW,MAAM,KAAK,UAAU,IAAI,GAAG,OAAO;AAAA,EACjE;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,CAAC,KAAK,WAAW;AACjB;AACJ,QAAI,MAAM,KAAK,WAAW,YAAY,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ,CAAC;AACjF,QAAI,OAAO,OAAO,IAAI,UAAU,KAAK,KAAK,WAAW,MAAM,IAAI,OAAO,IAAI,MAAM;AAChF,QAAI,oBAAoB,QAAQ,KAAK,KAAK,KAAK;AAC/C,QAAI,WAAW,OAAO,qBAAqB,aACrC,kBAAkB,KAAK,YAAY,KAAK,KAAK,IAC7C;AACN,QAAI,OAAO,CAAC,UAAU;AAClB,UAAI,SAAS,IAAI;AACjB,UAAI,KAAK,WAAW,YAAY,KAAK,WAAW,SAAS,OAAO;AAC5D,YAAI,QAAQ,UAAU,KAAK,WAAW,MAAM,KAAK,QAAQ,KAAK,WAAW,SAAS,KAAK;AACvF,YAAI,SAAS;AACT,mBAAS;AAAA,MACjB;AACA,WAAK,UAAU,MAAM;AACrB,WAAK,gBAAgB,GAAI;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,gBAAgB,EAAE;AAAA,EAC3B;AAAA,EACA,OAAO;AACH,SAAK,gBAAgB,EAAE;AAAA,EAC3B;AAAA,EACA,UAAU,OAAO;AACb,QAAI,CAAC,KAAK,WAAW,IAAI,SAAS,MAAM,aAAa;AACjD,WAAK,UAAU,IAAI;AAAA,EAC3B;AACJ;;;AC/Ga,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO;MACP,OAAO;MACP,OAAO;;;EAIX,wBAAqB;AACnB,WAAO;MACL,WAAW,KAAK,OAAO;;;AAG5B,CAAA;", "names": ["import_dist", "import_dist", "rect"]}