{"version": 3, "sources": ["../../@tiptap/pm/gapcursor/dist/index.js", "../../prosemirror-gapcursor/dist/index.js", "../../@tiptap/extension-gapcursor/src/gapcursor.ts"], "sourcesContent": ["// gapcursor/index.ts\nexport * from \"prosemirror-gapcursor\";\n", "import { keydown<PERSON><PERSON><PERSON> } from 'prosemirror-keymap';\nimport { Selection, NodeSelection, TextSelection, Plugin } from 'prosemirror-state';\nimport { Slice, Fragment } from 'prosemirror-model';\nimport { DecorationSet, Decoration } from 'prosemirror-view';\n\n/**\nGap cursor selections are represented using this class. Its\n`$anchor` and `$head` properties both point at the cursor position.\n*/\nclass GapCursor extends Selection {\n    /**\n    Create a gap cursor.\n    */\n    constructor($pos) {\n        super($pos, $pos);\n    }\n    map(doc, mapping) {\n        let $pos = doc.resolve(mapping.map(this.head));\n        return GapCursor.valid($pos) ? new GapCursor($pos) : Selection.near($pos);\n    }\n    content() { return Slice.empty; }\n    eq(other) {\n        return other instanceof GapCursor && other.head == this.head;\n    }\n    toJSON() {\n        return { type: \"gapcursor\", pos: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for GapCursor.fromJSON\");\n        return new GapCursor(doc.resolve(json.pos));\n    }\n    /**\n    @internal\n    */\n    getBookmark() { return new GapBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static valid($pos) {\n        let parent = $pos.parent;\n        if (parent.isTextblock || !closedBefore($pos) || !closedAfter($pos))\n            return false;\n        let override = parent.type.spec.allowGapCursor;\n        if (override != null)\n            return override;\n        let deflt = parent.contentMatchAt($pos.index()).defaultType;\n        return deflt && deflt.isTextblock;\n    }\n    /**\n    @internal\n    */\n    static findGapCursorFrom($pos, dir, mustMove = false) {\n        search: for (;;) {\n            if (!mustMove && GapCursor.valid($pos))\n                return $pos;\n            let pos = $pos.pos, next = null;\n            // Scan up from this position\n            for (let d = $pos.depth;; d--) {\n                let parent = $pos.node(d);\n                if (dir > 0 ? $pos.indexAfter(d) < parent.childCount : $pos.index(d) > 0) {\n                    next = parent.child(dir > 0 ? $pos.indexAfter(d) : $pos.index(d) - 1);\n                    break;\n                }\n                else if (d == 0) {\n                    return null;\n                }\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            // And then down into the next node\n            for (;;) {\n                let inside = dir > 0 ? next.firstChild : next.lastChild;\n                if (!inside) {\n                    if (next.isAtom && !next.isText && !NodeSelection.isSelectable(next)) {\n                        $pos = $pos.doc.resolve(pos + next.nodeSize * dir);\n                        mustMove = false;\n                        continue search;\n                    }\n                    break;\n                }\n                next = inside;\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            return null;\n        }\n    }\n}\nGapCursor.prototype.visible = false;\nGapCursor.findFrom = GapCursor.findGapCursorFrom;\nSelection.jsonID(\"gapcursor\", GapCursor);\nclass GapBookmark {\n    constructor(pos) {\n        this.pos = pos;\n    }\n    map(mapping) {\n        return new GapBookmark(mapping.map(this.pos));\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.pos);\n        return GapCursor.valid($pos) ? new GapCursor($pos) : Selection.near($pos);\n    }\n}\nfunction closedBefore($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.index(d), parent = $pos.node(d);\n        // At the start of this parent, look at next one\n        if (index == 0) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        // See if the node before (or its first ancestor) is closed\n        for (let before = parent.child(index - 1);; before = before.lastChild) {\n            if ((before.childCount == 0 && !before.inlineContent) || before.isAtom || before.type.spec.isolating)\n                return true;\n            if (before.inlineContent)\n                return false;\n        }\n    }\n    // Hit start of document\n    return true;\n}\nfunction closedAfter($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.indexAfter(d), parent = $pos.node(d);\n        if (index == parent.childCount) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        for (let after = parent.child(index);; after = after.firstChild) {\n            if ((after.childCount == 0 && !after.inlineContent) || after.isAtom || after.type.spec.isolating)\n                return true;\n            if (after.inlineContent)\n                return false;\n        }\n    }\n    return true;\n}\n\n/**\nCreate a gap cursor plugin. When enabled, this will capture clicks\nnear and arrow-key-motion past places that don't have a normally\nselectable position nearby, and create a gap cursor selection for\nthem. The cursor is drawn as an element with class\n`ProseMirror-gapcursor`. You can either include\n`style/gapcursor.css` from the package's directory or add your own\nstyles to make it visible.\n*/\nfunction gapCursor() {\n    return new Plugin({\n        props: {\n            decorations: drawGapCursor,\n            createSelectionBetween(_view, $anchor, $head) {\n                return $anchor.pos == $head.pos && GapCursor.valid($head) ? new GapCursor($head) : null;\n            },\n            handleClick,\n            handleKeyDown,\n            handleDOMEvents: { beforeinput: beforeinput }\n        }\n    });\n}\nconst handleKeyDown = keydownHandler({\n    \"ArrowLeft\": arrow(\"horiz\", -1),\n    \"ArrowRight\": arrow(\"horiz\", 1),\n    \"ArrowUp\": arrow(\"vert\", -1),\n    \"ArrowDown\": arrow(\"vert\", 1)\n});\nfunction arrow(axis, dir) {\n    const dirStr = axis == \"vert\" ? (dir > 0 ? \"down\" : \"up\") : (dir > 0 ? \"right\" : \"left\");\n    return function (state, dispatch, view) {\n        let sel = state.selection;\n        let $start = dir > 0 ? sel.$to : sel.$from, mustMove = sel.empty;\n        if (sel instanceof TextSelection) {\n            if (!view.endOfTextblock(dirStr) || $start.depth == 0)\n                return false;\n            mustMove = false;\n            $start = state.doc.resolve(dir > 0 ? $start.after() : $start.before());\n        }\n        let $found = GapCursor.findGapCursorFrom($start, dir, mustMove);\n        if (!$found)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(new GapCursor($found)));\n        return true;\n    };\n}\nfunction handleClick(view, pos, event) {\n    if (!view || !view.editable)\n        return false;\n    let $pos = view.state.doc.resolve(pos);\n    if (!GapCursor.valid($pos))\n        return false;\n    let clickPos = view.posAtCoords({ left: event.clientX, top: event.clientY });\n    if (clickPos && clickPos.inside > -1 && NodeSelection.isSelectable(view.state.doc.nodeAt(clickPos.inside)))\n        return false;\n    view.dispatch(view.state.tr.setSelection(new GapCursor($pos)));\n    return true;\n}\n// This is a hack that, when a composition starts while a gap cursor\n// is active, quickly creates an inline context for the composition to\n// happen in, to avoid it being aborted by the DOM selection being\n// moved into a valid position.\nfunction beforeinput(view, event) {\n    if (event.inputType != \"insertCompositionText\" || !(view.state.selection instanceof GapCursor))\n        return false;\n    let { $from } = view.state.selection;\n    let insert = $from.parent.contentMatchAt($from.index()).findWrapping(view.state.schema.nodes.text);\n    if (!insert)\n        return false;\n    let frag = Fragment.empty;\n    for (let i = insert.length - 1; i >= 0; i--)\n        frag = Fragment.from(insert[i].createAndFill(null, frag));\n    let tr = view.state.tr.replace($from.pos, $from.pos, new Slice(frag, 0, 0));\n    tr.setSelection(TextSelection.near(tr.doc.resolve($from.pos + 1)));\n    view.dispatch(tr);\n    return false;\n}\nfunction drawGapCursor(state) {\n    if (!(state.selection instanceof GapCursor))\n        return null;\n    let node = document.createElement(\"div\");\n    node.className = \"ProseMirror-gapcursor\";\n    return DecorationSet.create(state.doc, [Decoration.widget(state.selection.head, node, { key: \"gapcursor\" })]);\n}\n\nexport { GapCursor, gapCursor };\n", "import {\n  callOrReturn,\n  Extension,\n  getExtensionField,\n  ParentConfig,\n} from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n        name: string,\n        options: Options,\n        storage: Storage,\n        parent: ParentConfig<NodeConfig<Options>>['allowGapCursor'],\n      }) => boolean | null),\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [\n      gapCursor(),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AASA,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,MAAM;AACd,UAAM,MAAM,IAAI;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,SAAS;AACd,QAAI,OAAO,IAAI,QAAQ,QAAQ,IAAI,KAAK,IAAI,CAAC;AAC7C,WAAO,WAAU,MAAM,IAAI,IAAI,IAAI,WAAU,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,EAC5E;AAAA,EACA,UAAU;AAAE,WAAO,MAAM;AAAA,EAAO;AAAA,EAChC,GAAG,OAAO;AACN,WAAO,iBAAiB,cAAa,MAAM,QAAQ,KAAK;AAAA,EAC5D;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,aAAa,KAAK,KAAK,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,KAAK,MAAM;AACvB,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,sCAAsC;AAC/D,WAAO,IAAI,WAAU,IAAI,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAE,WAAO,IAAI,YAAY,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIrD,OAAO,MAAM,MAAM;AACf,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,eAAe,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,IAAI;AAC9D,aAAO;AACX,QAAI,WAAW,OAAO,KAAK,KAAK;AAChC,QAAI,YAAY;AACZ,aAAO;AACX,QAAI,QAAQ,OAAO,eAAe,KAAK,MAAM,CAAC,EAAE;AAChD,WAAO,SAAS,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,kBAAkB,MAAM,KAAK,WAAW,OAAO;AAClD,WAAQ,YAAS;AACb,UAAI,CAAC,YAAY,WAAU,MAAM,IAAI;AACjC,eAAO;AACX,UAAI,MAAM,KAAK,KAAK,OAAO;AAE3B,eAAS,IAAI,KAAK,SAAQ,KAAK;AAC3B,YAAI,SAAS,KAAK,KAAK,CAAC;AACxB,YAAI,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,OAAO,aAAa,KAAK,MAAM,CAAC,IAAI,GAAG;AACtE,iBAAO,OAAO,MAAM,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;AACpE;AAAA,QACJ,WACS,KAAK,GAAG;AACb,iBAAO;AAAA,QACX;AACA,eAAO;AACP,YAAI,OAAO,KAAK,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAU,MAAM,IAAI;AACpB,iBAAO;AAAA,MACf;AAEA,iBAAS;AACL,YAAI,SAAS,MAAM,IAAI,KAAK,aAAa,KAAK;AAC9C,YAAI,CAAC,QAAQ;AACT,cAAI,KAAK,UAAU,CAAC,KAAK,UAAU,CAAC,cAAc,aAAa,IAAI,GAAG;AAClE,mBAAO,KAAK,IAAI,QAAQ,MAAM,KAAK,WAAW,GAAG;AACjD,uBAAW;AACX,qBAAS;AAAA,UACb;AACA;AAAA,QACJ;AACA,eAAO;AACP,eAAO;AACP,YAAI,OAAO,KAAK,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAU,MAAM,IAAI;AACpB,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,UAAU,UAAU,UAAU;AAC9B,UAAU,WAAW,UAAU;AAC/B,UAAU,OAAO,aAAa,SAAS;AACvC,IAAM,cAAN,MAAM,aAAY;AAAA,EACd,YAAY,KAAK;AACb,SAAK,MAAM;AAAA,EACf;AAAA,EACA,IAAI,SAAS;AACT,WAAO,IAAI,aAAY,QAAQ,IAAI,KAAK,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,IAAI,QAAQ,KAAK,GAAG;AAC/B,WAAO,UAAU,MAAM,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,EAC5E;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,WAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,QAAI,QAAQ,KAAK,MAAM,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC;AAE/C,QAAI,SAAS,GAAG;AACZ,UAAI,OAAO,KAAK,KAAK;AACjB,eAAO;AACX;AAAA,IACJ;AAEA,aAAS,SAAS,OAAO,MAAM,QAAQ,CAAC,KAAI,SAAS,OAAO,WAAW;AACnE,UAAK,OAAO,cAAc,KAAK,CAAC,OAAO,iBAAkB,OAAO,UAAU,OAAO,KAAK,KAAK;AACvF,eAAO;AACX,UAAI,OAAO;AACP,eAAO;AAAA,IACf;AAAA,EACJ;AAEA,SAAO;AACX;AACA,SAAS,YAAY,MAAM;AACvB,WAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,QAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC;AACpD,QAAI,SAAS,OAAO,YAAY;AAC5B,UAAI,OAAO,KAAK,KAAK;AACjB,eAAO;AACX;AAAA,IACJ;AACA,aAAS,QAAQ,OAAO,MAAM,KAAK,KAAI,QAAQ,MAAM,YAAY;AAC7D,UAAK,MAAM,cAAc,KAAK,CAAC,MAAM,iBAAkB,MAAM,UAAU,MAAM,KAAK,KAAK;AACnF,eAAO;AACX,UAAI,MAAM;AACN,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAWA,SAAS,YAAY;AACjB,SAAO,IAAI,OAAO;AAAA,IACd,OAAO;AAAA,MACH,aAAa;AAAA,MACb,uBAAuB,OAAO,SAAS,OAAO;AAC1C,eAAO,QAAQ,OAAO,MAAM,OAAO,UAAU,MAAM,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,MACvF;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,EAAE,YAAyB;AAAA,IAChD;AAAA,EACJ,CAAC;AACL;AACA,IAAM,gBAAgB,eAAe;AAAA,EACjC,aAAa,MAAM,SAAS,EAAE;AAAA,EAC9B,cAAc,MAAM,SAAS,CAAC;AAAA,EAC9B,WAAW,MAAM,QAAQ,EAAE;AAAA,EAC3B,aAAa,MAAM,QAAQ,CAAC;AAChC,CAAC;AACD,SAAS,MAAM,MAAM,KAAK;AACtB,QAAM,SAAS,QAAQ,SAAU,MAAM,IAAI,SAAS,OAAS,MAAM,IAAI,UAAU;AACjF,SAAO,SAAU,OAAO,UAAU,MAAM;AACpC,QAAI,MAAM,MAAM;AAChB,QAAI,SAAS,MAAM,IAAI,IAAI,MAAM,IAAI,OAAO,WAAW,IAAI;AAC3D,QAAI,eAAe,eAAe;AAC9B,UAAI,CAAC,KAAK,eAAe,MAAM,KAAK,OAAO,SAAS;AAChD,eAAO;AACX,iBAAW;AACX,eAAS,MAAM,IAAI,QAAQ,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC;AAAA,IACzE;AACA,QAAI,SAAS,UAAU,kBAAkB,QAAQ,KAAK,QAAQ;AAC9D,QAAI,CAAC;AACD,aAAO;AACX,QAAI;AACA,eAAS,MAAM,GAAG,aAAa,IAAI,UAAU,MAAM,CAAC,CAAC;AACzD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY,MAAM,KAAK,OAAO;AACnC,MAAI,CAAC,QAAQ,CAAC,KAAK;AACf,WAAO;AACX,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,GAAG;AACrC,MAAI,CAAC,UAAU,MAAM,IAAI;AACrB,WAAO;AACX,MAAI,WAAW,KAAK,YAAY,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ,CAAC;AAC3E,MAAI,YAAY,SAAS,SAAS,MAAM,cAAc,aAAa,KAAK,MAAM,IAAI,OAAO,SAAS,MAAM,CAAC;AACrG,WAAO;AACX,OAAK,SAAS,KAAK,MAAM,GAAG,aAAa,IAAI,UAAU,IAAI,CAAC,CAAC;AAC7D,SAAO;AACX;AAKA,SAAS,YAAY,MAAM,OAAO;AAC9B,MAAI,MAAM,aAAa,2BAA2B,EAAE,KAAK,MAAM,qBAAqB;AAChF,WAAO;AACX,MAAI,EAAE,MAAM,IAAI,KAAK,MAAM;AAC3B,MAAI,SAAS,MAAM,OAAO,eAAe,MAAM,MAAM,CAAC,EAAE,aAAa,KAAK,MAAM,OAAO,MAAM,IAAI;AACjG,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,SAAS;AACpB,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AACpC,WAAO,SAAS,KAAK,OAAO,CAAC,EAAE,cAAc,MAAM,IAAI,CAAC;AAC5D,MAAI,KAAK,KAAK,MAAM,GAAG,QAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC;AAC1E,KAAG,aAAa,cAAc,KAAK,GAAG,IAAI,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AACjE,OAAK,SAAS,EAAE;AAChB,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,EAAE,MAAM,qBAAqB;AAC7B,WAAO;AACX,MAAI,OAAO,SAAS,cAAc,KAAK;AACvC,OAAK,YAAY;AACjB,SAAO,cAAc,OAAO,MAAM,KAAK,CAAC,WAAW,OAAO,MAAM,UAAU,MAAM,MAAM,EAAE,KAAK,YAAY,CAAC,CAAC,CAAC;AAChH;;;ACzMa,IAAA,YAAY,UAAU,OAAO;EACxC,MAAM;EAEN,wBAAqB;AACnB,WAAO;MACL,UAAS;;;EAIb,iBAAiB,WAAS;;AACxB,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;;AAGrB,WAAO;MACL,iBAAgB,KAAA,aAAa,kBAAkB,WAAW,kBAAkB,OAAO,CAAC,OAAC,QAAA,OAAA,SAAA,KAAI;;;AAG9F,CAAA;", "names": ["import_dist", "import_dist"]}