// Mention菜单样式 - 与斜杠菜单保持一致
.mention-menu {
  white-space: nowrap;
  pointer-events: all;
  max-height: 15rem;
  padding: 0.25rem;
  overflow: hidden auto;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  background-color: var(--bg-color, white);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 10%),
    0 2px 4px -2px rgba(0, 0, 0, 10%);
  z-index: 10000;

  // 位置适应样式
  &.mention-menu-above {
    // 当菜单显示在光标上方时的样式
    transform-origin: bottom center;
    animation: slide-up-fade-in 0.15s ease-out;
  }

  &.mention-menu-below {
    // 当菜单显示在光标下方时的样式
    transform-origin: top center;
    animation: slide-down-fade-in 0.15s ease-out;
  }

  &-empty {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 0.875rem;
    height: 2.25rem;
    padding: 0 0.75rem;
    color: var(--text-muted, #6b7280);
  }

  // 按钮样式
  &-button {
    appearance: none;
    user-select: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    line-height: 1;
    font-weight: 500;
    font-size: 0.875rem;
    border: none;
    outline: none;
    width: 100%;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background-color: transparent;
    color: var(--text-color, #374151);
    transition: all 0.15s ease;

    &:hover,
    &:focus,
    &[data-active] {
      color: var(--text-active, #1f2937);
      background-color: var(--bg-hover, #f3f4f6);
    }

    &-icon {
      margin-left: 0.25rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      height: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        object-fit: cover;
      }

      .avatar-fallback {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        background-color: var(--purple, #6a00f5);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
      }
    }

    &-name {
      flex-grow: 1;
      text-align: start;
      color: var(--purple, #6a00f5);
      font-weight: 600;
    }
  }
}

// 暗色主题支持
.dark-theme {
  .mention-menu {
    --bg-color: #1f2937;
    --border-color: #374151;
    --text-color: #f9fafb;
    --text-active: #fff;
    --text-muted: #9ca3af;
    --bg-hover: #374151;

    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 30%),
      0 2px 4px -2px rgba(0, 0, 0, 20%);
  }
}

// 滚动条样式
.mention-menu::-webkit-scrollbar {
  width: 5px;
}

.mention-menu::-webkit-scrollbar-track {
  background: transparent;
}

.mention-menu::-webkit-scrollbar-thumb {
  background: var(--border-color, #e5e7eb);
  border-radius: 5px;
}

.mention-menu * {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color, #e5e7eb) transparent;
}

// 动画定义
@keyframes slide-down-fade-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-up-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
