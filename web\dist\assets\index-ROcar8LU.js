const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ArticleModal-DvIGHa1r.js","assets/pinia-C1NaKXqp.js","assets/@vue-C18CzuYV.js","assets/naive-ui-KoJsJaIm.js","assets/seemly-Cdi3gWV3.js","assets/vueuc-BKIYztcR.js","assets/evtd-hWw0KU7y.js","assets/@css-render-DcoOFqSU.js","assets/vooks-n2t0Q59N.js","assets/vdirs-Bvq7nEML.js","assets/@juggle-BnTvdTVm.js","assets/css-render-7x70jhNC.js","assets/@emotion-DFFAhID7.js","assets/lodash-es-BpE61GNB.js","assets/treemate-D3ikBJ7G.js","assets/async-validator-Bed4cEOw.js","assets/vite-plugin-node-polyfills-CgB-Lgxu.js","assets/date-fns-efAbghYp.js","assets/vue-router-CM3lNrYT.js","assets/axios-CF6-kBsv.js","assets/@tiptap-DTTLRpZe.js","assets/prosemirror-dropcursor-b1zulL1f.js","assets/prosemirror-state-Dg8eoqF5.js","assets/prosemirror-model-BwtArlLQ.js","assets/orderedmap-6uBVSRPO.js","assets/prosemirror-transform-2YHSeD6B.js","assets/prosemirror-view-D1p6KQzm.js","assets/prosemirror-gapcursor-De4VkbqI.js","assets/prosemirror-keymap-CxzETJ23.js","assets/w3c-keyname-DcELQ0J3.js","assets/prosemirror-history-B0l72feN.js","assets/rope-sequence-DIBo4VXF.js","assets/linkifyjs-CgqKPF1I.js","assets/tippy.js-Cq-jft6S.js","assets/@popperjs-B5AGR5A_.js","assets/prosemirror-commands-jNaZT4ky.js","assets/prosemirror-schema-list-DJ0wlwD0.js","assets/tiptap-markdown-B88K87IF.js","assets/prosemirror-markdown-BKJFDS0M.js","assets/markdown-it-DV9r8h9J.js","assets/mdurl-DbZ9s47_.js","assets/uc.micro-CRGj88R_.js","assets/entities-D_unbCD7.js","assets/linkify-it-DVBmImI5.js","assets/punycode.js-H98b6B6Y.js","assets/markdown-it-task-lists-Dj-XbLVy.js","assets/highlight.js-O_OqoeFy.js","assets/smooth-scroll-into-view-if-needed-B9OyoO8F.js","assets/scroll-into-view-if-needed-CoacbxIo.js","assets/compute-scroll-into-view-Cfyw3hb3.js","assets/lowlight-CRhSxQ06.js","assets/sockjs-client-CnUIS0PG.js","assets/url-parse-BJt2elfP.js","assets/requires-port-CgMabaHb.js","assets/querystringify-B2QvdZsH.js","assets/inherits-BfZYsuMB.js","assets/@stomp-ba8ZO4qr.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,a,o)=>((t,a,o)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[a]=o)(t,"symbol"!=typeof a?a+"":a,o);import{d as a,c as o}from"./pinia-C1NaKXqp.js";import{r as n,d as l,t as i,o as r,f as s,n as c,X as d,Y as u,Z as m,_ as p,$ as v,u as h,a0 as g,a1 as f,a2 as w,H as b,a3 as y,K as x,v as k,U as C,c as L,a4 as M,F as S,a5 as A,h as R,a as E,q as z,W as T,a6 as I,G as _,a7 as B,N as $,B as P,A as U,w as F,a8 as H,T as V,J as O,a9 as D,S as j,z as N,aa as q}from"./@vue-C18CzuYV.js";import{d as Y,N as J,a as W,b as K,c as G,e as X,f as Q,S as Z,g as ee,h as te,i as ae,B as oe,j as ne,k as le,l as ie,m as re,n as se,o as ce,p as de,q as ue,r as me,s as pe,t as ve,u as he,v as ge,w as fe,x as we,y as be,z as ye,A as xe,C as ke,D as Ce,E as Le,F as Me,G as Se,H as Ae,I as Re,J as Ee,K as ze,L as Te,M as Ie,O as _e,P as Be}from"./naive-ui-KoJsJaIm.js";import{u as $e,a as Pe,c as Ue,b as Fe}from"./vue-router-CM3lNrYT.js";import{a as He}from"./axios-CF6-kBsv.js";import{a as Ve,N as Oe,V as De,b as je,C as Ne,E as qe,I as Ye,c as Je,d as We,S as Ke,T as Ge,D as Xe,P as Qe,e as Ze,f as et,B as tt,h as at,j as ot,U as nt,k as lt,H as it,l as rt,O as st,L as ct,m as dt,n as ut,o as mt,p as pt,q as vt,r as ht,s as gt,t as ft,u as wt,F as bt,G as yt,v as xt,w as kt,x as Ct,y as Lt,z as Mt,A as St,J as At,K as Rt}from"./@tiptap-DTTLRpZe.js";import{M as Et}from"./tiptap-markdown-B88K87IF.js";import{S as zt,a as Tt,P as It}from"./prosemirror-state-Dg8eoqF5.js";import{a as _t,D as Bt}from"./prosemirror-view-D1p6KQzm.js";import{s as $t}from"./smooth-scroll-into-view-if-needed-B9OyoO8F.js";import{c as Pt,g as Ut}from"./lowlight-CRhSxQ06.js";import{S as Ft}from"./sockjs-client-CnUIS0PG.js";import{C as Ht}from"./@stomp-ba8ZO4qr.js";import"./seemly-Cdi3gWV3.js";import"./vueuc-BKIYztcR.js";import"./evtd-hWw0KU7y.js";import"./@css-render-DcoOFqSU.js";import"./vooks-n2t0Q59N.js";import"./vdirs-Bvq7nEML.js";import"./@juggle-BnTvdTVm.js";import"./css-render-7x70jhNC.js";import"./@emotion-DFFAhID7.js";import"./lodash-es-BpE61GNB.js";import"./treemate-D3ikBJ7G.js";import"./async-validator-Bed4cEOw.js";import"./vite-plugin-node-polyfills-CgB-Lgxu.js";import"./date-fns-efAbghYp.js";import"./prosemirror-dropcursor-b1zulL1f.js";import"./prosemirror-transform-2YHSeD6B.js";import"./prosemirror-model-BwtArlLQ.js";import"./orderedmap-6uBVSRPO.js";import"./prosemirror-gapcursor-De4VkbqI.js";import"./prosemirror-keymap-CxzETJ23.js";import"./w3c-keyname-DcELQ0J3.js";import"./prosemirror-history-B0l72feN.js";import"./rope-sequence-DIBo4VXF.js";import"./linkifyjs-CgqKPF1I.js";import"./tippy.js-Cq-jft6S.js";import"./@popperjs-B5AGR5A_.js";import"./prosemirror-commands-jNaZT4ky.js";import"./prosemirror-schema-list-DJ0wlwD0.js";import"./prosemirror-markdown-BKJFDS0M.js";import"./markdown-it-DV9r8h9J.js";import"./mdurl-DbZ9s47_.js";import"./uc.micro-CRGj88R_.js";import"./entities-D_unbCD7.js";import"./linkify-it-DVBmImI5.js";import"./punycode.js-H98b6B6Y.js";import"./markdown-it-task-lists-Dj-XbLVy.js";import"./highlight.js-O_OqoeFy.js";import"./scroll-into-view-if-needed-CoacbxIo.js";import"./compute-scroll-into-view-Cfyw3hb3.js";import"./url-parse-BJt2elfP.js";import"./requires-port-CgMabaHb.js";import"./querystringify-B2QvdZsH.js";import"./inherits-BfZYsuMB.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const Vt="loginUser",Ot="homeCard",Dt="homeSearchCondition",jt="localhost"==window.location.hostname?`${window.location.protocol}//localhost:20001`:`${window.location.protocol}//${window.location.host}/api`,Nt={logLevel:"debug",cloudflareTurnstileSecret:"1x00000000000000000000AA",backend:{baseURL:jt,wsURL:`${jt}/core/ws`,resourcePrefix:"/osr",resourceURL:`${jt}/osr`}},qt={DEBUG:"debug",INFO:"info",WARN:"warn",ERROR:"error"},Yt=Nt.logLevel;function Jt(e){const t=Object.values(qt),a=t.indexOf(Yt);return t.indexOf(e)>=a}const Wt={levels:qt,debug(e,...t){Jt(qt.DEBUG)},info(e,...t){Jt(qt.INFO)},warn(e,...t){Jt(qt.WARN)},error(e,...t){Jt(qt.ERROR)}};class Kt{static parse(e){if("string"!=typeof e)return Wt.warn("Non-string input to JSON.parse:",typeof e),null;if(!e||""===e.trim())return Wt.warn("Empty string passed to JSON.parse"),null;if(e.includes("@")){Wt.debug("Detected @ symbol in string, trying to sanitize");try{const t=/@([^:]+)\s*:\s*(\{.*\})/,a=e.match(t);if(a&&a[2]){const e=a[2];return JSON.parse(e)}const o=e.indexOf("{"),n=e.lastIndexOf("}");if(-1!==o&&-1!==n&&n>o){const t=e.substring(o,n+1);return JSON.parse(t)}}catch(t){Wt.debug("Failed to extract JSON from string with @ symbol:",t)}}try{return JSON.parse(e)}catch(t){return Wt.warn("Invalid JSON format:",t instanceof Error?t.message:"Unknown error"),Wt.debug("Problem JSON string:",e.length>100?e.substring(0,97)+"...":e),null}}static stringify(e,t=!1){try{return t?JSON.stringify(e,null,2):JSON.stringify(e)}catch(a){return Wt.error("JSON stringify error:",a),""}}static deepClone(e){return JSON.parse(JSON.stringify(e))}static isValidJson(e){try{return JSON.parse(e),!0}catch{return!1}}static getProperty(e,t){return t.split(".").reduce(((e,t)=>e?e[t]:void 0),e)}static setProperty(e,t,a){const o=t.split(".");let n=e;o.forEach(((e,t)=>{t===o.length-1?n[e]=a:(n[e]||(n[e]={}),n=n[e])}))}}class Gt{static set(e,t){const a=Kt.stringify(t);localStorage.setItem(e,a)}static get(e){const t=localStorage.getItem(e);return t?Kt.parse(t):null}static remove(e){localStorage.removeItem(e)}static clear(){localStorage.clear()}static getLoginUser(){return this.get(Vt)}static setLoginUser(e){this.set(Vt,e)}static removeLoginUser(){this.remove(Vt)}}const Xt="wen-theme";var Qt=(e=>(e.LIGHT="light",e.DARK="dark",e))(Qt||{});const Zt={light:null,dark:Y},ea=n("light"),ta=n(!1),aa={"--white":"#fff","--white-1":"#f0f0f0","--white-2":"#ddd","--creamy-white-1":"#eeece4","--creamy-white-2":"#e4e1d8","--creamy-white-3":"#dcd8ca","--black":"#2e2b29","--black-contrast":"#110f0e","--gray-1":"rgba(61, 37, 20, 0.05)","--gray-2":"rgba(61, 37, 20, 0.08)","--gray-3":"rgba(61, 37, 20, 0.12)","--gray-4":"rgba(53, 38, 28, 0.3)","--gray-5":"rgba(28, 25, 23, 0.6)","--blue":"#4ba3fd","--blue-light":"#e6f3ff","--shadow":"0 0.25rem 0.6rem rgba(0, 0, 0, 0.1)","--code-comment":"#6a737d","--code-keyword":"#d73a49","--code-string":"#032f62","--code-number":"#005cc5","--code-function":"#6f42c1","--code-variable":"#005cc5","--code-tag":"#22863a","--code-attribute":"#22863a","--code-builtin":"#6f42c1","--code-meta":"#6a737d","--code-deletion-bg":"#ffeef0","--code-deletion-color":"#b31d28","--code-addition-bg":"#f0fff4","--code-addition-color":"#22863a","--code-text":"#24292e"},oa={"--white":"#121212","--white-1":"#242424","--white-2":"#363636","--creamy-white-1":"#1a1a1a","--creamy-white-2":"#262626","--creamy-white-3":"#333333","--black":"#e0e0e0","--black-contrast":"#ffffff","--gray-1":"rgba(200, 200, 200, 0.05)","--gray-2":"rgba(200, 200, 200, 0.08)","--gray-3":"rgba(200, 200, 200, 0.12)","--gray-4":"rgba(200, 200, 200, 0.3)","--gray-5":"rgba(200, 200, 200, 0.6)","--blue":"#4ba3fd","--blue-light":"#2a3745","--shadow":"0 0.25rem 0.6rem rgba(255, 255, 255, 0.1)","--code-comment":"#8b949e","--code-keyword":"#ff7b72","--code-string":"#a5d6ff","--code-number":"#79c0ff","--code-function":"#d2a8ff","--code-variable":"#79c0ff","--code-tag":"#7ee787","--code-attribute":"#7ee787","--code-builtin":"#d2a8ff","--code-meta":"#8b949e","--code-deletion-bg":"#490202","--code-deletion-color":"#ffdcd7","--code-addition-bg":"#033a16","--code-addition-color":"#aff5b4","--code-text":"#e6edf3"},na=e=>{const t=document.documentElement,a="light"===e?aa:oa,o="dark"===e;for(const[n,l]of Object.entries(a))t.style.setProperty(n,l);t.setAttribute("data-theme",e),o?t.classList.add("dark-theme"):t.classList.remove("dark-theme")},la=e=>{[".search-container",".n-input",".user-info-group",".user-info",".avatar-container",".n-popover",".notification-container",".comment-info-container",".comment-list-container",".user-comment-container",".user-comment-container-fixed",".comment-content-row",".comment-input-row",".comment-reply-row",".tiptap-editor-wrapper",".editor-content",".ProseMirror",".ProseMirrorInput",".article-content"].forEach((e=>{document.querySelectorAll(e).forEach((e=>{e instanceof HTMLElement&&e.classList.add("theme-priority")}))})),document.querySelectorAll(".ProseMirror, .editor-content, .tiptap-editor-wrapper").forEach((e=>{e instanceof HTMLElement&&(e.setAttribute("data-theme-refresh","true"),requestAnimationFrame((()=>{e.offsetHeight,setTimeout((()=>e.removeAttribute("data-theme-refresh")),50)})))}));document.querySelectorAll(".comment-info-container, .article-info-container").forEach((e=>{if(e instanceof HTMLElement){const t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t}}))},ia=l({__name:"App",setup(e){const t=i((()=>Zt[ea.value]));return r((()=>{(()=>{const e=Gt.get(Xt);e&&Object.values(Qt).includes(e)&&(ea.value=e,na(e))})()})),s(ea,(e=>{c((()=>{[".search-container",".n-input",".user-info-group",".user-info",".avatar-container",".comment-info-container",".comment-list-container",".user-comment-container",".user-comment-container-fixed",".comment-content-row",".comment-input-row",".comment-reply-row",".tiptap-editor-wrapper",".editor-content",".ProseMirror",".ProseMirrorInput"].forEach((e=>{document.querySelectorAll(e).forEach((e=>{e instanceof HTMLElement&&e.classList.add("theme-priority")}))})),document.querySelectorAll(".ProseMirror, .editor-content").forEach((e=>{e instanceof HTMLElement&&(e.setAttribute("data-force-update",Date.now().toString()),setTimeout((()=>e.removeAttribute("data-force-update")),10))}))}))})),(e,a)=>{const o=d("router-view");return m(),u(h(X),{theme:t.value},{default:p((()=>[v(h(J),null,{default:p((()=>[v(h(W),null,{default:p((()=>[v(h(K),null,{default:p((()=>[v(h(G),null,{default:p((()=>[v(o)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["theme"])}}}),ra="api",sa="request",ca="color-picker",da={},ua={},ma=(e,t,a=300)=>{da[e]&&clearTimeout(da[e]),da[e]=setTimeout((()=>{t()}),a)},pa=(e,t,a=1e3)=>{const o=Date.now();(!ua[e]||o-ua[e]>=a)&&(t(),ua[e]=o)},{message:va}=Q(["message"]),ha=He.create({baseURL:Nt.backend.baseURL,timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});ha.interceptors.request.use(),ha.interceptors.response.use((e=>e),(e=>{const{response:t}=e;if(!t)return ma(sa+"net",(()=>{va.warning("网络错误，请稍后重试")})),Promise.reject(null);if(401===t.status)return Gt.removeLoginUser(),ma(sa+401,(()=>{va.warning("登录已过期，请重新登录")})),ss.push("/login"),document.cookie.split(";").forEach((function(e){const t=e.split("=")[0].trim();document.cookie=`${t}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`})),Promise.reject(null);return Promise.reject(e)}));const ga=e=>{let t={};if(!e)return t;if("CanceledError"===e.name||"canceled"===e.message)return t;if(null==e?void 0:e.response){t=e.response.data;const a=t.code;ma(ra+a,(()=>{va.warning(t.message||"网络请求失败")}))}else(null==e?void 0:e.request)?ma(ra+"network_error",(()=>{va.warning("网络连接失败，请检查网络设置")})):ma(ra+"unknown_error",(()=>{va.warning("请求发生错误，请稍后重试")}));return t},fa=(e,t,a)=>ha.get(e,{...a,params:t}),wa=(e,t)=>ha.post(e,t),ba=(e,t,a)=>ha.post(e,t,a),ya=(e,t)=>ha.put(e,t),xa=(e,t,a)=>ha.put(e,t,a),ka=(e,t)=>ha.delete(e,{params:t}),Ca={class:"bilibili-wrapper"},La=["src","data-danmaku","data-mute"],Ma={key:1,class:"bilibili-error"},Sa=(e,t)=>{const a=e.__vccOpts||e;for(const[o,n]of t)a[o]=n;return a},Aa=Sa(l({__name:"BilibiliNodeView",props:{node:{},decorations:{},selected:{type:Boolean},updateAttributes:{type:Function},deleteNode:{type:Function},view:{},getPos:{type:Function},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e){const t=e,a=i((()=>{const e=(e=>{if(!e)return null;const t=e.match(/(BV[a-zA-Z0-9]{10})/),a=e.match(/aid=(\d+)/),o=e.match(/b23\.tv\/([a-zA-Z0-9]+)/),n=e.match(/bilibili\.com\/video\/([a-zA-Z0-9]+)/);return t?t[1]:a?a[1]:o?o[1]:n?n[1]:null})(t.node.attrs.src);if(!e)return null;return`//www.bilibili.com/blackboard/html5mobileplayer.html?${new URLSearchParams({[e.startsWith("BV")?"bvid":"aid"]:e,page:"1",danmaku:t.node.attrs.danmaku?"1":"0",mute:t.node.attrs.mute?"1":"0",high_quality:"1"}).toString()}`}));return(e,t)=>(m(),u(h(Ve),{class:"bilibili-container"},{default:p((()=>[g("div",Ca,[a.value?(m(),f("iframe",{key:0,src:a.value,scrolling:"no",border:"0",frameborder:"0",framespacing:"0",allowfullscreen:"true","data-danmaku":e.node.attrs.danmaku,"data-mute":e.node.attrs.mute,class:"bilibili-iframe"},null,8,La)):(m(),f("div",Ma,t[0]||(t[0]=[g("p",null,"无效的 Bilibili 视频链接",-1)])))])])),_:1}))}}),[["__scopeId","data-v-b50d6dee"]]),Ra=e=>{const t=e.match(/(BV[a-zA-Z0-9]{10})/),a=e.match(/aid=(\d+)/),o=e.match(/b23\.tv\/([a-zA-Z0-9]+)/),n=e.match(/bilibili\.com\/video\/([a-zA-Z0-9]+)/);return t?t[1]:a?a[1]:o?o[1]:n?n[1]:null},Ea=Oe.create({name:"bilibili",group:"block",content:"text*",inline:!1,atom:!0,addAttributes:()=>({src:{default:null,parseHTML:e=>{const t=e.getAttribute("src");return t?Ra(t):null}},danmaku:{default:!0},mute:{default:!1}}),parseHTML:()=>[{tag:"iframe[src]",getAttrs:e=>({src:e.getAttribute("src"),danmaku:"true"===e.getAttribute("data-danmaku"),mute:"true"===e.getAttribute("data-mute")})}],renderHTML({node:e}){const{src:t,danmaku:a,mute:o}=e.attrs,n=Ra(t);if(!n)return["div",{class:"bilibili-error"},"无效的 Bilibili 视频链接"];return["div",{class:"bilibili-container"},["div",{class:"bilibili-wrapper"},["iframe",{src:`//www.bilibili.com/blackboard/html5mobileplayer.html?${new URLSearchParams({[n.startsWith("BV")?"bvid":"aid"]:n,page:"1",danmaku:a?"1":"0",mute:o?"1":"0",high_quality:"1"}).toString()}`,scrolling:"no",border:"0",frameborder:"0",framespacing:"0",allowfullscreen:"true","data-danmaku":a,"data-mute":o,class:"bilibili-iframe"}]]]},addNodeView:()=>De(Aa),addCommands(){return{setBilibiliVideo:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}}}),za={class:"code-block-header"},Ta={class:"code-block-language",contenteditable:"false",style:{userSelect:"none",pointerEvents:"none"}},Ia={class:"code-block-toolbar"},_a=["title"],Ba=["title"],$a=Sa(l({__name:"CodeBlockToolbar",props:{language:{},wrapMode:{type:Boolean},copyState:{}},emits:["toggle-wrap","copy-code"],setup:e=>(e,t)=>(m(),f("div",za,[g("span",Ta,w(e.language),1),g("div",Ia,[g("button",{class:b(["code-wrap-button",{active:e.wrapMode}]),title:e.wrapMode?"禁用换行":"启用换行",onClick:t[0]||(t[0]=t=>e.$emit("toggle-wrap"))},t[2]||(t[2]=[y('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-7684eaa4><polyline points="17 2 21 6 17 10" data-v-7684eaa4></polyline><path d="M3 11V9a4 4 0 0 1 4-4h14" data-v-7684eaa4></path><polyline points="7 22 3 18 7 14" data-v-7684eaa4></polyline><path d="M21 13v2a4 4 0 0 1-4 4H3" data-v-7684eaa4></path></svg>',1)]),10,_a),g("button",{class:b(["code-copy-button",{active:e.copyState.copied}]),title:e.copyState.copied?"已复制":"复制代码",onClick:t[1]||(t[1]=t=>e.$emit("copy-code"))},t[3]||(t[3]=[g("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[g("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),g("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})],-1)]),10,Ba)])]))}),[["__scopeId","data-v-7684eaa4"]]),Pa=Sa(l({__name:"CodeBlockNodeView",props:{decorations:{},selected:{type:Boolean},updateAttributes:{type:Function},deleteNode:{type:Function},node:{},view:{},getPos:{type:Function},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e){const t=e,a=n(!1),o=n({copied:!1,timer:null}),l=i((()=>t.editor.isEditable)),s=i((()=>{const e=t.node.attrs.language||"text";return"null"===e?"text":e})),c=i((()=>({display:"block",padding:"0.8rem 1rem",margin:"0",background:"transparent",border:"none",borderRadius:"0",fontFamily:"inherit",fontSize:"inherit",lineHeight:"inherit",whiteSpace:a.value?"pre-wrap":"pre",wordBreak:a.value?"break-word":"normal",overflowWrap:a.value?"break-word":"normal",width:"100%",boxSizing:"border-box"}))),d=()=>{a.value=!a.value},f=async()=>{try{const e=t.node.textContent||"";await navigator.clipboard.writeText(e),va.success("代码已复制到剪贴板"),o.value.copied=!0,ma("tiptap-code-copy",(()=>{o.value.copied=!1}),3e3)}catch(e){va.error("复制失败，请手动复制")}};return r((()=>{void 0!==t.node.attrs.wrap&&null!==t.node.attrs.wrap&&(a.value=Boolean(t.node.attrs.wrap))})),(e,t)=>(m(),u(h(Ve),{as:"pre",class:b(["code-block-container",{"code-wrap":a.value,"code-block-readonly":!l.value,"editable-mode":l.value,"readonly-mode":!l.value}]),"data-language":s.value,"data-selectable":!1},{default:p((()=>[v($a,{language:s.value,"wrap-mode":a.value,"copy-state":o.value,onToggleWrap:d,onCopyCode:f},null,8,["language","wrap-mode","copy-state"]),g("div",{class:b(["code-scrollbar-container",{"code-wrap":a.value}])},[v(h(Z),{"x-scrollable":!a.value,"y-scrollable":!1,trigger:"hover",size:8},{default:p((()=>[v(h(je),{as:"code",class:b([`language-${s.value}`,{"code-wrap-enabled":a.value,"code-selectable":!l.value}]),style:x(c.value)},null,8,["class","style"])])),_:1},8,["x-scrollable"])],2)])),_:1},8,["class","data-language"]))}}),[["__scopeId","data-v-6fd5c5d6"]]),Ua=Ne.extend({selectable:!1,atom:!1,draggable:!1,addOptions(){var e;return{...null==(e=this.parent)?void 0:e.call(this),HTMLAttributes:{class:"code-block-readonly","data-selectable":"false"},exitOnTripleEnter:!1,exitOnArrowDown:!1}},addNodeView:()=>De(Pa),addKeyboardShortcuts(){var e;return{...(null==(e=this.parent)?void 0:e.call(this))||{},Enter:({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:o,empty:n}=a;return!(!n||o.parent.type!==this.type)&&e.commands.first((({commands:e})=>[()=>e.newlineInCode()]))},"Shift-Enter":({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:o,empty:n}=a;if(!n||o.parent.type!==this.type)return!1;const l=o.before();return e.chain().insertContentAt(l,{type:"paragraph"}).command((({tr:e})=>{const t=l;return e.setSelection(zt.near(e.doc.resolve(t))),!0})).run()},"Ctrl-Enter":({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:o,empty:n}=a;if(!n||o.parent.type!==this.type)return!1;const l=o.after();return e.chain().insertContentAt(l,{type:"paragraph"}).command((({tr:e})=>{const t=l+1;return e.setSelection(zt.near(e.doc.resolve(t))),!0})).run()}}}}),Fa=qe.create({name:"fullscreen"}),Ha={passive:!0},Va=new Set(["wheel","touchstart","touchmove","touchend","touchcancel","scroll"]),Oa=new Set(["wheel"]);function Da(e,t){return function(e,t,a,o={},n=!1){const l=!n&&Va.has(t),i={...Ha,...o,passive:l};(n||Oa.has(t))&&(i.passive=!1);try{e.addEventListener(t,a,i)}catch(r){e.addEventListener(t,a,i.passive)}}(e,"wheel",t,{},!0),()=>{!function(e,t,a,o={}){try{e.removeEventListener(t,a,o)}catch(n){}}(e,"wheel",t)}}const ja={URL:"/core/files",imageTypes:["image/png","image/jpg","image/jpeg","image/gif","image/webp"],upload:async(e,t)=>{const a=new FormData;a.append("bucket",t),a.append("file",e);return(await ba(ja.URL,a,{headers:{"Content-Type":"multipart/form-data"}}).catch((e=>ga(e)))).data},uploadImage:async(e,t)=>ja.imageTypes.includes(e.type)?ja.upload(e,t):Promise.reject(new Error("upload fail")),getResourceURL:e=>e?e.startsWith("http")?e:`${Nt.backend.resourceURL}${e}`:""},Na="/thumbnail";const qa=e=>e.replace(/\/+/g,"/").replace(/^\/+/,"/"),Ya=e=>{if(!e)return"";if(!e.startsWith("http"))return qa(e);if(e.includes("localhost")){const t=e.match(/\/osr(\/.*$)/);if(t&&t[1])return qa(t[1])}if(!/^https?:\/\/[^\s]+$/.test(e)){Wt.warn("Invalid URL format:",e);const t=e.match(/\/[^?#]*/);return qa(t?t[0]:e)}const t=new URL(e).pathname;return t.startsWith("/")?qa(t.substring(1)):qa(t)},Ja=(e,t=!1)=>{const a=Ya(e),o=t?a:a.replace(Na,"");return ja.getResourceURL(o)},Wa=(e,t,a,o=!1)=>(Wt.debug("handleImageUpload started for file:",e.name,e.type),ja.uploadImage(e,a).then((e=>{var a,n;let l=e.data;Wt.debug("Original image URL from server:",l),!o&&l.includes(Na)&&(l=l.replace(Na,""),Wt.debug("Removed thumbnail from URL:",l));const i=(null==(a=t.storage.image)?void 0:a.transformSrc)?t.storage.image.transformSrc(l):Ya(l);Wt.debug("Converted to relative path:",i);const r=document.createElement("img");r.onerror=e=>{Wt.error("Image loading error:",e)},r.onload=()=>{let e=r.width,a=r.height,o=1;if(Wt.debug("Original image dimensions:",{width:e,height:a,aspectRatio:e/a}),e>600||a>800){const t=600/e,n=800/a;o=Math.min(t,n),e=Math.round(e*o),a=Math.round(a*o)}Wt.debug("Adjusted image dimensions:",{width:e,height:a,ratio:o}),Wt.debug("Setting image with relative path:",i);const n={src:i,width:`${e}px`,height:`${a}px`};t.commands.setImage(n)};const s=(null==(n=t.storage.image)?void 0:n.getFullUrl)?t.storage.image.getFullUrl(l):Ja(l,o);return Wt.debug("Loading image with full URL:",s),r.src=s,i}))),Ka=new Map,Ga=e=>{e?Ka.delete(e):Ka.clear()};function Xa(){return{showImagePreview:e=>{const{src:t,originalSrc:a,alt:o,onOriginalLoaded:n}=e,l=document.createElement("div");l.classList.add("modal-overlay");const i=document.createElement("img");i.alt=o,l.appendChild(i),document.body.appendChild(l),setTimeout((()=>{l.classList.add("modal-overlay-active")}),10);const r=!a.includes("/thumbnail")||-1===t.indexOf("/thumbnail"),s=Ka.get(a),c=void 0!==s;if(!a.includes("/thumbnail")||r||c)c?(i.src=s,i.style.opacity="1",i.dataset.originalFullUrl=s):(i.src=t,i.style.opacity="1");else{i.src=t,i.style.opacity="0.5";const e=document.createElement("div");e.classList.add("loading-spinner"),l.appendChild(e);const o=new Image,n=Date.now(),r=500;o.onload=()=>{const t=Date.now()-n,l=()=>{e.style.display="none",i.src=o.src,i.style.opacity="1",i.dataset.originalFullUrl=o.src,((e,t)=>{if(Ka.size>=50){const e=Ka.keys().next().value;e&&Ka.delete(e)}Ka.set(e,t)})(a,o.src)};t<r?setTimeout(l,r-t):l()};const s=Ja(a.replace("/thumbnail",""),!1);o.src=s}const{setupZoomControls:d,cleanupZoomControls:u}=function(e){let t=1;const a=.5,o=3,n=.1,l=l=>{l.preventDefault();const i=l.deltaY>0?-.1:n,r=Math.max(a,Math.min(o,t+i));r!==t&&(t=r,e.style.transform=`scale(${t})`)},i=()=>{t=1,e.style.transform=""};return{setupZoomControls:l,cleanupZoomControls:i}}(i),m=Da(l,d),p=()=>{l.classList.remove("modal-overlay-active"),l.addEventListener("transitionend",(()=>{l.classList.contains("modal-overlay-active")||(i.dataset.originalFullUrl&&a.includes("/thumbnail")&&(null==n||n(i.dataset.originalFullUrl)),document.body.removeChild(l),document.removeEventListener("keydown",v),m(),u())}),{once:!0})};l.addEventListener("click",p,{once:!0});const v=e=>{"Escape"===e.key&&p()};document.addEventListener("keydown",v)},clearOriginalImageCache:Ga}}const Qa=["src","alt","data-relative-src","data-original-src"],Za=["data-handle-position","onMousedown","onTouchstart"],eo=["data-handle-position","onMousedown","onTouchstart"],to={key:1,class:"resize-info"},ao=Sa(l({__name:"ImageNodeView",props:{useThumbnail:{type:Boolean,default:!0},decorations:{},selected:{type:Boolean},updateAttributes:{},deleteNode:{},node:{},view:{},getPos:{},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e,{expose:t}){const a=e,o=n(),l=n(),d=i((()=>a.editor.isEditable)),v=i((()=>a.selected)),y=k(!1),R=k(1),E=i((()=>Ya(a.node.attrs.src))),z=i((()=>Ja(E.value,a.useThumbnail))),T=k(0),I=k(0),_=k(0),B=k(0),$=k(0),P=k(0),U=k(null),F=k(""),H=k(""),V=C(["top-left","top-right","bottom-left","bottom-right"]),O=C(["top","right","bottom","left"]);let D=null;const j=new Map,N=i((()=>({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",margin:"0",padding:"0",verticalAlign:"baseline",lineHeight:"1",transform:"translateY(0)",transition:"none",zIndex:"1",willChange:"transform",whiteSpace:"nowrap",boxSizing:"border-box",width:"fit-content",maxWidth:"100%",border:"0"}))),q=i((()=>({display:"block",maxWidth:"100%",margin:"0",padding:"0",verticalAlign:"baseline",transform:"translateY(0)",transition:"none",willChange:"transform",lineHeight:"normal",fontSize:"inherit",width:y.value?F.value:a.node.attrs.width||"",height:y.value?H.value:a.node.attrs.height||""}))),Y=e=>{const t={position:"absolute",background:"#2d8cf0",border:"1px solid white",borderRadius:"50%",zIndex:"100",display:v.value&&d.value?"block":"none",visibility:v.value&&d.value?"visible":"hidden",opacity:v.value&&d.value?"1":"0"};if(V.includes(e)){const a={width:"8px",height:"8px",cursor:K(e)},o={"top-left":{top:"-4px",left:"-4px"},"top-right":{top:"-4px",right:"-4px"},"bottom-left":{bottom:"-4px",left:"-4px"},"bottom-right":{bottom:"-4px",right:"-4px"}};return{...t,...a,...o[e]}}if(O.includes(e)){const a={borderRadius:"2px",cursor:K(e)},o={top:{top:"-4px",left:"calc(50% - 6px)",width:"12px",height:"4px"},right:{right:"-4px",top:"calc(50% - 6px)",width:"4px",height:"12px"},bottom:{bottom:"-4px",left:"calc(50% - 6px)",width:"12px",height:"4px"},left:{left:"-4px",top:"calc(50% - 6px)",width:"4px",height:"12px"}};return{...t,...a,...o[e]}}return t},J=e=>{if(y.value)return Y(e);const t=`${e}-${v.value}-${d.value}`;if(j.has(t))return j.get(t);const a=Y(e);return j.set(t,a),a},W=Object.freeze({"top-left":"nw-resize","top-right":"ne-resize","bottom-left":"sw-resize","bottom-right":"se-resize",top:"n-resize",right:"e-resize",bottom:"s-resize",left:"w-resize"}),K=e=>W[e]||"pointer",G=new Set(O);let X=null;const Q=()=>{X&&clearTimeout(X),X=window.setTimeout((()=>{if(l.value){const e=l.value.getBoundingClientRect();$.value=e.width,P.value=e.height,R.value=e.width/e.height}}),16)},Z=()=>{},ee=e=>{e.stopPropagation()},te=e=>{e.preventDefault(),e.stopPropagation(),d.value||oe(e)},ae=e=>{e.preventDefault(),e.stopPropagation(),d.value&&oe(e)},oe=e=>{var t;e.preventDefault(),e.stopPropagation();const{showImagePreview:o}=Xa(),n=(null==(t=l.value)?void 0:t.src)||z.value,i=!n.includes("/thumbnail");o({src:i?n:z.value,originalSrc:i?E.value.replace("/thumbnail",""):E.value,alt:a.node.attrs.alt||"图片预览",useThumbnail:a.useThumbnail&&!i,onOriginalLoaded:e=>{if(l.value&&E.value.includes("/thumbnail")){l.value.src=e;const o=E.value.replace("/thumbnail","");if(l.value.dataset.originalSrc=o,"function"==typeof a.getPos&&!a.editor.isEditable)try{a.editor.commands.updateAttributes("image",{src:o})}catch(t){}}}})},ne=(e,t)=>{if(e.preventDefault(),e.stopPropagation(),!d.value)return;if("function"!=typeof a.getPos)return;const o=a.getPos();if(a.editor.commands.setNodeSelection(o),y.value=!0,U.value=t,l.value){const e=l.value.getBoundingClientRect();_.value=e.width,B.value=e.height,$.value=e.width,P.value=e.height,R.value=_.value/B.value,F.value=`${e.width}px`,H.value=`${e.height}px`}T.value=e.clientX,I.value=e.clientY,document.addEventListener("mousemove",ie,{passive:!1}),document.addEventListener("mouseup",ce,{passive:!0})},le=(e,t)=>{if(!e.touches||0===e.touches.length)return;const a=e.touches[0],o={clientX:a.clientX,clientY:a.clientY,preventDefault:()=>{},stopPropagation:()=>{}};ne(o,t),requestAnimationFrame((()=>{document.body.style.overflow="hidden"})),document.addEventListener("touchmove",re,{passive:!1}),document.addEventListener("touchend",se,{passive:!0}),document.addEventListener("touchcancel",se,{passive:!0})},ie=e=>{if(!y.value||!U.value)return;e.preventDefault();const t=e.clientX-T.value,a=e.clientY-I.value,o=U.value;let n=_.value,l=B.value;const i=e.shiftKey,r=e.altKey;switch(o){case"top-left":n=_.value-t,l=B.value-a;break;case"top-right":n=_.value+t,l=B.value-a;break;case"bottom-left":n=_.value-t,l=B.value+a;break;case"bottom-right":n=_.value+t,l=B.value+a;break;case"top":l=B.value-a,i&&(n=l*R.value);break;case"right":n=_.value+t,i&&(l=n/R.value);break;case"bottom":l=B.value+a,i&&(n=l*R.value);break;case"left":n=_.value-t,i&&(l=n/R.value)}!i||(e=>G.has(e))(o)||r||(Math.abs(t)>Math.abs(a)?l=n/R.value:n=l*R.value);n=Math.max(n,20),l=Math.max(l,20),$.value=n,P.value=l,F.value=`${n}px`,H.value=`${l}px`},re=e=>{if(!y.value||!U.value)return;if(!e.touches||0===e.touches.length)return;e.preventDefault();const t=e.touches[0],a=e.touches.length,o={clientX:t.clientX,clientY:t.clientY,preventDefault:()=>{},altKey:2===a,shiftKey:3===a};ie(o)},se=()=>{ce()},ce=()=>{if(y.value&&(document.removeEventListener("mousemove",ie),document.removeEventListener("mouseup",ce),document.removeEventListener("touchmove",re),document.removeEventListener("touchend",se),document.removeEventListener("touchcancel",se),document.body.style.overflow="",y.value=!1,U.value=null,F.value="",H.value="","function"==typeof a.getPos&&l.value)){const e=Math.round($.value),t=Math.round(P.value);a.editor.commands.updateAttributes("image",{width:`${e}px`,height:`${t}px`})}};s((()=>a.editor.isEditable),(e=>{var t;if(!e){const e=(null==(t=o.value)?void 0:t.$el)||o.value;e&&e.classList.remove("ProseMirror-selectednode")}j.clear()})),s((()=>v.value),(()=>{j.clear()}));const de=()=>{var e;if(!a.editor.isEditable){const t=(null==(e=o.value)?void 0:e.$el)||o.value;t&&t.classList.remove("ProseMirror-selectednode")}};r((()=>{c((()=>{var e;if((()=>{var e;const t=(null==(e=o.value)?void 0:e.$el)||o.value;if(!t)return;D&&D.disconnect();let a=null;D=new MutationObserver((e=>{a&&clearTimeout(a),a=window.setTimeout((()=>{const a=e.filter((e=>"attributes"===e.type&&"class"===e.attributeName));if(0===a.length)return;const o=a[0].target,n=v.value,l=o.classList.contains("ProseMirror-selectednode");!n&&l&&d.value&&(null==t||t.scrollIntoView({behavior:"smooth",block:"center"}),t&&t.animate([{outline:"2px solid #2d8cf0",boxShadow:"0 0 0 3px rgba(45, 140, 240, 0.2)"},{outline:"2px solid #2d8cf0",boxShadow:"0 0 0 5px rgba(45, 140, 240, 0.5)"},{outline:"2px solid #2d8cf0",boxShadow:"0 0 0 3px rgba(45, 140, 240, 0.2)"}],{duration:600,easing:"ease-in-out"})),!d.value&&l&&requestAnimationFrame((()=>{o.classList.remove("ProseMirror-selectednode")}))}),50)})),D.observe(t,{attributes:!0,attributeFilter:["class"],subtree:!1})})(),Q(),a.editor.on("update",de),!d.value){const t=(null==(e=o.value)?void 0:e.$el)||o.value;t&&t.classList.remove("ProseMirror-selectednode")}}))})),L((()=>{X&&(clearTimeout(X),X=null),D&&(D.disconnect(),D=null),a.editor.off("update",de),document.removeEventListener("mousemove",ie),document.removeEventListener("mouseup",ce),document.removeEventListener("touchmove",re),document.removeEventListener("touchend",se),document.removeEventListener("touchcancel",se),document.body.style.overflow="",j.clear()}));return t({updateNode:e=>{e.attrs.src,a.node.attrs.src,e.attrs.width!==a.node.attrs.width&&l.value&&(l.value.style.width=e.attrs.width||""),e.attrs.height!==a.node.attrs.height&&l.value&&(l.value.style.height=e.attrs.height||"")}}),(e,t)=>(m(),u(h(Ve),{ref_key:"imageWrapper",ref:o,class:b(["image-wrapper",{"readonly-image":!d.value,"ProseMirror-selectednode":v.value&&d.value,resizing:y.value}]),style:x(N.value),onClick:ee},{default:p((()=>[g("img",{ref_key:"imageElement",ref:l,src:z.value,alt:e.node.attrs.alt||"",style:x(q.value),"data-relative-src":E.value,"data-original-src":E.value,contenteditable:"false",loading:"eager",class:"cursor-pointer",onDblclick:ae,onClick:te,onLoad:Q,onError:Z},null,44,Qa),d.value&&v.value?(m(),f(S,{key:0},[(m(!0),f(S,null,A(h(V),(e=>(m(),f("div",{key:e,class:b(["resize-handle",`handle-${e}`]),"data-handle-position":e,"data-center-handle":!1,style:x(J(e)),onMousedown:t=>ne(t,e),onTouchstart:t=>le(t,e)},null,46,Za)))),128)),(m(!0),f(S,null,A(h(O),(e=>(m(),f("div",{key:e,class:b(["resize-handle",`handle-${e}`]),"data-handle-position":e,"data-center-handle":!0,style:x(J(e)),onMousedown:t=>ne(t,e),onTouchstart:t=>le(t,e)},null,46,eo)))),128))],64)):M("",!0),y.value?(m(),f("div",to,w(Math.round($.value))+" × "+w(Math.round(P.value)),1)):M("",!0)])),_:1},8,["class","style"]))}}),[["__scopeId","data-v-9df24d95"]]),oo=(e=!1)=>Ye.extend({draggable:!0,inline:!0,group:"inline",atom:!0,addOptions(){var t;return{...null==(t=this.parent)?void 0:t.call(this),transformSrc:e=>Ya(e),getFullUrl:t=>Ja(t,e)}},addAttributes(){var e;return{...null==(e=this.parent)?void 0:e.call(this),width:{default:null,renderHTML:e=>e.width?{width:e.width}:{}},height:{default:null,renderHTML:e=>e.height?{height:e.height}:{}},src:{default:null,parseHTML:e=>{const t=e.getAttribute("src")||"";return Ya(t)},renderHTML:e=>({src:e.src})}}},addNodeView:()=>De(ao),addStorage:()=>({transformSrc:Ya,getFullUrl:t=>Ja(t,e)})}).configure({allowBase64:!1}),no=oo(!0);const lo=()=>{const e=n(),t=(e,t,a,o)=>{Wt.debug("files: ",e),e.length&&e.forEach((e=>{e.type.startsWith("image/")&&Wa(e,t,a,o).then((e=>{Wt.debug("Image uploaded, relative path:",e);const{handleImageUploadSuccess:a}=function(){const e=()=>{document.querySelectorAll(".image-wrapper.ProseMirror-selectednode").forEach((e=>{e.classList.remove("ProseMirror-selectednode")}))},t=e=>!!(e&&e.view&&e.view.dom)||(Wt.error("Editor view dom not found"),!1),a=(e,t)=>{var a;const o=e.view.dom.querySelectorAll(".image-wrapper img");Wt.debug("Found image elements:",o.length);let n=null;const l=null==(a=e.storage.image)?void 0:a.transformSrc;return Array.from(o).filter((e=>e instanceof HTMLImageElement)).forEach((e=>{const a=e.dataset.relativeSrc,o=e.getAttribute("src");Wt.debug("Checking img:",{dataRelativeSrc:a,src:o,lookingFor:t});let i=o||"";if(l&&o)i=l(o);else if(o&&o.startsWith("http"))try{i=new URL(o).pathname}catch{Wt.warn("Failed to parse URL:",o)}(a===t||i===t||t.endsWith(i))&&(Wt.debug("Found matching image element"),n=e.closest(".image-wrapper"))})),n},o=e=>{var t,a;Wt.debug("Found target wrapper, adding visual feedback"),null==(t=e.classList)||t.add("ProseMirror-selectednode"),null==(a=e.scrollIntoView)||a.call(e,{behavior:"smooth",block:"center"})},n=e=>{try{const t=document.createElement("div");t.className="upload-success-effect",t.style.cssText="\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        border-radius: 0.25rem;\n        background-color: rgba(90, 214, 150, 0.2);\n        z-index: 5;\n        pointer-events: none;\n        opacity: 0;\n      ",e.appendChild(t),t.animate([{opacity:.7},{opacity:0}],{duration:800,easing:"ease-out"}).onfinish=()=>{t.remove()},e.animate([{outline:"2px solid #5ad696",outlineOffset:"3px"},{outline:"2px solid #2d8cf0",outlineOffset:"2px"}],{duration:800,easing:"ease-out"})}catch(t){Wt.error("Error adding upload success animation:",t)}},l=e=>{var t,a;Wt.debug("No matching image found, selecting last wrapper");const o=e.view.dom.querySelectorAll(".image-wrapper");if(o.length>0){const e=o[o.length-1];null==(t=e.classList)||t.add("ProseMirror-selectednode"),null==(a=e.scrollIntoView)||a.call(e,{behavior:"smooth",block:"center"})}};return{handleImageUploadSuccess:(i,r)=>{setTimeout((()=>{try{if(e(),!t(i))return;const s=a(i,r);s?(o(s),n(s)):l(i)}catch(s){Wt.error("Error selecting image after upload:",s)}}),300)},clearSelectedImages:e,validateEditor:t,findTargetImageWrapper:a,selectAndScrollToImage:o,addUploadSuccessAnimation:n,selectLastImage:l}}();a(t,e)}))}))},{imageHandleCallback:a}=function(){const e=e=>e.filter((e=>e.type.startsWith("image/"))),t=e=>!(!e||!e.src),a=(e,t,a,o)=>{if(Wt.debug("Editor is already handling one image, skipping duplicate processing"),e.length>1){Wt.debug("Processing additional images:",e.length-1);for(let n=1;n<e.length;n++)Wa(e[n],t,a,o)}},o=(e,t,a,o)=>{Wt.debug("Processing all images:",e.length),e.forEach((e=>{Wa(e,t,a,o)}))};return{imageHandleCallback:(n,l,i,r,s)=>{if(Wt.debug("imageHandleCallback called with:",{filesCount:l.length,hasAttrs:!!i}),!l.length)return;const c=e(l);0!==c.length?t(i)?a(c,n,r,s):o(c,n,r,s):Wt.debug("No image files found in event")},filterImageFiles:e,shouldSkipFirstImage:t,processAdditionalImages:a,processAllImages:o}}();return{imageInputRef:e,handleImageChange:async(e,a,o,n)=>{const l=e.target;if(l.files&&l.files.length>0){const e=Array.from(l.files);t(e,a,o,n)}},handleImageChangeCallback:t,imageHandleCallback:a}},io={URL:"/core/users",online:async()=>(await fa(io.URL+"/online").catch((e=>ga(e)))).data,info:async()=>(await fa(io.URL+"/info").catch((e=>ga(e)))).data,changeAvatar:async e=>{const t=new FormData;t.append("file",e);return(await xa(io.URL+"/avatar",t,{headers:{"Content-Type":"multipart/form-data"}}).catch((e=>ga(e)))).data},updateNotificationReceiveType:async e=>(await ya(`${io.URL}/notification-receive-type/${e}`).catch((e=>ga(e)))).data,searchUser:async e=>(await fa(io.URL+"/search/"+e).catch((e=>ga(e)))).data},ro={class:"mention-list-container"},so={key:0,class:"mention-list"},co=["data-active","onClick"],uo={class:"mention-button-icon"},mo=["src","alt"],po={key:1,class:"mention-avatar mention--avatar-fallback"},vo={class:"mention-name"},ho={key:1,class:"mention-empty"},go=Sa(l({__name:"MentionList",props:{items:{},command:{type:Function}},setup(e,{expose:t}){const a=e,o=n(0);s((()=>a.items),(()=>{o.value=0}));const l=()=>{o.value=(o.value+a.items.length-1)%a.items.length},i=()=>{o.value=(o.value+1)%a.items.length},r=()=>{c(o.value)},c=e=>{const t=a.items[e];if(t){const e={id:t.id,label:t.username,avatar:t.avatar};a.command(e)}};return t({onKeyDown:({event:e})=>"ArrowUp"===e.key?(l(),!0):"ArrowDown"===e.key?(i(),!0):"Enter"===e.key&&(r(),!0)}),(e,t)=>(m(),f("div",ro,[e.items.length?(m(),f("div",so,[(m(!0),f(S,null,A(e.items,((e,t)=>(m(),f("button",{key:t,type:"button",class:b(["mention-button",{"mention-button-active":t===o.value}]),"data-active":t===o.value?"true":void 0,onClick:e=>c(t)},[g("div",uo,[e.avatar?(m(),f("img",{key:0,src:h(ja).getResourceURL(e.avatar),alt:e.username,class:"mention-avatar"},null,8,mo)):(m(),f("div",po,w(e.username.charAt(0).toUpperCase()),1))]),g("div",vo,w(e.username),1)],10,co)))),128))])):(m(),f("div",ho," ... "))]))}}),[["__scopeId","data-v-9733f8bd"]]),fo=Je.extend({name:"mention",addAttributes(){var e;return{...null==(e=this.parent)?void 0:e.call(this),avatar:{default:null,parseHTML:e=>e.getAttribute("data-avatar"),renderHTML:e=>e.avatar?{"data-avatar":e.avatar}:{}}}},renderHTML({node:e,HTMLAttributes:t}){const a=e.attrs,o=[["span",{class:"mention-name"},`@${a.label}`]];if(a.avatar){const e=ja.getResourceURL(a.avatar);o.push(["img",{class:"mention-avatar",src:e,alt:a.label,loading:"lazy"},""])}return["span",{...t,"data-type":"mention","data-id":a.id,"data-label":a.label,"data-avatar":a.avatar||"",class:"mention",contenteditable:"false"},...o]}}).configure({suggestion:{char:"@",items:async({query:e})=>{if(!e)return[];return(await io.searchUser(e)).data},render:()=>{let e,t=null;return{onStart:a=>{e=new We(go,{props:a,editor:a.editor}),a.clientRect&&(t=document.createElement("div"),t.className="mention-menu",t.style.cssText="\n            position: fixed;\n            z-index: 10000;\n            white-space: nowrap;\n            pointer-events: all;\n            max-height: 15rem;\n            padding: 0.25rem;\n            overflow: hidden auto;\n            border: 1px solid var(--border-color, #e5e7eb);\n            border-radius: 0.5rem;\n            background-color: var(--bg-color, white);\n            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 10%), 0 2px 4px -2px rgba(0, 0, 0, 10%);\n          ",e.element&&t.appendChild(e.element),document.body.appendChild(t),function(){if(!t||!a.clientRect)return;const e=a.clientRect();if(!e)return;const o=window.innerHeight,n=t.offsetHeight||240,l=o-e.bottom,i=e.top;l<n&&i>l?(t.className="mention-menu mention-menu-above",t.style.transformOrigin="bottom center",t.style.animation="slide-up-fade-in 0.15s ease-out",t.style.left=e.left+"px",t.style.top=e.top-n-4+"px"):(t.className="mention-menu mention-menu-below",t.style.transformOrigin="top center",t.style.animation="slide-down-fade-in 0.15s ease-out",t.style.left=e.left+"px",t.style.top=e.bottom+4+"px")}())},onUpdate(a){if(e&&e.updateProps(a),!a.clientRect||!t)return;const o=a.clientRect();if(!o)return;const n=window.innerHeight,l=t.offsetHeight||240,i=n-o.bottom,r=o.top;i<l&&r>i?(t.className="mention-menu mention-menu-above",t.style.left=o.left+"px",t.style.top=o.top-l-4+"px"):(t.className="mention-menu mention-menu-below",t.style.left=o.left+"px",t.style.top=o.bottom+4+"px")},onKeyDown(t){var a;return"Escape"===t.event.key||(null==(a=e.ref)?void 0:a.onKeyDown(t))},onExit(){t&&t.parentNode&&t.parentNode.removeChild(t),e&&e.destroy()}}}}});class wo{constructor(e){t(this,"editor"),t(this,"options"),t(this,"element"),t(this,"index"),t(this,"nodes"),t(this,"items"),this.editor=e.editor,this.options=e}static create(e){return()=>new wo(e)}onStart(e){this.index=0,this.nodes=[],this.items=[],this.element=document.createElement("div"),this.element.classList.add("slash-menu");for(const t of this.options.classes??[])this.element.classList.add(t);for(const[t,a]of Object.entries(this.options.attributes??{}))this.element.setAttribute(t,a);document.body.appendChild(this.element),this.onUpdate(e)}onUpdate(e){this.element&&void 0!==this.index&&this.nodes&&this.items&&(this.items=e.items,this.render(),this.updatePosition(e.clientRect))}onKeyDown(e){if(!this.element||void 0===this.index||!this.nodes||!this.items)return!1;if("Escape"===e.event.key)return this.hide(),!0;if("Enter"===e.event.key){const e=this.items[this.index];return e&&"string"!=typeof e&&e.action&&e.action(this.editor),!0}if("ArrowUp"===e.event.key){const e=this.index-1,t=this.items[e]&&"string"==typeof this.items[e]?e-1:e;return this.selectItem(t<0?this.items.length-1:t,!0),!0}if("ArrowDown"===e.event.key){const e=this.index+1,t=this.items[e]&&"string"==typeof this.items[e]?e+1:e;return this.selectItem(t>=this.items.length?0:t,!0),!0}return!1}onExit(){this.hide(),this.cleanup()}selectItem(e,t){if(this.element&&void 0!==this.index&&this.nodes&&this.items){this.index=Math.max(0,Math.min(e,this.items.length-1));for(let e=0;e<this.nodes.length;e++)e===this.index?this.nodes[e].setAttribute("data-active","true"):this.nodes[e].removeAttribute("data-active");t&&this.nodes[this.index]&&$t(this.nodes[this.index],{block:"center",scrollMode:"if-needed",boundary:e=>e!==this.element})}}render(){var e;if(this.element&&void 0!==this.index&&this.items){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);if(this.index=Math.max(0,Math.min(this.index,Math.max(0,this.items.length-1))),this.items.length){this.nodes=[];for(let e=0;e<this.items.length;e++){const t=this.items[e];if("|"===t){const e=document.createElement("div");e.classList.add("slash-menu-divider"),this.nodes.push(e)}else{const a=document.createElement("button");if(a.classList.add("slash-menu-button"),a.setAttribute("type","button"),t.icon){const e=document.createElement("div");e.classList.add("slash-menu-button-icon"),e.innerHTML=t.icon,a.appendChild(e)}const o=document.createElement("div");if(o.classList.add("slash-menu-button-name"),o.textContent=t.name,a.appendChild(o),t.shortcut){const e=document.createElement("div");e.classList.add("slash-menu-button-shortcut"),e.textContent=t.shortcut.replace(/mod/i,navigator.userAgent.includes("Mac")?"⌘":"Ctrl").replace(/ctrl|control/i,"Ctrl").replace(/cmd|command/i,"⌘").replace(/shift/i,"Shift").replace(/alt|option/i,"Alt"),a.appendChild(e)}e===this.index&&a.setAttribute("data-active","true"),a.addEventListener("click",(()=>{t.action&&t.action(this.editor)})),a.addEventListener("mouseover",(()=>{this.index!==e&&this.selectItem(e)})),this.nodes.push(a)}}this.element.append(...this.nodes),this.nodes[this.index]&&$t(this.nodes[this.index],{block:"center",scrollMode:"if-needed",boundary:e=>e!==this.element})}else{const t=document.createElement("div");t.classList.add("slash-menu-empty"),t.textContent=(null==(e=this.options.dictionary)?void 0:e.empty)||"未找到结果",this.element.appendChild(t)}this.show()}}updatePosition(e){if(!this.element)return;const t=e(),a=window.innerHeight,o=window.innerWidth;this.element.style.position="fixed",this.element.style.zIndex="10000",this.element.style.left=`${t.left}px`,this.element.style.top=`${t.bottom+8}px`,this.element.style.visibility="hidden",this.element.style.display="block";const n=this.element.getBoundingClientRect(),l=n.height,i=n.width;this.element.style.visibility="visible";let r=t.left,s=t.bottom+8,c=!1;const d=a-t.bottom-8,u=t.top-8;d<l&&u>l?(s=t.top-l-8,c=!0):d<l&&u<l&&(u>d&&(s=t.top-l-8,c=!0),(c&&u<l||!c&&d<l)&&(this.element.style.maxHeight=Math.max(u,d)-16+"px",this.element.style.overflowY="auto")),r+i>o&&(r=Math.max(8,o-i-8)),r<8&&(r=8),this.element.style.left=`${r}px`,this.element.style.top=`${s}px`,this.element.classList.toggle("slash-menu-above",c),this.element.classList.toggle("slash-menu-below",!c)}show(){this.element&&(this.element.style.display="block")}hide(){this.element&&(this.element.style.display="none")}cleanup(){this.element&&(this.element.remove(),this.element=void 0),this.index=void 0,this.items=void 0,this.nodes=void 0}}const bo=qe.create({name:"slashMenu",addOptions:()=>({items:[],dictionary:{lineEmpty:"",lineSlash:" ...",queryEmpty:"..."},imageUploadTrigger:void 0,modalTrigger:void 0}),addProseMirrorPlugins(){return[Ke({editor:this.editor,pluginKey:new It(`${this.name}-suggestion`),char:"/",items:({query:e})=>{const t=[];for(const o of this.options.items)if("|"!==o){if(""!==e){const t=e.toLowerCase();if(!o.name.toLowerCase().includes(t)&&!o.keywords.toLowerCase().includes(t))continue}t.push({...o,action:e=>{const{state:t,dispatch:a}=e.view,n=t.selection.$from;a(t.tr.deleteRange(n.start(),n.pos)),"image"===o.id&&this.options.imageUploadTrigger?this.options.imageUploadTrigger():o.action(e),e.view.focus()}})}else t.push(o);const a=[];for(let o=0;o<t.length;o++){const e=t[o];if("|"===e){if(0===o||o===t.length-1)continue;if("|"===t[o+1])continue;if(0===a.length)continue;if("|"===a[a.length-1])continue}a.push(e)}return a},render:wo.create({editor:this.editor,dictionary:{empty:this.options.dictionary.queryEmpty}})}),new Tt({key:new It(`${this.name}-placeholder`),props:{decorations:e=>{const{$from:t}=e.selection,a=t.parent;if("paragraph"!==a.type.name)return null;const o=[],n=0===a.content.size,l="/"===a.textContent;return 1===t.depth?(n&&o.push(_t.node(t.start()-1,t.end()+1,{class:"slash-menu-placeholder","data-placeholder":this.options.dictionary.lineEmpty})),l&&o.push(_t.node(t.start()-1,t.end()+1,{class:"slash-menu-placeholder","data-placeholder":` ${this.options.dictionary.lineSlash}`})),Bt.create(e.doc,o)):null}}})]}}),yo={TextHeader120Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M16.573 3.823a.75.75 0 0 0-1.058.53c-.255 1.138-1.308 2.608-2.681 3.523a.75.75 0 1 0 .832 1.248A8.769 8.769 0 0 0 15.5 7.47V15.5a.75.75 0 0 0 1.5 0V4.516a.75.75 0 0 0-.427-.693zM3.5 4.5a.75.75 0 1 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 1 0-1.5 0V9h-5V4.5z" fill="currentColor"></path></g></svg>',TextHeader220Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm11.25.75c-1.292 0-2.25 1.124-2.25 2.25a.75.75 0 0 1-1.5 0c0-1.874 1.551-3.75 3.75-3.75c1.403 0 2.475.793 2.973 1.915c.49 1.106.41 2.488-.33 3.72c-.385.643-.958 1.16-1.527 1.607c-.265.209-.545.414-.816.613l-.067.049c-.295.217-.582.43-.858.65c-.892.715-1.569 1.449-1.794 2.446h4.919a.75.75 0 0 1 0 1.5H11.5a.75.75 0 0 1-.75-.75c0-2.099 1.226-3.396 2.437-4.366c.303-.243.614-.473.909-.69l.062-.045c.276-.202.535-.393.78-.586c.534-.42.929-.799 1.169-1.199c.51-.85.52-1.718.244-2.341c-.27-.608-.822-1.023-1.601-1.023z" fill="currentColor"></path></g></svg>',TextHeader320Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm8.97 1.958c.086-.295.216-.573.467-.784c.245-.206.693-.424 1.563-.424c.777 0 1.257.3 1.555.648c.32.374.445.825.445 1.102c0 .356-.091.92-.448 1.38c-.327.423-.965.87-2.302.87a.75.75 0 0 0 0 1.5c.446 0 1.198.11 1.81.42c.59.298.94.711.94 1.33c0 .84-.258 1.385-.593 1.72c-.338.338-.824.53-1.407.53c-.68 0-1.152-.116-1.458-.3c-.275-.164-.47-.414-.557-.847a.75.75 0 1 0-1.47.294c.163.817.593 1.442 1.255 1.84c.632.379 1.41.513 2.23.513c.917 0 1.806-.308 2.468-.97c.665-.665 1.032-1.62 1.032-2.78c0-1.234-.695-2.034-1.481-2.512c.283-.201.522-.434.72-.689C17.868 8.485 18 7.551 18 7c0-.63-.25-1.428-.805-2.077c-.577-.675-1.472-1.173-2.695-1.173c-1.13 0-1.95.29-2.528.776c-.571.48-.816 1.078-.942 1.516a.75.75 0 0 0 1.44.416z" fill="currentColor"></path></g></svg>',TextBold20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5 4.5A1.5 1.5 0 0 1 6.5 3h3.88c2.364 0 4.12 1.934 4.12 4.12c0 .819-.247 1.606-.68 2.269c.842.749 1.427 1.849 1.427 3.241c0 2.775-2.318 4.37-4.367 4.37H6.5A1.5 1.5 0 0 1 5 15.5v-11zM8 6v2.25h2.38c.625 0 1.12-.516 1.12-1.13A1.12 1.12 0 0 0 10.38 6H8zm0 5.25V14h2.88c.691 0 1.367-.537 1.367-1.37c0-.84-.684-1.38-1.367-1.38H8z" fill="currentColor"></path></g></svg>',TextItalic20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M8 3.25a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-3.235L8.592 15.5h2.658a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3.235L11.408 4H8.75A.75.75 0 0 1 8 3.25z" fill="currentColor"></path></g></svg>',TextUnderline24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6 4.5a1 1 0 0 1 2 0v6.001c-.003 3.463 1.32 4.999 4.247 4.999c2.928 0 4.253-1.537 4.253-5v-6a1 1 0 1 1 2 0v6c0 4.54-2.18 7-6.253 7S5.996 15.039 6 10.5v-6zM7 21a1 1 0 1 1 0-2h10.5a1 1 0 1 1 0 2H7z" fill="currentColor"></path></g></svg>',TextStrikethrough20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M6.252 3.702A6.56 6.56 0 0 1 10 2.5c2.783 0 4.489 1.485 5.1 2.3a.75.75 0 0 1-1.2.9C13.511 5.182 12.217 4 10 4a5.06 5.06 0 0 0-2.877.923C6.331 5.489 6 6.105 6 6.5c0 .78.376 1.285 1.11 1.71c.18.105.377.2.586.29H5.162c-.408-.523-.662-1.178-.662-2c0-1.105.794-2.114 1.752-2.798zM16.5 10a.75.75 0 0 1 0 1.5h-1.662c.408.523.662 1.178.662 2c0 1.358-.874 2.376-1.912 3.014c-1.042.641-2.367.986-3.588.986c-1.142 0-2.133-.129-2.992-.498c-.877-.378-1.563-.982-2.132-1.836a.75.75 0 1 1 1.248-.832c.43.646.901 1.042 1.477 1.29c.594.255 1.354.376 2.4.376c.966 0 2.015-.28 2.801-.764C13.593 14.75 14 14.141 14 13.5c0-.78-.376-1.285-1.11-1.71c-.18-.105-.377-.2-.586-.29H3.5a.75.75 0 0 1 0-1.5h13z" fill="currentColor"></path></g></svg>',Code20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M12.937 4.052a.75.75 0 0 0-1.373-.604l-5.5 12.5a.75.75 0 1 0 1.372.604l5.5-12.5zm1.356 9.793a.75.75 0 0 1-.137-1.052L16.303 10l-2.148-2.793a.75.75 0 0 1 1.188-.914l2.5 3.25a.75.75 0 0 1 0 .915l-2.5 3.25a.75.75 0 0 1-1.051.137zm-8.586-7.69a.75.75 0 0 1 .137 1.053L3.696 10l2.148 2.793a.75.75 0 1 1-1.188.915l-2.5-3.25a.75.75 0 0 1 0-.915l2.5-3.25a.75.75 0 0 1 1.051-.137z" fill="currentColor"></path></g></svg>',Image28Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 28 28"><g fill="none"><path d="M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z" fill="currentColor"></path></g></svg>',ArrowUndo16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3 2.75a.75.75 0 0 1 1.5 0v3.095l2.673-2.673a4 4 0 0 1 5.657 5.656l-4.95 4.95a.75.75 0 1 1-1.06-1.06l4.95-4.95a2.5 2.5 0 0 0-3.536-3.536L5.966 6.5H8.25a.75.75 0 0 1 0 1.5h-4.4A.85.85 0 0 1 3 7.15v-4.4z" fill="currentColor"></path></g></svg>',ArrowRedo16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M13.002 2.75a.75.75 0 0 0-1.5 0v3.095L8.828 3.172a4 4 0 0 0-5.656 5.656l4.95 4.95a.75.75 0 1 0 1.06-1.06l-4.95-4.95a2.5 2.5 0 0 1 3.536-3.536L10.036 6.5H7.75a.75.75 0 0 0 0 1.5h4.4c.47 0 .85-.38.85-.85v-4.4z" fill="currentColor"></path></g></svg>',LineHorizontal120Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M2 9.75A.75.75 0 0 1 2.75 9h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 9.75z" fill="currentColor"></path></g></svg>',VideoClip24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6.25A3.25 3.25 0 0 1 5.25 3h13.5A3.25 3.25 0 0 1 22 6.25v11.5A3.25 3.25 0 0 1 18.75 21H5.25A3.25 3.25 0 0 1 2 17.75V6.25zm7.5 3.134v5.231c0 .57.61.932 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.658z" fill="currentColor"></path></g></svg>',FullScreenMaximize16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M4 3.5a.5.5 0 0 0-.5.5v1.614a.75.75 0 0 1-1.5 0V4a2 2 0 0 1 2-2h1.614a.75.75 0 0 1 0 1.5H4zm5.636-.75a.75.75 0 0 1 .75-.75H12a2 2 0 0 1 2 2v1.614a.75.75 0 0 1-1.5 0V4a.5.5 0 0 0-.5-.5h-1.614a.75.75 0 0 1-.75-.75zM2.75 9.636a.75.75 0 0 1 .75.75V12a.5.5 0 0 0 .5.5h1.614a.75.75 0 0 1 0 1.5H4a2 2 0 0 1-2-2v-1.614a.75.75 0 0 1 .75-.75zm10.5 0a.75.75 0 0 1 .75.75V12a2 2 0 0 1-2 2h-1.614a.75.75 0 1 1 0-1.5H12a.5.5 0 0 0 .5-.5v-1.614a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',ResizeSmall20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zm3 3A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zm6-3A1.5 1.5 0 0 1 16 5.5v1a.5.5 0 0 0 1 0v-1A2.5 2.5 0 0 0 14.5 3h-1a.5.5 0 0 0 0 1h1zm0 12a1.5 1.5 0 0 0 1.5-1.5v-1a.5.5 0 0 1 1 0v1a2.5 2.5 0 0 1-2.5 2.5h-1a.5.5 0 0 1 0-1h1zm-9 0A1.5 1.5 0 0 1 4 14.5v-1.25a.5.5 0 0 0-1 0v1.25A2.5 2.5 0 0 0 5.5 17h1.25a.5.5 0 0 0 0-1H5.5z" fill="currentColor"></path></g></svg>',TextBulletListLtr16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M2.25 5a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm0 4.25a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm1.25 3a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0zM5.75 3a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5zM5 8a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5A.75.75 0 0 1 5 8zm.75 3.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5z" fill="currentColor"></path></g></svg>',TextNumberListLtr16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3.684 1.01c.193.045.33.21.33.402v3.294a.42.42 0 0 1-.428.412a.42.42 0 0 1-.428-.412V2.58a3.11 3.11 0 0 1-.664.435a.436.436 0 0 1-.574-.184a.405.405 0 0 1 .192-.552c.353-.17.629-.432.82-.661a2.884 2.884 0 0 0 .27-.388a.44.44 0 0 1 .482-.22zm-1.53 6.046a.401.401 0 0 1 0-.582l.002-.001V6.47l.004-.002l.008-.008a1.12 1.12 0 0 1 .103-.084a2.2 2.2 0 0 1 1.313-.435h.007c.32.004.668.084.947.283c.295.21.485.536.485.951c0 .452-.207.767-.488.992c-.214.173-.49.303-.714.409c-.036.016-.07.033-.103.049c-.267.128-.468.24-.61.39a.763.763 0 0 0-.147.22h1.635a.42.42 0 0 1 .427.411a.42.42 0 0 1-.428.412H2.457a.42.42 0 0 1-.428-.412c0-.51.17-.893.446-1.184c.259-.275.592-.445.86-.574c.043-.02.085-.04.124-.06c.231-.11.4-.19.529-.293c.12-.097.18-.193.18-.36c0-.148-.057-.23-.14-.289a.816.816 0 0 0-.448-.122a1.32 1.32 0 0 0-.818.289l-.005.005a.44.44 0 0 1-.602-.003zm.94 5.885a.42.42 0 0 1 .427-.412c.294 0 .456-.08.537-.15a.303.303 0 0 0 .11-.246c-.006-.16-.158-.427-.647-.427c-.352 0-.535.084-.618.137a.349.349 0 0 0-.076.062l-.003.004a.435.435 0 0 0 .01-.018v.001l-.002.002l-.002.004l-.003.006l-.005.008l.002-.003a.436.436 0 0 1-.563.165a.405.405 0 0 1-.191-.552v-.002l.002-.003l.003-.006l.008-.013a.71.71 0 0 1 .087-.12c.058-.067.142-.146.259-.22c.238-.153.59-.276 1.092-.276c.88 0 1.477.556 1.502 1.22c.012.303-.1.606-.339.84c.238.232.351.535.34.838c-.026.664-.622 1.22-1.503 1.22c-.502 0-.854-.122-1.092-.275a1.19 1.19 0 0 1-.326-.308a.71.71 0 0 1-.02-.033l-.008-.013l-.003-.005l-.001-.003v-.001l-.001-.001a.405.405 0 0 1 .19-.553a.436.436 0 0 1 .564.165l.003.004c.01.01.033.035.076.063c.083.053.266.137.618.137c.489 0 .641-.268.648-.428a.303.303 0 0 0-.11-.245c-.082-.072-.244-.151-.538-.151a.42.42 0 0 1-.427-.412zM7.75 3a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5z" fill="currentColor"></path></g></svg>',TaskListLtr24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6.707 3.293a1 1 0 0 0-1.414 0L4 4.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm14.296 13.7H10L9.883 17A1 1 0 0 0 10 18.993h11.003l.117-.006a1 1 0 0 0-.117-1.994zm0-5.993H10l-.117.007A1 1 0 0 0 10 13h11.003l.117-.007A1 1 0 0 0 21.003 11zm0-6H10l-.117.007A1 1 0 0 0 10 7h11.003l.117-.007A1 1 0 0 0 21.003 5zM6.707 16.293a1 1 0 0 0-1.414 0L4 17.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm-1.414-6.5a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 1 1 1.414-1.414l.293.293l1.293-1.293z" fill="currentColor"></path></g></svg>',TextAlignLeft24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignCenter24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M4 6a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1zm2 12a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1zm-3-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignRight24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M5 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1zm4 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H10a1 1 0 0 1-1-1zm-6-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignJustify24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',Search24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.5a7.5 7.5 0 0 1 5.964 12.048l4.743 4.745a1 1 0 0 1-1.32 1.497l-.094-.083l-4.745-4.743A7.5 7.5 0 1 1 10 2.5zm0 2a5.5 5.5 0 1 0 0 11a5.5 5.5 0 0 0 0-11z" fill="currentColor"></path></g></svg>',Add24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.883 3.007L12 3a1 1 0 0 1 .993.883L13 4v7h7a1 1 0 0 1 .993.883L21 12a1 1 0 0 1-.883.993L20 13h-7v7a1 1 0 0 1-.883.993L12 21a1 1 0 0 1-.993-.883L11 20v-7H4a1 1 0 0 1-.993-.883L3 12a1 1 0 0 1 .883-.993L4 11h7V4a1 1 0 0 1 .883-.993L12 3l-.117.007z" fill="currentColor"></path></g></svg>',DocumentEdit16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M8 1v3.5A1.5 1.5 0 0 0 9.498 6h3.5v1.035a2.548 2.548 0 0 0-1.37.712l-4.287 4.287a3.777 3.777 0 0 0-.994 1.755l-.302 1.209H4.5a1.5 1.5 0 0 1-1.5-1.5V2.5A1.5 1.5 0 0 1 4.5 1H8zm4.998 7.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.73 1.29l-.303 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6zm-4-6.81V4.5a.5.5 0 0 0 .5.5h3.25l-3.75-3.75z" fill="currentColor"></path></g></svg>',Star48Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832z" fill="currentColor"></path></svg>',CommentNote20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v4.5a2.5 2.5 0 0 0 2.5 2.5h6c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',ArrowRight20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.265 3.205a.75.75 0 0 0-1.03 1.09l5.239 4.955H2.75a.75.75 0 0 0 0 1.5h12.726l-5.241 4.957a.75.75 0 1 0 1.03 1.09l6.418-6.07a.995.995 0 0 0 .3-.566a.753.753 0 0 0-.002-.329a.995.995 0 0 0-.298-.557l-6.418-6.07z" fill="currentColor"></path></g></svg>',LinkOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0c-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0c59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z" fill="currentColor"></path></svg>',RollbackOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M793 242H366v-74c0-6.7-7.7-10.4-12.9-6.3l-142 112a8 8 0 0 0 0 12.6l142 112c5.2 4.1 12.9.4 12.9-6.3v-74h415v470H175c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h618c35.3 0 64-28.7 64-64V306c0-35.3-28.7-64-64-64z" fill="currentColor"></path></svg>',LikeOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7c0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 0 0-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 0 0 471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.5-5.4c47.6-20.3 78.3-66.8 78.3-118.4c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c-.2-12.6-2-25.1-5.6-37.1zM184 852V568h81v284h-81zm636.4-353l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 22.4-13.2 42.6-33.6 51.8H329V564.8l99.5-360.5a44.1 44.1 0 0 1 42.2-32.3c7.6 0 15.1 2.2 21.1 6.7c9.9 7.4 15.2 18.6 14.6 30.5l-9.6 198.4h314.4C829 418.5 840 436.9 840 456c0 16.5-7.2 32.1-19.6 43z" fill="currentColor"></path></svg>',DislikeOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 490.3c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-51.6-30.7-98.1-78.3-118.4a66.1 66.1 0 0 0-26.5-5.4H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h129.3l85.8 310.8C372.9 889 418.9 924 470.9 924c29.7 0 57.4-11.8 77.9-33.4c20.5-21.5 31-49.7 29.5-79.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3c40.4-23.5 65.5-66.1 65.5-111c0-28.3-9.3-55.5-26.1-77.7zM184 456V172h81v284h-81zm627.2 160.4H496.8l9.6 198.4c.6 11.9-4.7 23.1-14.6 30.5c-6.1 4.5-13.6 6.8-21.1 6.7a44.28 44.28 0 0 1-42.2-32.3L329 459.2V172h415.4a56.85 56.85 0 0 1 33.6 51.8c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-14 25.5l21.9 19a56.76 56.76 0 0 1 19.6 43c0 19.1-11 37.5-28.8 48.4z" fill="currentColor"></path></svg>',CommentOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path><path d="M894 345c-48.1-66-115.3-110.1-189-130v.1c-17.1-19-36.4-36.5-58-52.1c-163.7-119-393.5-82.7-513 81c-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4c5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6c17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5l-99 31l-1-104l-8-9c-84.6-103.2-90.2-251.9-11-361c96.4-132.2 281.2-161.4 413-66c132.2 96.1 161.5 280.6 66 412c-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10l1 104l-98-33l-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1C613.7 788.2 680.7 742.2 729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62c72.6 99.6 68.5 235.2-8 330z" fill="currentColor"></path><path d="M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path></svg>',IosCode:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g><path d="M332,142.7c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-13.8,13.2c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l95.8,91.5l-95.8,91.5c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l13.8,13.2c1.2,1.1,2.6,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l114.2-109c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L332,142.7z"></path><path d="M204,160.2c0-1.6-0.7-3.2-1.9-4.3l-13.8-13.2c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-114.2,109c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l114.2,109c1.2,1.1,2.7,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l13.8-13.2c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L106.3,256l95.8-91.5C203.3,163.4,204,161.8,204,160.2z"></path></g></svg>',IosNotificationsOutline:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g><path d="M289.7,403c-6.1,0-11.4,4.2-12.7,10.2c-1,4.5-2.7,8.2-5,10.9c-1.3,1.5-5.1,5.9-16.1,5.9c-11,0-14.8-4.5-16.1-5.9c-2.3-2.7-4-6.4-5-10.9c-1.3-6-6.6-10.2-12.7-10.2h0c-8.4,0-14.5,7.8-12.7,15.9c5,22.3,21,37.1,46.5,37.1s41.5-14.7,46.5-37.1C304.2,410.8,298,403,289.7,403L289.7,403z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9c0-13.3-10.8-24.6-24-24.6c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-13.2,0-24,11.3-24,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-38.5,9.1-79.6,46.5-79.6,139.8c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8h140.4h139.7C412.5,384,421.8,365.2,412,352.2z M373,358H139.8c-3.8,0-5.8-4.4-3.3-7.3c7-8,14.7-18.5,21-33.4c9.6-22.6,14.3-51.5,14.3-88.2c0-37.3,7-66.5,20.9-86.8c12.4-18.2,27.9-25.1,38.7-27.6c8.4-2,14.4-5.8,18.6-10.5c3.2-3.6,8.7-3.8,11.9-0.2c5.1,5.7,12,9.1,18.8,10.7c10.8,2.5,26.3,9.4,38.7,27.6c13.9,20.3,20.9,49.5,20.9,86.8c0,36.7,4.7,65.6,14.3,88.2c6.5,15.2,14.4,25.9,21.5,33.9C378.3,353.9,376.5,358,373,358z"></path></g></svg>',IosNotificationsOff:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g><path d="M255.9,456c31.1,0,48.1-22,48.1-53h-96.3C207.7,434,224.7,456,255.9,456z"></path><path d="M154.5,55c-2.5-4.3-7-6.8-11.6-7c0.1,0,0.2,0,0.3,0c-0.3,0-0.6,0-0.9,0c-0.1,0-0.2,0-0.3,0c-2.3,0-4.7,0.7-6.9,1.9c-6.8,3.9-9.1,12.6-5.1,19.3L357.5,457c2.6,4.5,7.4,7,12.3,7c2.4,0,4.9-0.6,7.2-1.9c6.8-3.9,9.1-12.6,5.1-19.3L154.5,55z"></path><path d="M296.1,384L159,150.5c-8.2,20.2-13.3,46-13.3,78.6c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8l140.4,0H296.1z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9c0-13.4-11-24.7-24.4-24.6c-13.4-0.2-24.4,11.2-24.4,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-8.7,2-17.5,5.5-25.9,10.8L366.1,384h29.9C412.5,384,421.9,365.2,412,352.2z"></path></g></svg>',IosClose:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><path d="M278.6,256l68.2-68.2c6.2-6.2,6.2-16.4,0-22.6c-6.2-6.2-16.4-6.2-22.6,0L256,233.4l-68.2-68.2c-6.2-6.2-16.4-6.2-22.6,0c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3l68.2,68.2l-68.2,68.2c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3c6.2,6.2,16.4,6.2,22.6,0l68.2-68.2l68.2,68.2c6.2,6.2,16.4,6.2,22.6,0c6.2-6.2,6.2-16.4,0-22.6L278.6,256z"></path></svg>',LockOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',UnlockOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v68c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-68c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zm-40 376H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',TextColor24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M17.75 14.5A2.25 2.25 0 0 1 20 16.75v3A2.25 2.25 0 0 1 17.75 22H5.25A2.25 2.25 0 0 1 3 19.75v-3a2.25 2.25 0 0 1 2.25-2.25h12.5zm0 1.5H5.25a.75.75 0 0 0-.75.75v3c0 .415.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-3a.75.75 0 0 0-.75-.75zM7.053 11.97l3.753-9.496c.236-.595 1.043-.63 1.345-.104l.05.105l3.747 9.5a.75.75 0 0 1-1.352.643l-.044-.092L13.556 10H9.443l-.996 2.52a.75.75 0 0 1-.876.454l-.097-.031a.75.75 0 0 1-.453-.876l.032-.098l3.753-9.495l-3.753 9.495zm4.45-7.178L10.036 8.5h2.928l-1.461-3.708z" fill="currentColor"></path></g></svg>',Color24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M3.839 5.858c2.94-3.916 9.03-5.055 13.364-2.36c4.28 2.66 5.854 7.777 4.1 12.577c-1.655 4.533-6.016 6.328-9.159 4.048c-1.177-.854-1.634-1.925-1.854-3.664l-.106-.987l-.045-.398c-.123-.934-.311-1.352-.705-1.572c-.535-.298-.892-.305-1.595-.033l-.351.146l-.179.078c-1.014.44-1.688.595-2.541.416l-.2-.047l-.164-.047c-2.789-.864-3.202-4.647-.565-8.157zm.984 6.716l.123.037l.134.03c.439.087.814.015 1.437-.242l.602-.257c1.202-.493 1.985-.54 3.046.05c.917.512 1.275 1.298 1.457 2.66l.053.459l.055.532l.047.422c.172 1.361.485 2.09 1.248 2.644c2.275 1.65 5.534.309 6.87-3.349c1.516-4.152.174-8.514-3.484-10.789c-3.675-2.284-8.899-1.306-11.373 1.987c-2.075 2.763-1.82 5.28-.215 5.816zm11.225-1.994a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm.494 3.488a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zM14.07 7.577a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zm-.028 8.998a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm-3.497-9.97a1.25 1.25 0 1 1 2.415-.646a1.25 1.25 0 0 1-2.415.646z" fill="currentColor"></path></g></svg>',FormatPainterOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M840 192h-56v-72c0-13.3-10.7-24-24-24H168c-13.3 0-24 10.7-24 24v272c0 13.3 10.7 24 24 24h592c13.3 0 24-10.7 24-24V256h32v200H465c-22.1 0-40 17.9-40 40v136h-44c-4.4 0-8 3.6-8 8v228c0 .6.1 1.3.2 1.9c-.1 2-.2 4.1-.2 6.1c0 46.4 37.6 84 84 84s84-37.6 84-84c0-2.1-.1-4.1-.2-6.1c.1-.6.2-1.2.2-1.9V640c0-4.4-3.6-8-8-8h-44V520h351c22.1 0 40-17.9 40-40V232c0-22.1-17.9-40-40-40zM720 352H208V160h512v192zM477 876c0 11-9 20-20 20s-20-9-20-20V696h40v180z" fill="currentColor"></path></svg>',Blockquote:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 15h15"></path><path d="M21 19H6"></path><path d="M15 11h6"></path><path d="M21 7h-6"></path><path d="M9 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path><path d="M3 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path></g></svg>',UserMultiple:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 30h-2v-5a5.006 5.006 0 0 0-5-5v-2a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M22 30h-2v-5a5.006 5.006 0 0 0-5-5H9a5.006 5.006 0 0 0-5 5v5H2v-5a7.008 7.008 0 0 1 7-7h6a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M20 2v2a5 5 0 0 1 0 10v2a7 7 0 0 0 0-14z" fill="currentColor"></path><path d="M12 4a5 5 0 1 1-5 5a5 5 0 0 1 5-5m0-2a7 7 0 1 0 7 7a7 7 0 0 0-7-7z" fill="currentColor"></path></svg>',Search24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.75a7.25 7.25 0 0 1 5.63 11.819l4.9 4.9a.75.75 0 0 1-.976 1.134l-.084-.073l-4.901-4.9A7.25 7.25 0 1 1 10 2.75zm0 1.5a5.75 5.75 0 1 0 0 11.5a5.75 5.75 0 0 0 0-11.5z" fill="currentColor"></path></g></svg>',Add24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.75 3a.75.75 0 0 1 .743.648l.007.102l.001 7.25h7.253a.75.75 0 0 1 .102 1.493l-.102.007h-7.253l.002 7.25a.75.75 0 0 1-1.493.101l-.007-.102l-.002-7.249H3.752a.75.75 0 0 1-.102-1.493L3.752 11h7.25L11 3.75a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',ArrowReplyDown24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M9.704 8.706A1 1 0 1 0 8.29 7.292l-4.997 5.004a1 1 0 0 0 0 1.413l4.997 4.998a1 1 0 1 0 1.415-1.414L6.41 14H13a8 8 0 0 0 7.996-7.75L21 6a1 1 0 1 0-2 0a6 6 0 0 1-5.775 5.996L13 12H6.414l3.29-3.294z" fill="currentColor"></path></g></svg>',SendAltFilled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M27.71 4.29a1 1 0 0 0-1.05-.23l-22 8a1 1 0 0 0 0 1.87l8.59 3.43L19.59 11L21 12.41l-6.37 6.37l3.44 8.59A1 1 0 0 0 19 28a1 1 0 0 0 .92-.66l8-22a1 1 0 0 0-.21-1.05z" fill="currentColor"></path></svg>',DocumentDownload:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 25l-1.414-1.414L26 26.172V18h-2v8.172l-2.586-2.586L20 25l5 5l5-5z" fill="currentColor"></path><path d="M18 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v3h2v-5a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h10zm0-23.6l5.6 5.6H18z" fill="currentColor"></path></svg>',FileUpload:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 3v4a1 1 0 0 0 1 1h4"></path><path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z"></path><path d="M12 11v6"></path><path d="M9 14l3-3l3 3"></path></g></svg>',DocumentEdit16Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M5 1a2 2 0 0 0-2 2v9.998a2 2 0 0 0 2 2h1.046l.25-1H5a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h3v2.5A1.5 1.5 0 0 0 9.498 6h2.5v1.44c.306-.209.647-.344 1-.405V5.413a1.5 1.5 0 0 0-.44-1.06L9.645 1.439A1.5 1.5 0 0 0 8.585 1H5zm6.791 4H9.5a.5.5 0 0 1-.5-.5V2.206l2.792 2.792zm1.207 3.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.722 1.257l-.009.033l-.302 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6z" fill="currentColor"></path></g></svg>',Star48Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832zM24 7.283l-4.82 9.764a2.45 2.45 0 0 1-1.844 1.34L6.56 19.954l7.798 7.601a2.45 2.45 0 0 1 .704 2.169l-1.84 10.732l9.638-5.067a2.45 2.45 0 0 1 2.28 0l9.638 5.067l-1.84-10.732a2.45 2.45 0 0 1 .704-2.169l7.798-7.6l-10.776-1.566a2.45 2.45 0 0 1-1.845-1.34L24 7.282z" fill="currentColor"></path></svg>',CommentNote20Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v1H4.6C3.704 4 3 4.713 3 5.566v6.71c0 .853.704 1.566 1.6 1.566h1.6V17h.003l.002-.001l4.276-3.157H15.4c.896 0 1.6-.713 1.6-1.566V10h.5c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',VideoClip24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M9.5 9.385v5.231c0 .57.61.931 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.659zM5.25 3A3.25 3.25 0 0 0 2 6.25v11.5A3.25 3.25 0 0 0 5.25 21h13.5A3.25 3.25 0 0 0 22 17.75V6.25A3.25 3.25 0 0 0 18.75 3H5.25zM3.5 6.25c0-.966.784-1.75 1.75-1.75h13.5c.966 0 1.75.784 1.75 1.75v11.5a1.75 1.75 0 0 1-1.75 1.75H5.25a1.75 1.75 0 0 1-1.75-1.75V6.25z" fill="currentColor"></path></g></svg>',Italic:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 5h6"></path><path d="M7 19h6"></path><path d="M14 5l-4 14"></path></g></svg>'};function xo(e){return l({name:`Icon${e}`,props:{size:{type:[String,Number],default:20},color:{type:String,default:"currentColor"}},setup:t=>()=>{const a=yo[e];if(!a)return R("span",{style:{color:"red"}},"?");const o="number"==typeof t.size?`${t.size}px`:t.size,n=function(e,t){const a=(new DOMParser).parseFromString(e,"image/svg+xml").documentElement;if("svg"!==a.tagName)return R("span",{style:{color:"red"}},"?");const o="number"==typeof t.size?`${t.size}px`:t.size;return function e(a){const n=a.tagName.toLowerCase(),l={};for(let o=0;o<a.attributes.length;o++){const e=a.attributes[o];let n=e.value;"fill"===e.name&&"currentColor"===n||"stroke"===e.name&&"currentColor"===n?n=t.color:"fill"===e.name&&"none"===n&&(n="none"),l[e.name]=n}"svg"===n&&(l.width=o,l.height=o,delete l.width,delete l.height,l.width=o,l.height=o);const i=[];for(let t=0;t<a.children.length;t++)i.push(e(a.children[t]));return a.textContent&&0===a.children.length?R(n,l,a.textContent.trim()):R(n,l,i)}(a)}(a,t);return R("span",{class:"icon-wrapper",style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:o,height:o,lineHeight:1,color:t.color}},[n])}})}const ko=xo("TextHeader120Filled"),Co=xo("TextHeader220Filled"),Lo=xo("TextHeader320Filled"),Mo=xo("TextBold20Filled"),So=xo("TextUnderline24Filled"),Ao=xo("TextStrikethrough20Filled"),Ro=xo("Code20Filled"),Eo=xo("Image28Regular"),zo=xo("ArrowUndo16Filled"),To=xo("ArrowRedo16Filled"),Io=xo("LineHorizontal120Filled"),_o=xo("FullScreenMaximize16Filled"),Bo=xo("ResizeSmall20Filled"),$o=xo("TextBulletListLtr16Filled"),Po=xo("TextNumberListLtr16Filled"),Uo=xo("TaskListLtr24Filled"),Fo=xo("TextAlignLeft24Filled"),Ho=xo("TextAlignCenter24Filled"),Vo=xo("TextAlignRight24Filled"),Oo=xo("TextAlignJustify24Filled"),Do=xo("ArrowRight20Filled"),jo=xo("LinkOutlined"),No=xo("RollbackOutlined"),qo=xo("LikeOutlined"),Yo=xo("DislikeOutlined"),Jo=xo("CommentOutlined"),Wo=xo("IosCode"),Ko=xo("IosNotificationsOutline"),Go=xo("IosNotificationsOff"),Xo=xo("IosClose"),Qo=xo("LockOutlined"),Zo=xo("UnlockOutlined"),en=xo("TextColor24Regular"),tn=xo("Color24Regular"),an=xo("FormatPainterOutlined"),on=xo("Blockquote"),nn=xo("UserMultiple"),ln=xo("Search24Regular"),rn=xo("Add24Regular"),sn=xo("ArrowReplyDown24Filled"),cn=xo("SendAltFilled"),dn=xo("DocumentDownload"),un=xo("FileUpload"),mn=xo("DocumentEdit16Regular"),pn=xo("Star48Regular"),vn=xo("CommentNote20Regular"),hn=xo("VideoClip24Regular"),gn=xo("Italic"),fn=yo.TextHeader120Filled,wn=yo.TextHeader220Filled,bn=yo.TextHeader320Filled,yn=yo.TextBold20Filled,xn=yo.TextUnderline24Filled,kn=yo.TextStrikethrough20Filled,Cn=yo.Code20Filled,Ln=yo.Image28Regular,Mn=yo.LineHorizontal120Filled,Sn=yo.TextBulletListLtr16Filled,An=yo.TextNumberListLtr16Filled,Rn=yo.TaskListLtr24Filled,En=yo.LinkOutlined,zn=yo.Blockquote,Tn=yo.VideoClip24Regular,In=yo.Italic,_n=(e,t)=>[{id:"heading1",name:"标题 1",icon:fn,keywords:"h1 标题 heading",shortcut:"Ctrl+Alt+1",action:e=>e.chain().focus().toggleHeading({level:1}).run()},{id:"heading2",name:"标题 2",icon:wn,keywords:"h2 标题 heading",shortcut:"Ctrl+Alt+2",action:e=>e.chain().focus().toggleHeading({level:2}).run()},{id:"heading3",name:"标题 3",icon:bn,keywords:"h3 标题 heading",shortcut:"Ctrl+Alt+3",action:e=>e.chain().focus().toggleHeading({level:3}).run()},"|",{id:"bold",name:"加粗",icon:yn,keywords:"bold 加粗 粗体 b",shortcut:"Ctrl+B",action:e=>e.chain().focus().toggleBold().run()},{id:"italic",name:"斜体",icon:In,keywords:"italic 斜体 i",shortcut:"Ctrl+I",action:e=>e.chain().focus().toggleItalic().run()},{id:"strike",name:"删除线",icon:kn,keywords:"strike 删除线 strikethrough",action:e=>e.chain().focus().toggleStrike().run()},{id:"underline",name:"下划线",icon:xn,keywords:"underline 下划线 u",shortcut:"Ctrl+U",action:e=>e.chain().focus().toggleUnderline().run()},{id:"code",name:"行内代码",icon:Cn,keywords:"code 代码 行内代码 inline",shortcut:"Ctrl+E",action:e=>e.chain().focus().toggleCode().run()},"|",{id:"bulletList",name:"无序列表",icon:Sn,keywords:"ul 列表 list bullet",action:e=>e.chain().focus().toggleBulletList().run()},{id:"orderedList",name:"有序列表",icon:An,keywords:"ol 列表 list ordered number",action:e=>e.chain().focus().toggleOrderedList().run()},{id:"taskList",name:"任务列表",icon:Rn,keywords:"todo 任务 task checklist",action:e=>e.chain().focus().toggleTaskList().run()},"|",{id:"blockquote",name:"引用",icon:zn,keywords:"quote 引用 blockquote",action:e=>e.chain().focus().toggleBlockquote().run()},{id:"codeBlock",name:"代码块",icon:Cn,keywords:"code 代码 codeblock",shortcut:"Ctrl+Alt+C",action:e=>e.chain().focus().toggleCodeBlock().run()},"|",{id:"image",name:"图片",icon:Ln,keywords:"image 图片 img picture",action:e=>{}},{id:"link",name:"链接",icon:En,keywords:"link 链接 url",action:e=>{t&&t("插入链接",(()=>{}),!1)}},{id:"bilibili",name:"B站视频",icon:Tn,keywords:"bilibili b站 视频 video",action:e=>{t&&t("插入bilibili视频链接",(()=>{}),!0)}},{id:"horizontalRule",name:"分割线",icon:Mn,keywords:"hr 分割线 divider line",action:e=>e.chain().focus().setHorizontalRule().run()}],Bn=Ge.extend({addNodeView(){return e=>{const t=document.createElement("li");t.setAttribute("data-type","taskItem"),t.dataset.checked=e.node.attrs.checked;const a=document.createElement("label");a.className="cst-task-label",a.style.display="flex",a.style.alignItems="flex-start",a.style.marginTop="0.2rem";const o=document.createElement("input");o.type="checkbox",o.checked=e.node.attrs.checked,o.style.display="none",o.className="cst-task-checkbox";const n=document.createElement("span");n.className="cst-task-checkbox-wrapper",n.style.display="inline-flex",n.style.justifyContent="center",n.style.alignItems="center",n.style.width="1rem",n.style.height="1rem",n.style.border="1px solid #ccc",n.style.borderRadius="0.25rem",n.style.marginRight="0.5rem",n.style.position="relative",n.style.cursor=e.editor.isEditable?"pointer":"default",n.style.transition=e.editor.isEditable?"background-color 0.2s ease":"none",n.style.flexShrink="0",n.style.marginTop="0.2rem",e.editor.isEditable||(n.style.cursor="not-allowed",n.setAttribute("aria-disabled","true"),n.title="只读模式下不可更改",a.style.transition="none",a.style.transform="none");const l=document.createElement("span");function i(){n.style.backgroundColor=o.checked?"var(--purple-contrast)":"white",l.style.opacity=o.checked?"1":"0",!e.editor.isEditable&&o.checked&&(n.style.backgroundColor="var(--purple-contrast-disabled, #a095c3)")}l.className="cst-task-checkmark",l.textContent="✓",l.style.color="white",l.style.fontSize="0.75rem",l.style.opacity=o.checked?"1":"0",l.style.transition=e.editor.isEditable?"opacity 0.2s ease":"none",i(),n.appendChild(l),n.addEventListener("mousedown",(a=>{if(a.preventDefault(),a.stopPropagation(),e.editor.isEditable)o.checked=!o.checked,i(),"function"==typeof e.getPos&&e.editor.commands.command((({tr:t})=>(t.setNodeMarkup(e.getPos(),void 0,{...e.node.attrs,checked:o.checked}),!0))),t.dataset.checked=o.checked.toString();else if(this.options.onReadOnlyChecked){const a=!o.checked;if(!1===this.options.onReadOnlyChecked(e.node,a))return;o.checked=a,i(),"function"==typeof e.getPos&&e.editor.commands.command((({tr:t})=>(t.setNodeMarkup(e.getPos(),void 0,{...e.node.attrs,checked:a}),!0))),t.dataset.checked=a.toString()}}));const r=document.createElement("div");return r.className="cst-task-content",r.style.flex="1",a.appendChild(o),a.appendChild(n),t.appendChild(a),t.appendChild(r),{dom:t,contentDOM:r}}}}),$n=Pt(Ut),Pn=[["document",Xe],["paragraph",Qe],["text",Ze],["image",no],["dropcursor",et],["bold",tt],["italic",at],["strike",ot],["underline",nt],["code",lt],["heading",it],["bulletList",rt.configure({keepMarks:!0})],["orderedList",st.configure({keepMarks:!0})],["listItem",ct],["taskList",dt.configure({itemTypeName:"taskItem"})],["taskItem",Bn.configure({nested:!0,onReadOnlyChecked:(e,t)=>!1})],["blockquote",ut],["textStyle",mt],["color",pt.configure({types:["textStyle"]})],["backgroundColor",vt.configure({multicolor:!0})],["codeBlockLowlight",Ua.configure({lowlight:$n})],["horizontalRule",ht],["link",gt.configure({defaultProtocol:"https"})],["history",ft],["typography",wt],["markdown",Et.configure({transformPastedText:!0})],["focus",bt.configure({mode:"deepest"})],["gapcursor",yt],["mention",fo],["bilibili",Ea],["floatingMenu",xt],["bubbleMenu",kt],["align",Ct.configure({types:["heading","paragraph","blockquote"],alignments:["left","center","right","justify"],defaultAlignment:"left"})],["fullscreen",Fa],["slashMenu",bo.configure({items:_n()})]],Un={extensionMap:new Map(Pn),replaceImageUrls:(e,t=!1)=>{var a;const o=no,n=o.storage.transformSrc,l=o.storage.getFullUrl;null==(a=null==e?void 0:e.content)||a.forEach((e=>{if("image"===e.type&&e.attrs&&e.attrs.src)if(t)e.attrs.src=l?l(e.attrs.src):e.attrs.src.replace("https://",Nt.backend.resourceURL);else if(n)e.attrs.src=n(e.attrs.src);else{const t=Nt.backend.resourceURL;e.attrs.src.startsWith(t)&&(e.attrs.src=e.attrs.src.substring(t.length))}e.content&&Un.replaceImageUrls(e,t)}))},toJsonString:e=>{const t=no.storage.transformSrc,a=JSON.parse(JSON.stringify(e)),o=e=>{if(e){if("image"===e.type&&e.attrs&&e.attrs.src){const a=e.attrs.src;if(t)e.attrs.src=t(a);else{const t=Nt.backend.resourceURL;if(a.startsWith(t))e.attrs.src=a.substring(t.length);else if(a.startsWith("http"))try{const t=new URL(a);e.attrs.src=t.pathname}catch{Wt.warn("Failed to parse URL:",a)}}Wt.debug("Image URL transformed:",{original:a,transformed:e.attrs.src})}e.content&&Array.isArray(e.content)&&e.content.forEach(o)}};return o(a),Kt.stringify(a)},toJsonObject:e=>{if(!e)return Wt.warn("Empty content passed to toJsonObject"),{type:"doc",content:[{type:"paragraph",content:[]}]};try{if("object"==typeof e)return e;const t=Kt.parse(e);return t||(Wt.warn("Failed to parse content in toJsonObject, creating fallback structure"),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"无法显示内容"}]}]})}catch(t){return Wt.error("Error in toJsonObject:",t),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"解析错误"}]}]}}},serializeContent:e=>{let t="";if(!e)return t;return function e(a){var o;"text"===a.type?t+=a.text:"mention"===a.type?t+="@"+(null==(o=a.attrs)?void 0:o.label):"image"===a.type?t+="[图片]":a.content&&Array.isArray(a.content)&&a.content.forEach((t=>e(t)))}(e),t}},Fn={URL:"/core/articles",search:async(e,t)=>(await fa(Fn.URL,e,{signal:t}).catch((e=>ga(e)))).data,title:async e=>(await fa(Fn.URL+"/title/"+e).catch((e=>ga(e)))).data,detail:async e=>(await fa(Fn.URL+"/detail/"+e).catch((e=>ga(e)))).data,save:async e=>(await wa(Fn.URL,e).catch((e=>ga(e)))).data,edit:async e=>(await ya(Fn.URL,e).catch((e=>ga(e)))).data,togglePublishedScope:async e=>(await ya(`${Fn.URL}/toggle-scope/${e}`).catch((e=>ga(e)))).data,getHotTags:async(e=5)=>(await fa(`${Fn.URL}/hot-tags`,{limit:e}).catch((e=>ga(e)))).data,md:async(e,t)=>{var a;try{const o=await fa(Fn.URL+"/md/"+e);if(o&&"data"in o){const n=o.data.data.split(/\r?\n/),l=n.pop()||"";t.setContent(Un.toJsonObject(l));const i=t.getMarkdown(),r=new Blob([n.join("\n")+"\n"+i],{type:"text/markdown"}),s=window.URL.createObjectURL(r),c=document.createElement("a");c.href=s;let d=decodeURIComponent((null==(a=o.data.headers["content-disposition"])?void 0:a.split("filename=")[1])||`article-${e}`);d+=".md",document.body.appendChild(c),c.download=d,c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(s)}}catch(o){ga(o)}},delete:async e=>(await ka(`${Fn.URL}/${e}`).catch((e=>ga(e)))).data},Hn=a("article",{state:()=>({id:""}),actions:{setId(e){this.id=e}},getters:{getId:e=>e.id}}),Vn=a("comment",{state:()=>({id:""}),actions:{setId(e){this.id=e}},getters:{getId:e=>e.id}}),On=l({__name:"SearchUserSelect",props:{modelValue:{},placeholder:{}},emits:["update:modelValue"],setup(e,{expose:t,emit:a}){const o=e,l=a,r=n(!1),s=n([]),c=i((()=>{const e=[...s.value];return o.modelValue.forEach((t=>{e.some((e=>e.id===t.id))||e.push(t)})),e})),d=e=>{const t=ja.getResourceURL(e.avatar)||"";return Wt.debug("render user label: ",e),v(S,null,[v(te,{size:"small",round:!0,"object-fit":"cover",src:t,fallbackSrc:"/avatar/avatar.png",style:{marginRight:"8px",verticalAlign:"middle"}},null),v("span",null,[e.username])])},p=i({get:()=>o.modelValue.map((e=>e.id)),set:e=>{const t=o.modelValue.filter((t=>e.includes(t.id))),a=c.value.filter((a=>e.includes(a.id)&&!t.some((e=>e.id===a.id))));l("update:modelValue",[...t,...a])}}),g=e=>{e.trim()?(r.value=!0,io.searchUser(e).then((e=>{e.data&&(s.value=e.data)})).finally((()=>{r.value=!1}))):s.value=[]};return t({reset:()=>{s.value=[]}}),(e,t)=>(m(),u(h(ee),{value:p.value,"onUpdate:value":t[0]||(t[0]=e=>p.value=e),filterable:"",clearable:"",multiple:"",remote:"","value-field":"id","label-field":"username",options:c.value,loading:r.value,placeholder:e.placeholder,onSearch:g,"render-label":d},null,8,["value","options","loading","placeholder"]))}}),Dn=qe.create({name:"characterCount",addOptions:()=>({limit:void 0}),addStorage:()=>({characters:0}),addCommands:()=>({getCharacterCount:()=>({editor:e})=>(e.storage.characterCount.characters,!0)}),addProseMirrorPlugins(){const e=this;return[new Tt({key:new It("characterCount"),view:()=>({update:t=>{const{doc:a}=t.state,o=(e=>{let t=0;return e.descendants((e=>{var a;e.isText?t+=(null==(a=e.text)?void 0:a.length)||0:"image"===e.type.name?t+=100:"bilibili"===e.type.name&&(t+=800)})),t})(a);e.storage.characters=o}}),props:{handleKeyDown:(t,a)=>{if(!e.options.limit)return!1;if(e.storage.characters<e.options.limit)return!1;return!(e=>{const t=e.ctrlKey&&"a"===e.key.toLowerCase()||e.ctrlKey&&"c"===e.key.toLowerCase()||e.ctrlKey&&"x"===e.key.toLowerCase()||e.ctrlKey&&"z"===e.key.toLowerCase()||e.ctrlKey&&"y"===e.key.toLowerCase()||e.shiftKey&&["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(e.key);return["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End","Tab","Escape","Enter"].includes(e.key)||t})(a)&&(a.preventDefault(),!0)},handlePaste:(t,a)=>{if(!e.options.limit)return!1;const o=e.storage.characters;if(o>=e.options.limit)return a.preventDefault(),!0;const n=a.clipboardData;if(!n)return!1;const l=n.getData("text/plain");if(!l)return!1;return o+l.length>e.options.limit&&(a.preventDefault(),!0)},handleTextInput:(t,a,o,n)=>{if(!e.options.limit)return!1;const l=e.storage.characters;if(l>=e.options.limit)return!0;return l+n.length-(o-a)>e.options.limit}},appendTransaction:(t,a,o)=>{if(!e.options.limit)return null;if(e.storage.characters>=e.options.limit){const e=o.tr;let t=!1;if(e.steps.forEach((e=>{if(e instanceof Object&&"from"in e&&"to"in e&&"slice"in e){const a=e,o=a.to-a.from;a.slice.content.size>o&&(t=!0)}})),t)return null}return null}})]}}),jn=qe.create({name:"formatPainter",addOptions:()=>({enabled:!0}),addStorage:()=>({formatPainter:{isActive:!1,sourceMarks:null,sourceNode:null}}),addCommands:()=>({toggleFormatPainter:()=>({editor:e,state:t})=>{const{formatPainter:a}=e.storage,{selection:o}=t,{from:n,to:l}=o;if(a.isActive){if(a.sourceMarks){const o=t.tr;t.doc.nodesBetween(n,l,((e,t)=>{e.isText&&o.removeMark(t,t+e.nodeSize,null)})),a.sourceMarks.forEach((e=>{o.addMark(n,l,e)})),e.view.dispatch(o)}return a.isActive=!1,a.sourceNode=null,a.sourceMarks=null,!0}{const e=t.doc.nodeAt(n);return!!e&&(a.isActive=!0,a.sourceNode=e,a.sourceMarks=e.marks,!0)}}}),addProseMirrorPlugins:()=>[new Tt({key:new It("formatPainter"),props:{handleClick:(e,t)=>{const a=e.editor;if(!a)return!1;const{formatPainter:o}=a.storage;return!!(null==o?void 0:o.isActive)&&(a.commands.toggleFormatPainter(),!0)}}})]}),Nn=(e,t,a,o)=>((e,t,a,o)=>{const l=n(),i=Lt.configure({placeholder:e.placeholder}),r=Dn.configure({limit:e.characterLimit}),c=jn.configure({enabled:!0});return{editor:l,initEditor:()=>{var n;Un.extensionMap.set("placeholder",i),Un.extensionMap.set("characterCount",r),Un.extensionMap.set("formatPainter",c),void 0!==e.useThumbnail&&Un.extensionMap.set("image",oo(e.useThumbnail)),(a||o)&&Un.extensionMap.set("slashMenu",bo.configure({items:_n(0,o),imageUploadTrigger:a,modalTrigger:o}));const s=[];let d=new Set(["document","paragraph","text",...e.extensions]);return e.allExtensions?(s.push(...Un.extensionMap.values()),d=new Set([...Un.extensionMap.keys()])):d.forEach((e=>{const t=Un.extensionMap.get(e);t?s.push(t):va.warning(`Unknown extension: ${e}`)})),Wt.debug("extensions: ",s),l.value=new Mt({extensions:s,content:Object.keys(e.modelValue).length?e.modelValue:"",editable:e.editable,editorProps:e.editorProps}),null==(n=l.value)||n.on("update",(()=>{var e;const a=(null==(e=l.value)?void 0:e.getJSON())||null;Wt.debug("update content: ",a),t("update:modelValue",a)})),d},watchEditable:()=>{s((()=>e.editable),(e=>{l.value&&(l.value.setEditable(e),requestAnimationFrame((()=>{try{document.querySelectorAll(".cst-task-checkbox-wrapper").forEach((t=>{if(e){t.classList.remove("readonly-checkbox"),t.removeAttribute("aria-disabled"),t.removeAttribute("title"),t.style.cursor="pointer",t.style.transition="background-color 0.2s ease",t.style.transform="";const e=t.closest("label");e&&(e.style.transition="transform 0.15s ease",e.style.transform="",e.style.pointerEvents="")}else{t.classList.add("readonly-checkbox"),t.setAttribute("aria-disabled","true"),t.setAttribute("title","只读模式下不可更改"),t.style.cursor="not-allowed",t.style.transition="none",t.style.transform="none";const e=t.closest("label");e&&(e.style.transition="none",e.style.transform="none")}}))}catch(t){Wt.error("Error updating task checkboxes in readonly mode:",t)}})))}))},clearContent:()=>{var e;null==(e=l.value)||e.commands.clearContent()},setContent:e=>{var t;null==(t=l.value)||t.commands.setContent(e)},getMarkdown:()=>{var e,t;return null==(t=null==(e=l.value)?void 0:e.storage.markdown)?void 0:t.getMarkdown()},cleanupEditor:()=>{setTimeout((()=>{var e;null==(e=l.value)||e.destroy()}),1e3)}}})(e,t,a,o),qn=(e,t,a,o)=>{const{selectBubbleMenu:n,setupEditorEvents:l}=((e,t,a,o)=>{const n=E({image:!1,bilibili:!1});return{selectBubbleMenu:n,setupEditorEvents:()=>{if(!e.value)return;if(e.value.on("update",(()=>{var t;(null==(t=e.value)?void 0:t.isEditable)||Promise.resolve().then((()=>{try{document.querySelectorAll(".ProseMirror-selectednode").forEach((e=>{e.classList.remove("ProseMirror-selectednode")}))}catch(e){Wt.error("Error removing selection in readonly mode:",e)}}))})),!e.value.isEditable){const t=e.value.view.dom,a=e=>{(e.ctrlKey||e.metaKey)&&(e.preventDefault(),e.stopPropagation())};t.addEventListener("mousedown",a,!0);const o=()=>{t.removeEventListener("mousedown",a,!0)};t.dataset.cleanupRegistered||(t.dataset.cleanupRegistered="true",e.value.on("destroy",o))}e.value.on("selectionUpdate",(()=>{var t;const a=null==(t=e.value)?void 0:t.state.selection,o=null==a?void 0:a.node;n.image=!1,n.bilibili=!1,(null==o?void 0:o.type)&&(n.image=!1,n.bilibili="bilibili"===o.type.name,"image"===o.type.name&&ma("tiptap-selection-update",(()=>{const e=document.querySelector(".ProseMirror-selectednode");if(e){const t=e.getBoundingClientRect();t.top>=0&&t.bottom<=window.innerHeight||e.scrollIntoView({behavior:"auto",block:"nearest"})}}),150))})),e.value.on("paste",(n=>{var l,i;if(!e.value)return;const r=(null==(l=n.slice.content.firstChild)?void 0:l.attrs)||{},s=Array.from((null==(i=n.event.clipboardData)?void 0:i.files)||[]);s.some((e=>e.type.startsWith("image/")))&&(Wt.debug("Detected image files in clipboard"),t(e.value,s,r,a,o))})),e.value.on("drop",(n=>{var l,i;if(!e.value)return;const r=(null==(l=n.slice.content.firstChild)?void 0:l.attrs)||{},s=Array.from((null==(i=n.event.dataTransfer)?void 0:i.files)||[]);s.some((e=>e.type.startsWith("image/")))&&(Wt.debug("Detected image files in drop event"),t(e.value,s,r,a,o))}));let l=null;e.value.on("transaction",(t=>{var a,o;const n=t;if(!(null==(a=e.value)?void 0:a.isEditable)||!(null==(o=n.transaction)?void 0:o.selectionSet))return;const i=e.value.state.selection.from+"-"+e.value.state.selection.to;i!==l&&(l=i,ma("tiptap-image-update",(()=>{var t,a;try{const o=null==(a=null==(t=e.value)?void 0:t.view)?void 0:a.dom;if(!o)return;o.querySelectorAll(".image-wrapper").forEach((e=>{const t=e.classList.contains("ProseMirror-selectednode");e.querySelectorAll(".resize-handle").forEach((e=>{const a=e,o=t;o!==("none"!==a.style.display)&&(o?(a.style.display="block",a.style.visibility="visible",a.style.opacity="1",a.style.pointerEvents="all"):(a.style.display="none",a.style.visibility="hidden",a.style.opacity="0",a.style.pointerEvents="none"))}))}))}catch(o){Wt.error("Error updating image handles:",o)}}),100))}))}}})(e,t,a,o);return{selectBubbleMenu:n,setupEditorEvents:l}},Yn={class:"tiptap-btn-wrapper"},Jn=Sa(l({__name:"TiptapBtn",props:{show:{type:Boolean,default:!0},trigger:{type:Function,default:()=>{}},icon:{type:Object,required:!0},size:{type:String,default:"20"},isActive:{type:Boolean,default:!1},tooltip:{type:String,default:""},disabled:{type:Boolean,default:!1}},setup:e=>(t,a)=>(m(),u(h(ae),{trigger:"hover","theme-overrides":{padding:"4px 8px",textColor:"#333",color:"#fff",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"}},{trigger:p((()=>[g("div",Yn,[e.show?(m(),u(h(oe),{key:0,class:b(["padding-4",{"is-active":e.isActive}]),quaternary:"",size:"small",onClick:e.trigger,disabled:e.disabled},{default:p((()=>[(m(),u(I(e.icon),{size:e.size},null,8,["size"])),T(t.$slots,"content",{},void 0,!0)])),_:3},8,["onClick","class","disabled"])):M("",!0)])])),default:p((()=>[z(" "+w(e.tooltip),1)])),_:3}))}),[["__scopeId","data-v-0648a6e6"]]),Wn=Sa(l({__name:"ColorPicker",props:{show:{type:Boolean,default:!0},type:{type:String,required:!0},editor:{type:Object,required:!0},colorType:{type:String,default:"color"}},setup(e){const t=e,a=i((()=>"color"===t.colorType?en:tn)),o=i((()=>{if(!t.editor)return!1;try{if("color"===t.colorType){const e=t.editor.isActive("textStyle"),a=t.editor.getAttributes("textStyle");return e&&!!a.color}return t.editor.isActive("highlight")}catch(e){return!1}})),l=n({visible:!1,value:"#000000FF",type:""}),r=i({get:()=>l.value.value,set:e=>{l.value.value=e}}),s=i((()=>l.value.visible&&l.value.type===t.type)),c=()=>"",d=["#FFFFFF","#18A058","#2080F0","#F0A020","rgba(208, 48, 80, 1)"],g=e=>{const t=document.querySelectorAll(".n-color-picker"),o=document.querySelectorAll(".n-button");let n=!1;t.forEach((t=>{t.contains(e.target)&&(n=!0)})),o.forEach((t=>{var o;t.contains(e.target)&&(null==(o=t.querySelector(".n-icon"))?void 0:o.innerHTML.includes(a.value.name||""))&&(n=!0)})),n||(l.value.visible=!1,document.removeEventListener("click",g))},f=e=>{"color"===t.colorType?ma(ca,(()=>{var a;return null==(a=t.editor)?void 0:a.chain().focus().setColor(e).run()})):ma(ca,(()=>{var a;return null==(a=t.editor)?void 0:a.chain().focus().setHighlight({color:e}).run()}))};return _((()=>{document.removeEventListener("click",g)})),(t,n)=>(m(),u(Jn,{icon:a.value,show:e.show,trigger:()=>{return t=e.type,l.value.visible=l.value.type!==t||!l.value.visible,l.value.type=t,void(l.value.visible?setTimeout((()=>{document.addEventListener("click",g)}),0):document.removeEventListener("click",g));var t},"is-active":o.value},{content:p((()=>[v(h(ne),{"show-preview":!1,size:"small",placement:"top",to:"body",value:r.value,"onUpdate:value":[n[0]||(n[0]=e=>r.value=e),f],"popover-style":"min-width: 220px; z-index: 1000;","render-label":c,swatches:d,show:s.value},null,8,["value","show"])])),_:1},8,["icon","show","trigger","is-active"]))}}),[["__scopeId","data-v-43be4655"]]),Kn=l({__name:"ToolbarButtonGroup",props:{buttons:{},editor:{},extensionsSet:{},showModal:{type:Function},modal:{}},emits:["image-upload","toggle-fullscreen"],setup(e,{emit:t}){const a=e,o=t,n=i((()=>a.buttons.filter((e=>a.extensionsSet.has(e.extensionName)))));return(e,t)=>(m(!0),f(S,null,A(n.value,(t=>{var n;return m(),u(Jn,{key:t.tooltip,icon:t.icon,show:e.extensionsSet.has(t.extensionName),trigger:()=>(e=>{e.emit?"image-upload"===e.emit?o("image-upload"):"toggle-fullscreen"===e.emit&&o("toggle-fullscreen"):e.trigger(a.editor,a.showModal,a.modal)})(t),"is-active":(null==(n=t.isActive)?void 0:n.call(t,e.editor))||!1,tooltip:t.tooltip},null,8,["icon","show","trigger","is-active","tooltip"])})),128))}}),Gn={textFormat:[{icon:Mo,extensionName:"bold",trigger:e=>null==e?void 0:e.chain().focus().toggleBold().run(),isActive:e=>null==e?void 0:e.isActive("bold"),tooltip:"加粗"},{icon:gn,extensionName:"italic",trigger:e=>null==e?void 0:e.chain().focus().toggleItalic().run(),isActive:e=>null==e?void 0:e.isActive("italic"),tooltip:"斜体"},{icon:Ao,extensionName:"strike",trigger:e=>null==e?void 0:e.chain().focus().toggleStrike().run(),isActive:e=>null==e?void 0:e.isActive("strike"),tooltip:"删除线"},{icon:So,extensionName:"underline",trigger:e=>null==e?void 0:e.chain().focus().toggleUnderline().run(),isActive:e=>null==e?void 0:e.isActive("underline"),tooltip:"下划线"},{icon:Wo,extensionName:"code",trigger:e=>null==e?void 0:e.chain().focus().toggleCode().run(),isActive:e=>null==e?void 0:e.isActive("code"),tooltip:"行内代码"}],heading:[{icon:ko,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:1}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:1}),tooltip:"标题1"},{icon:Co,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:2}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:2}),tooltip:"标题2"},{icon:Lo,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:3}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:3}),tooltip:"标题3"}],list:[{icon:$o,extensionName:"bulletList",trigger:e=>null==e?void 0:e.chain().focus().toggleBulletList().run(),isActive:e=>null==e?void 0:e.isActive("bulletList"),tooltip:"无序列表"},{icon:Po,extensionName:"orderedList",trigger:e=>null==e?void 0:e.chain().focus().toggleOrderedList().run(),isActive:e=>null==e?void 0:e.isActive("orderedList"),tooltip:"有序列表"},{icon:Uo,extensionName:"taskList",trigger:e=>null==e?void 0:e.chain().focus().toggleTaskList().run(),isActive:e=>null==e?void 0:e.isActive("taskList"),tooltip:"任务列表"}],align:[{icon:Fo,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("left").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"left"}),tooltip:"左对齐"},{icon:Ho,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("center").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"center"}),tooltip:"居中对齐"},{icon:Vo,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("right").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"right"}),tooltip:"右对齐"},{icon:Oo,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("justify").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"justify"}),tooltip:"两端对齐"}],other:[{icon:on,extensionName:"blockquote",trigger:e=>null==e?void 0:e.chain().focus().toggleBlockquote().run(),isActive:e=>null==e?void 0:e.isActive("blockquote"),tooltip:"引用"},{icon:Ro,extensionName:"codeBlockLowlight",trigger:e=>null==e?void 0:e.chain().focus().toggleCodeBlock().run(),isActive:e=>null==e?void 0:e.isActive("codeBlock"),tooltip:"代码块"},{icon:Io,extensionName:"horizontalRule",trigger:e=>null==e?void 0:e.chain().focus().setHorizontalRule().run(),tooltip:"分割线"},{icon:jo,extensionName:"link",trigger:(e,t)=>null==t?void 0:t("插入链接",(()=>{})),isActive:e=>null==e?void 0:e.isActive("link"),tooltip:"链接"},{icon:Eo,extensionName:"image",trigger:()=>{},tooltip:"图片",emit:"image-upload"},{icon:hn,extensionName:"bilibili",trigger:(e,t,a)=>null==t?void 0:t("插入bilibili视频链接",(()=>null==e?void 0:e.commands.setBilibiliVideo({src:null==a?void 0:a.inputValue})),!0),tooltip:"B站视频"},{icon:zo,extensionName:"history",trigger:e=>null==e?void 0:e.chain().focus().undo().run(),tooltip:"撤销"},{icon:To,extensionName:"history",trigger:e=>null==e?void 0:e.chain().focus().redo().run(),tooltip:"重做"}]},Xn={key:0,class:"editor-bubble-menu"},Qn={key:1,class:"editor-bubble-menu"},Zn={key:2,class:"editor-bubble-menu"},el=l({__name:"EditorBubbleMenu",props:{editor:{},extensionsSet:{},selectBubbleMenu:{}},emits:["show-modal"],setup(e,{emit:t}){const a={textFormat:Gn.textFormat,align:Gn.align,other:[Gn.other[0]]},o=t,n={duration:100,appendTo:document.body},l=(e,t,a=!1)=>{o("show-modal",{title:e,trigger:t,onlyInputValue:a})},i=e=>{var t,a,o;const{editor:n,state:l}=e,{selection:i}=l;if(i.empty)return!1;if(!n.isEditable)return!1;const r=i.node;if("image"===(null==(t=null==r?void 0:r.type)?void 0:t.name)||"bilibili"===(null==(a=null==r?void 0:r.type)?void 0:a.name)||"codeBlock"===(null==(o=null==r?void 0:r.type)?void 0:o.name))return!1;const{from:s,to:c}=i;let d=!1;return l.doc.nodesBetween(s,c,(e=>{if("codeBlock"===e.type.name)return d=!0,!1})),!d};return(e,t)=>(m(),u(h(St),{"tippy-options":n,editor:e.editor,"should-show":i},{default:p((()=>{var t;return[e.selectBubbleMenu.image?(m(),f("div",Xn)):e.selectBubbleMenu.bilibili?(m(),f("div",Qn)):(m(),f("div",Zn,[v(Kn,{buttons:a.textFormat,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":l},null,8,["buttons","editor","extensions-set"]),v(Kn,{buttons:[a.other[0]],editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":l},null,8,["buttons","editor","extensions-set"]),v(Jn,{icon:h(jo),show:e.extensionsSet.has("link"),trigger:()=>l("设置链接",(()=>{var t;null==(t=e.editor)||t.chain().focus().extendMarkRange("link").run()}),!0),"is-active":null==(t=e.editor)?void 0:t.isActive("link"),tooltip:"链接"},null,8,["icon","show","trigger","is-active"]),v(Wn,{show:e.extensionsSet.has("color"),type:"bubble-menu",editor:e.editor,colorType:"color"},null,8,["show","editor"]),v(Wn,{show:e.extensionsSet.has("backgroundColor"),type:"bubble-menu",editor:e.editor,colorType:"backgroundColor"},null,8,["show","editor"]),v(Kn,{buttons:a.align,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":l},null,8,["buttons","editor","extensions-set"])]))]})),_:1},8,["editor"]))}}),tl={class:"editor-floating-menu"},al=l({__name:"EditorFloatingMenu",props:{editor:{type:Object,required:!0},extensionsSet:{type:Object,required:!0}},emits:["image-upload","show-modal"],setup(e,{emit:t}){const a=e,o=t,n={duration:100,appendTo:document.body,placement:"right"},l=()=>{var e;null==(e=a.editor)||e.chain().focus().setLink({href:""}).run()},i=e=>{const{editor:t,state:a}=e,{selection:o}=a,{$anchor:n}=o,l="paragraph"===n.parent.type.name,i=0===n.parent.content.size,r="codeBlock"===n.parent.type.name;let s=!1;const c=n.pos,d=a.doc;if(c>0){"codeBlock"===d.resolve(c-1).parent.type.name&&(s=!0)}if(c<d.content.size){"codeBlock"===d.resolve(c+1).parent.type.name&&(s=!0)}return l&&i&&t.isEditable&&!r&&!s};return(t,a)=>(m(),u(h(At),{"tippy-options":n,editor:e.editor,"should-show":i},{default:p((()=>{var a,n,i,r,s,c;return[g("div",tl,[v(Jn,{icon:h(Lo),show:e.extensionsSet.has("heading"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().toggleHeading({level:3}).run()},"is-active":null==(a=e.editor)?void 0:a.isActive("heading",{level:3}),tooltip:"标题3"},null,8,["icon","show","trigger","is-active"]),v(Jn,{icon:h(jo),show:e.extensionsSet.has("link"),trigger:()=>((e,t,a=!1)=>{o("show-modal",{title:e,trigger:t,onlyInputValue:a})})("插入链接",l),"is-active":null==(n=e.editor)?void 0:n.isActive("link"),tooltip:"链接"},null,8,["icon","show","trigger","is-active"]),v(Jn,{icon:h(Eo),show:e.extensionsSet.has("image"),trigger:()=>t.$emit("image-upload"),tooltip:"图片"},null,8,["icon","show","trigger"]),v(Wn,{show:e.extensionsSet.has("color"),type:"floating-menu",editor:e.editor,colorType:"color"},null,8,["show","editor"]),v(Wn,{show:e.extensionsSet.has("backgroundColor"),type:"floating-menu",editor:e.editor,colorType:"backgroundColor"},null,8,["show","editor"]),v(Jn,{icon:h(Fo),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("left").run()},"is-active":null==(i=e.editor)?void 0:i.isActive({textAlign:"left"}),tooltip:"左对齐"},null,8,["icon","show","trigger","is-active"]),v(Jn,{icon:h(Ho),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("center").run()},"is-active":null==(r=e.editor)?void 0:r.isActive({textAlign:"center"}),tooltip:"居中对齐"},null,8,["icon","show","trigger","is-active"]),v(Jn,{icon:h(Vo),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("right").run()},"is-active":null==(s=e.editor)?void 0:s.isActive({textAlign:"right"}),tooltip:"右对齐"},null,8,["icon","show","trigger","is-active"]),v(Jn,{icon:h(Oo),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("justify").run()},"is-active":null==(c=e.editor)?void 0:c.isActive({textAlign:"justify"}),tooltip:"两端对齐"},null,8,["icon","show","trigger","is-active"])])]})),_:1},8,["editor"]))}}),ol=()=>{const{modal:e,showModal:t}=(()=>{const e=n({visible:!1,title:"",inputTitle:"",inputValue:"",onlyInputValue:!1,trigger:()=>{}}),t=()=>{e.value.visible=!1};return{modal:e,showModal:t=>{const{title:a,trigger:o,onlyInputValue:n=!1}=t;e.value.inputTitle="",e.value.inputValue="",e.value.visible=!0,e.value.title=a,e.value.trigger=o,e.value.onlyInputValue=n},closeModal:t,handleConfirm:a=>{a?a(e.value):e.value.trigger(),t()},handleCancel:()=>{t()}}})();return{modal:e,handleShowModal:t}},nl={class:"flex-column-gap12"},ll=Sa(l({__name:"EditorModalHandler",props:{modal:{},editor:{}},emits:["update:modal"],setup(e,{emit:t}){const a=e,o=t,n=()=>{if(a.editor)if("插入链接"===a.modal.title){const e=!a.editor.state.selection.empty;a.modal.inputValue&&(e?a.editor.chain().focus().setLink({href:a.modal.inputValue}).run():a.modal.inputTitle?a.editor.chain().focus().insertContent(`<a href="${a.modal.inputValue}">${a.modal.inputTitle}</a>`).run():a.editor.chain().focus().insertContent(`<a href="${a.modal.inputValue}">${a.modal.inputValue}</a>`).run())}else"设置链接"===a.modal.title?a.modal.inputValue&&a.editor.chain().focus().extendMarkRange("link").setLink({href:a.modal.inputValue}).run():"插入bilibili视频链接"===a.modal.title?a.modal.inputValue&&a.editor.commands.setBilibiliVideo({src:a.modal.inputValue}):a.modal.trigger();else a.modal.trigger();const e={...a.modal,visible:!1};o("update:modal",e)},l=()=>{const e={...a.modal,visible:!1};o("update:modal",e)};return(e,t)=>(m(),u(h(ie),{show:e.modal.visible,"onUpdate:show":t[2]||(t[2]=t=>e.modal.visible=t),title:e.modal.title,preset:"dialog","positive-text":"确认","negative-text":"取消",onPositiveClick:n,onNegativeClick:l},{default:p((()=>[g("div",nl,[e.modal.onlyInputValue?M("",!0):(m(),u(h(le),{key:0,value:e.modal.inputTitle,"onUpdate:value":t[0]||(t[0]=t=>e.modal.inputTitle=t),placeholder:"请输入标题"},null,8,["value"])),v(h(le),{value:e.modal.inputValue,"onUpdate:value":t[1]||(t[1]=t=>e.modal.inputValue=t),placeholder:"请输入链接"},null,8,["value"])])])),_:1},8,["show","title"]))}}),[["__scopeId","data-v-ccec921c"]]),il=Sa(l({__name:"LongPress",props:{duration:{type:Number,default:500}},emits:["long-press","click"],setup(e,{emit:t}){const a=e,o=t;let l=null,i=null,r=null;const s=n(!1),c=()=>{i=Date.now(),l=setTimeout((()=>{s.value=!0,o("long-press")}),a.duration)},d=()=>{if(null!==l){clearTimeout(l);Date.now()-(i??0)<a.duration&&(null!==r&&clearTimeout(r),r=setTimeout((()=>{o("click")}),200))}s.value=!1},u=()=>{null!==l&&clearTimeout(l),s.value=!1};return(e,t)=>(m(),f("div",{class:"long-press-wrapper",onMousedown:c,onMouseup:d,onMouseleave:u,onTouchstartPassive:c,onTouchendPassive:d,onTouchcancelPassive:u},[g("div",{class:b(["slot-content",{"long-press-active":s.value}])},[T(e.$slots,"default",{},void 0,!0)],2)],32))}}),[["__scopeId","data-v-5f07a55b"]]),rl=l({__name:"FormatPainterBtn",props:{editor:{}},setup(e){const t=e,a=i((()=>{var e;const{selection:a}=(null==(e=t.editor)?void 0:e.state)??{};return!!a&&a.from!==a.to})),o=()=>{var e;const{selection:a}=null==(e=t.editor)?void 0:e.state,{from:o,to:n}=a;if(o!==n){const e=t.editor.state.tr;t.editor.state.doc.nodesBetween(o,n,((t,a)=>{t.isText&&e.removeMark(a,a+t.nodeSize,null)})),t.editor.view.dispatch(e),t.editor.commands.focus()}},n=()=>{var e,a;const{formatPainter:o}=null==(e=t.editor)?void 0:e.storage,{selection:n}=null==(a=t.editor)?void 0:a.state,{from:l,to:i}=n;if(o.isActive){if(o.sourceMarks&&l!==i){const e=t.editor.state.tr;t.editor.state.doc.nodesBetween(l,i,((t,a)=>{t.isText&&e.removeMark(a,a+t.nodeSize,null)})),o.sourceMarks.forEach((t=>{e.addMark(l,i,t)})),t.editor.view.dispatch(e),t.editor.commands.focus(),o.isActive=!1,o.sourceNode=null,o.sourceMarks=null}}else{const e=t.editor.state.doc.nodeAt(l);e&&(o.isActive=!0,o.sourceNode=e,o.sourceMarks=[...e.marks],t.editor.commands.focus())}};return r((()=>{t.editor&&t.editor.on("selectionUpdate",(()=>{var e,a;const{formatPainter:o}=(null==(e=t.editor)?void 0:e.storage)||{},{selection:n}=(null==(a=t.editor)?void 0:a.state)||{},{from:l,to:i}=n||{};if(l===i&&(null==o?void 0:o.isActive)){const e=t.editor.state.tr;e.setMeta("addToHistory",!1),t.editor.view.dispatch(e)}}))})),_((()=>{t.editor&&t.editor.off("selectionUpdate")})),(e,t)=>(m(),u(il,{duration:500,onLongPress:o,onClick:n},{default:p((()=>{var o,n,l,i,r,s;return[v(Jn,{"is-active":(null==(l=null==(n=null==(o=e.editor)?void 0:o.storage)?void 0:n.formatPainter)?void 0:l.isActive)??!1,icon:h(an),tooltip:(null==(s=null==(r=null==(i=e.editor)?void 0:i.storage)?void 0:r.formatPainter)?void 0:s.isActive)?"点击应用格式":"格式刷",show:!0,trigger:()=>{},disabled:!a.value,onMousedown:t[0]||(t[0]=B((()=>{}),["prevent"])),onClick:t[1]||(t[1]=B((()=>{}),["prevent"]))},null,8,["is-active","icon","tooltip","disabled"])]})),_:1}))}}),sl=l({__name:"EditorToolbar",props:{editor:{},extensionsSet:{},toolbarClass:{default:"editor-toolbar"},externalFullscreenState:{type:Boolean,default:void 0},modal:{default:()=>({inputValue:"",inputTitle:""})}},emits:["image-upload","show-modal","toggle-fullscreen"],setup(e,{emit:t}){const a=e,o=t,l=n(a.modal),i=n(!1);s((()=>a.externalFullscreenState),(e=>{void 0!==e&&(i.value=e)}));const r=(e,t,a=!1)=>{o("show-modal",{title:e,trigger:t,onlyInputValue:a})},c=()=>{i.value=!i.value,o("toggle-fullscreen",i.value)};return(e,t)=>(m(),f("div",{class:b(e.toolbarClass)},[v(Kn,{buttons:h(Gn).textFormat,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:l.value,onImageUpload:t[0]||(t[0]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Kn,{buttons:h(Gn).heading,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:l.value,onImageUpload:t[1]||(t[1]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Kn,{buttons:h(Gn).list,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:l.value,onImageUpload:t[2]||(t[2]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Kn,{buttons:[h(Gn).other[0],h(Gn).other[1]],editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:l.value,onImageUpload:t[3]||(t[3]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Wn,{show:e.extensionsSet.has("color"),type:"toolbar",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),v(Wn,{show:e.extensionsSet.has("backgroundColor"),type:"toolbar",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),v(Kn,{buttons:h(Gn).other.slice(2),editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:l.value,onImageUpload:t[4]||(t[4]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(rl,{show:e.extensionsSet.has("formatPainter"),editor:e.editor},null,8,["show","editor"]),v(Kn,{buttons:h(Gn).align,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:l.value,onImageUpload:t[5]||(t[5]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Jn,{icon:i.value?h(Bo):h(_o),show:e.extensionsSet.has("fullscreen"),trigger:c,"is-active":i.value,tooltip:i.value?"退出全屏":"全屏"},null,8,["icon","show","is-active","tooltip"])],2))}}),cl=["image","dropcursor","placeholder","link","textStyle","color","backgroundColor","mention","bilibili","align","history"],dl=1e4,ul={key:0,class:"character-count"},ml=["accept"],pl=Sa(l({__name:"TipTapEditor",props:{modelValue:{type:[Object,String],default:""},extensions:{type:Array,default:()=>[]},allExtensions:{type:Boolean,default:!1},toolbar:{type:Boolean,default:!1},placeholder:{type:String,default:"..."},editable:{type:Boolean,default:!0},fileBucket:{type:String,required:!0},bubbleMenu:{type:Boolean,default:!1},floatingMenu:{type:Boolean,default:!1},editorProps:{type:Object,default:()=>({attributes:{class:"ProseMirror"}})},toolbarClass:{type:Object,default:["editor-toolbar"]},showCharacterCount:{type:Boolean,default:!1},characterLimit:{type:Number,default:1e3},useThumbnail:{type:Boolean,default:!0}},emits:["update:modelValue","save"],setup(e,{expose:t,emit:a}){const o=e,l=a,{imageInputRef:i,handleImageChange:s,imageHandleCallback:c}=lo(),{modal:d,handleShowModal:p}=ol(),{editor:y,initEditor:x,watchEditable:k,clearContent:C,setContent:L,getMarkdown:S,cleanupEditor:A}=Nn({...o,modelValue:"string"==typeof o.modelValue?{}:o.modelValue},l,(()=>{var e;return null==(e=i.value)?void 0:e.click()}),((e,t,a)=>{p({title:e,trigger:t,onlyInputValue:a})})),{isFullscreen:R,toolbarFullscreenState:E,handleToggleFullscreen:z,handleCloseFullscreen:T,cleanupFullscreen:I}=(e=>{const t=n(!1),a=n(void 0);let o=null,l=null,i=null;const r=n=>{if(t.value=n,a.value=n,t.value){if(document.body.style.overflow="hidden",window.innerWidth<=768){if(i&&window.removeEventListener("resize",i),l){const e=window.visualViewport;e&&e.removeEventListener("resize",l)}o=()=>{const e=document.querySelector(".tiptap-fullscreen"),a=null==e?void 0:e.querySelector(".editor-content");if(e&&a&&t.value){const e=window.innerHeight;a.style.maxHeight=e-128+"px"}},i=e=>{o&&o()};const e=window.visualViewport;if(e&&(l=a=>{const o=document.querySelector(".tiptap-fullscreen");if(o&&t.value){const t=e.offsetTop;o.style.transform=t?`translateY(-${t}px)`:"";const a=o.querySelector(".editor-content");if(a){const t=e.height-128;a.style.maxHeight=`${t}px`}}},e.addEventListener("resize",l)),o)try{o()}catch(r){Wt.error("Error initializing resize handler",r)}i&&window.addEventListener("resize",i)}const e=document.documentElement.classList.contains("dark-theme");setTimeout((()=>{const t=document.querySelector(".tiptap-fullscreen");if(t){e&&t.classList.add("dark-theme");const a=document.querySelector(".tiptap-editor-wrapper:not(.tiptap-fullscreen)")||document.querySelector(".article-modal-content");if(a){const o=window.getComputedStyle(a),n="rgba(0, 0, 0, 0)"!==o.backgroundColor&&"transparent"!==o.backgroundColor?o.backgroundColor:e?"var(--white-2)":"white";t.style.backgroundColor=n;const l=t.querySelector(".editor-content");l&&(l.style.backgroundColor=n);const i=t.querySelector(".editor-toolbar");i&&(i.style.backgroundColor=n);const r=t.querySelector(".ProseMirror");r&&(r.style.backgroundColor=n)}else{const a=e?"var(--white-2)":"white";t.style.backgroundColor=a;const o=t.querySelector(".editor-content");o&&(o.style.backgroundColor=a);const n=t.querySelector(".editor-toolbar");n&&(n.style.backgroundColor=a);const l=t.querySelector(".ProseMirror");l&&(l.style.backgroundColor=a)}}}),0)}else{document.body.style.overflow="",i&&(window.removeEventListener("resize",i),i=null);const e=window.visualViewport;l&&e&&(e.removeEventListener("resize",l),l=null),o=null;const t=document.querySelector(".tiptap-fullscreen");t&&(t.style.transform="")}requestAnimationFrame((()=>{var t;null==(t=e.value)||t.commands.focus()}))};return{isFullscreen:t,toolbarFullscreenState:a,handleToggleFullscreen:r,handleCloseFullscreen:()=>{r(!1),a.value=!1},cleanupFullscreen:()=>{document.body.style.overflow="",i&&(window.removeEventListener("resize",i),i=null);const e=window.visualViewport;l&&e&&(e.removeEventListener("resize",l),l=null),o=null}}})(y);let B,P={image:!1,bilibili:!1};r((()=>{B=x();const e=qn(y,c,o.fileBucket,o.useThumbnail);e.selectBubbleMenu&&(P=e.selectBubbleMenu),e.setupEditorEvents(),k()}));return t({setContent:L,clearContent:C,getMarkdown:S,editor:y,handleSave:()=>{l("save")}}),_((()=>{A(),I()})),(t,a)=>h(y)?(m(),f("div",{key:0,class:b(["tiptap-editor-wrapper",{"tiptap-fullscreen":h(R)}]),style:{width:"100%"}},[h(R)?(m(),f("div",{key:0,class:"fullscreen-close-button",onClick:a[0]||(a[0]=(...e)=>h(T)&&h(T)(...e))},[v(h(Xo),{size:24})])):M("",!0),o.toolbar?(m(),u(sl,{key:1,editor:h(y),"extensions-set":h(B),"toolbar-class":e.toolbarClass,"external-fullscreen-state":h(E),modal:h(d),onImageUpload:a[1]||(a[1]=e=>{var t;return null==(t=h(i))?void 0:t.click()}),onShowModal:h(p),onToggleFullscreen:h(z)},null,8,["editor","extensions-set","toolbar-class","external-fullscreen-state","modal","onShowModal","onToggleFullscreen"])):M("",!0),h(y)&&o.bubbleMenu?(m(),u(el,{key:2,editor:h(y),"extensions-set":h(B),"select-bubble-menu":h(P),onShowModal:h(p)},null,8,["editor","extensions-set","select-bubble-menu","onShowModal"])):M("",!0),h(y)&&o.floatingMenu?(m(),u(al,{key:3,editor:h(y),"extensions-set":h(B),onShowModal:h(p),onImageUpload:a[2]||(a[2]=e=>{var t;return null==(t=h(i))?void 0:t.click()})},null,8,["editor","extensions-set","onShowModal"])):M("",!0),g("div",{class:b(["editor-content",{"editor-readonly":!o.editable}]),style:{width:"100%"}},[v(h(Rt),{editor:h(y)},null,8,["editor"]),o.showCharacterCount&&h(y)?(m(),f("div",ul,w(h(y).storage.characterCount.characters)+" / "+w(o.characterLimit),1)):M("",!0)],2),v(ll,{modal:h(d),"onUpdate:modal":a[3]||(a[3]=e=>$(d)?d.value=e:null),editor:h(y)},null,8,["modal","editor"]),g("input",{type:"file",accept:h(ja).imageTypes.join(","),ref_key:"imageInputRef",ref:i,onChange:a[4]||(a[4]=e=>h(y)&&h(s)(e,h(y),o.fileBucket,o.useThumbnail)),class:"display-none"},null,40,ml)],2)):M("",!0)}}),[["__scopeId","data-v-4e360fe8"]]);var vl=(e=>(e[e.PUBLIC=0]="PUBLIC",e[e.PERSONAL=1]="PERSONAL",e))(vl||{});const hl={0:"公开",1:"个人"};function gl(){const e=n(null);return{articleFileInputRef:e,handleArticleFileClick:()=>{var t;null==(t=e.value)||t.click()},handleArticleFileChange:(t,a,o)=>{var n;const l=null==(n=t.target.files)?void 0:n[0];l&&(((e,t,a)=>{const o=new FileReader;o.readAsText(e,"UTF-8"),o.onload=e=>{var o,n;const l=(null==(o=e.target)?void 0:o.result).split("\n");Wt.debug("import lines: ",l);const i=(l[0]?l[0].substring(1).trim():"")||"",r=l[1]?l[1].replace(">","").split(",").filter(Boolean).map((e=>e.trim())):[],s=null==(n=l[9])?void 0:n.split("|"),c=parseInt(s[0].replace(">","").trim())||0,d=parseInt(s[1].trim())||vl.PERSONAL,u=l.slice(12).join("\n"),m=a.value.editor;if(m)try{if(m.storage.markdown){m.commands.clearContent(!1);const e=m.storage.markdown.parser.parse(u);m.commands.setContent(e||u,!0)}else m.commands.setContent(u,!0),va.warning("Markdown 格式可能无法完全解析");t.value={...t.value,title:i,tags:r,operationLevel:c,publishedScope:d,contentObj:m.getJSON()}}catch(p){Wt.error("Error parsing or setting markdown content:",p),va.error("解析 Markdown 内容时出错"),m.commands.setContent(u,!0),t.value={...t.value,title:i,tags:r,operationLevel:c,publishedScope:d,contentObj:m.getJSON()}}else va.error("编辑器尚未准备好")},o.onerror=()=>{va.warning("文件貌似有问题~")}})(l,a,o),e.value&&(e.value.value=""))}}}function fl(e,t,a="内容不能为空哦~"){var o;if(!e){const e="编辑器初始化失败，请刷新后重试";return va.warning(e),{isValid:!1,message:e}}const n="editor"in e?e.editor:e,l=n.isEmpty,i=!t.value;if(l)return va.warning(a),{isValid:!1,message:a};if(i&&!l){const e=null==(o=n.getJSON)?void 0:o.call(n);return e?(t.value=e,{isValid:!0,content:e}):(va.warning(a),{isValid:!1,message:a})}return{isValid:!0,content:t.value}}function wl(e,t,a="正在处理中，请稍候..."){let o;if(e instanceof Map&&t)o=e.get(t)||!1;else{if(!("value"in e))return!0;o=e.value}return!o||(va.warning(a),!1)}function bl(e,t,a){e instanceof Map&&a?e.set(a,t):"value"in e&&(e.value=t)}const{dialog:yl}=Q(["dialog"]);function xl(){const e=n(!1),t=n(!1),a=n(!1),o=n(!1);return{isArticleDialogVisible:e,isEditingArticle:t,submitLoading:a,quickSaveLoading:o,openCreateArticleDialog:a=>{t.value=!1,a(),e.value=!0},openEditArticleDialog:(a,o)=>{t.value=!0,o(a),e.value=!0},handleClose:t=>(yl.warning({title:"提示",content:"你确定关闭？",positiveText:"确定",negativeText:"不确定",onPositiveClick:()=>{t(),e.value=!1},onNegativeClick:()=>{}}),!1),setupKeyboardListener:t=>{const a=(t=>a=>{e.value&&a.ctrlKey&&"s"===a.key&&(a.preventDefault(),a.stopPropagation(),t())})(t);r((()=>{window.addEventListener("keydown",a)})),_((()=>{window.removeEventListener("keydown",a)}))}}}const kl="article",Cl="comment",Ll={class:"flex-between-center",style:{width:"min(16rem, 100%)"}},Ml=l({__name:"ArticleModal",emits:["success"],setup(e,{expose:t,emit:a}){const o=a,l=xl(),r=function(){const e=n(l()),t=n(),a=n(),o=n();function l(){return{id:"",title:"",tags:[],operationLevel:0,publishedScope:vl.PERSONAL,shareUsers:[],contentObj:{}}}const r=i((()=>{var e;const t=[];for(let a=0;a<=(null==(e=Gt.getLoginUser())?void 0:e.level);a++)t.push({label:"Lv"+a,value:a});return t})),s=(o,n,l,i,r)=>{var s,d;if(!(null==(s=t.value)?void 0:s.validate()))return;if(!wl(l))return;const u=null==(d=a.value)?void 0:d.editor;if(!fl(u,{value:e.value.contentObj},"文章内容不能为空哦~").isValid)return;bl(l,!0);const m=u.getJSON(),p={title:e.value.title,tag:e.value.tags.join(","),operationLevel:e.value.operationLevel,publishedScope:e.value.publishedScope,content:Un.toJsonString(m),shareUserIds:e.value.shareUsers.map((e=>e.id))};n.value&&e.value.id&&Object.assign(p,{id:e.value.id}),(n.value&&e.value.id?Fn.edit:Fn.save)(p).then((t=>{(null==t?void 0:t.success)&&(!n.value&&t.data&&(n.value=!0,e.value.id=t.data,c((()=>{Wt.debug("Article state updated:",{isEditing:n.value,articleId:e.value.id})}))),o?(i.value=!1,va.success(n.value?"修改成功":"创建成功")):va.success("保存成功"),r("success"))})).catch((t=>{n.value||(n.value=!1,e.value.id=""),va.error(t.message||"保存失败")})).finally((()=>{l.value=!1}))};return{articleForm:e,articleFormRef:t,articleTiptapEditorRef:a,shareUserSelectRef:o,generateCommentLevel:r,resetArticleForm:()=>{var t,n;e.value=l(),null==(t=o.value)||t.reset();const i=null==(n=a.value)?void 0:n.editor;i&&i.commands.clearContent(!0)},setFormData:t=>{e.value={id:t.id,title:t.title,tags:t.tags,operationLevel:t.operationLevel,publishedScope:t.publishedScope,contentObj:t.contentObj,shareUsers:t.shareUsers||[]},Wt.debug("edit article form: ",e.value)},submitArticleForm:(e,t,a,o)=>(t.value||ma("article-submit",(()=>{s(!0,e,t,a,o)}),300),!1),quickSaveArticleForm:(e,t,a,o)=>{t.value||ma("article-quick-save",(()=>{s(!1,e,t,a,o)}),300)}}}(),s=gl(),{articleFormRef:d,articleTiptapEditorRef:b,shareUserSelectRef:y}=r;l.setupKeyboardListener((()=>k()));const x=()=>{r.submitArticleForm(l.isEditingArticle,l.submitLoading,l.isArticleDialogVisible,o)},k=()=>{r.quickSaveArticleForm(l.isEditingArticle,l.quickSaveLoading,l.isArticleDialogVisible,o)};return t({openCreateArticleDialog:()=>l.openCreateArticleDialog(r.resetArticleForm),openEditArticleDialog:e=>l.openEditArticleDialog(e,r.setFormData)}),(e,t)=>(m(),u(h(ie),{show:h(l).isArticleDialogVisible.value,"onUpdate:show":t[7]||(t[7]=e=>h(l).isArticleDialogVisible.value=e),preset:"dialog","negative-text":"算了","positive-text":"确认",onNegativeClick:t[8]||(t[8]=()=>h(l).handleClose(h(r).resetArticleForm)),onPositiveClick:x,showIcon:!1,onClose:t[9]||(t[9]=()=>h(l).handleClose(h(r).resetArticleForm)),onMaskClick:t[10]||(t[10]=()=>h(l).handleClose(h(r).resetArticleForm)),"mask-closable":!1,"auto-focus":!1,"close-on-esc":!1,class:"article-modal","positive-button-props":{loading:h(l).submitLoading.value}},{header:p((()=>[v(h(pe),{type:"primary",size:20},{default:p((()=>[z(w(h(l).isEditingArticle.value?"是得再改改":"想点什么呢"),1)])),_:1}),v(h(un),{size:24,class:"cursor-pointer",onClick:h(s).handleArticleFileClick},null,8,["onClick"]),g("input",{type:"file",ref:"articleFile.articleFileInputRef",accept:".md",onChange:t[0]||(t[0]=e=>h(s).handleArticleFileChange(e,h(r).articleForm,h(r).articleTiptapEditorRef)),class:"display-none"},null,544)])),default:p((()=>[v(h(re),{model:h(r).articleForm.value,ref_key:"articleFormRef",ref:d,"label-placement":"left"},{default:p((()=>[v(h(se),{label:"标题",path:"title",style:{width:"min(30rem, 100%)"}},{default:p((()=>[v(h(le),{value:h(r).articleForm.value.title,"onUpdate:value":t[1]||(t[1]=e=>h(r).articleForm.value.title=e),placeholder:"请输入文章标题"},null,8,["value"])])),_:1}),v(h(se),{label:"标签",path:"tag",style:{width:"min(30rem, 100%)"}},{default:p((()=>[v(h(ce),{value:h(r).articleForm.value.tags,"onUpdate:value":t[2]||(t[2]=e=>h(r).articleForm.value.tags=e),"input-props":{maxlength:20},max:3,type:"primary",placeholder:"请输入标签"},null,8,["value"])])),_:1}),g("div",Ll,[v(h(se),{label:"等级 | 范围",path:"allowCommentLevel",style:{width:"6rem"}},{default:p((()=>[v(h(de),{value:h(r).articleForm.value.operationLevel,"onUpdate:value":t[3]||(t[3]=e=>h(r).articleForm.value.operationLevel=e),options:h(r).generateCommentLevel.value,size:"small",trigger:"click"},{default:p((()=>[v(h(oe),{size:"small"},{default:p((()=>[z(" Lv"+w(h(r).articleForm.value.operationLevel||"0"),1)])),_:1})])),_:1},8,["value","options"])])),_:1}),v(h(se),{path:"scope"},{default:p((()=>[v(h(ue),{value:h(r).articleForm.value.publishedScope,"onUpdate:value":t[4]||(t[4]=e=>h(r).articleForm.value.publishedScope=e),size:"small","default-value":h(vl).PERSONAL},{default:p((()=>[(m(!0),f(S,null,A([{value:h(vl).PUBLIC,label:h(hl)[h(vl).PUBLIC]},{value:h(vl).PERSONAL,label:h(hl)[h(vl).PERSONAL]}],(e=>(m(),u(h(me),{class:"flex-between-center",key:e.value,value:e.value,label:e.label},null,8,["value","label"])))),128))])),_:1},8,["value","default-value"])])),_:1})]),h(r).articleForm.value.publishedScope===h(vl).PERSONAL?(m(),u(h(se),{key:0,label:"分享给",path:"shareUsers",style:{width:"min(30rem, 100%)"}},{default:p((()=>[v(On,{modelValue:h(r).articleForm.value.shareUsers,"onUpdate:modelValue":t[5]||(t[5]=e=>h(r).articleForm.value.shareUsers=e),placeholder:"请搜索并选择用户",ref_key:"shareUserSelectRef",ref:y},null,8,["modelValue"])])),_:1})):M("",!0),v(h(se),{path:"content"},{default:p((()=>[v(h(Z),{class:"article-modal-content"},{default:p((()=>[v(pl,{ref_key:"articleTiptapEditorRef",ref:b,modelValue:h(r).articleForm.value.contentObj,"onUpdate:modelValue":t[6]||(t[6]=e=>h(r).articleForm.value.contentObj=e),"editor-props":{attributes:{class:"ProseMirrorNoneOutline"}},"bubble-menu":!0,"floating-menu":!0,"file-bucket":h(kl),"all-extensions":!0,toolbar:!0,"toolbar-class":["editor-toolbar","editor-toolbar-bgc"],placeholder:"尽情发挥！","show-character-count":!0,"character-limit":h(dl),"save-loading":h(l).quickSaveLoading.value,onSave:k},null,8,["modelValue","file-bucket","character-limit","save-loading"])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["show","positive-button-props"]))}}),Sl={class:"article-info-container"},Al={class:"article-header"},Rl={class:"article-header-content-wrapper",style:{"max-width":"80%"}},El={class:"article-header-content"},zl={class:"article-tag-container"},Tl={class:"flex-column-center",style:{width:"80%",gap:"0.25rem"}},Il={class:"action-buttons-container"},_l={class:"interaction-container"},Bl={class:"comment-count-container",style:{"margin-right":"0"}},$l={class:"article-content flex-column-gap24"},Pl=l({__name:"ArticleSkeleton",props:{show:{type:Boolean,default:!1}},setup:e=>(t,a)=>P((m(),f("div",Sl,[g("div",Al,[g("div",Rl,[g("div",El,[v(h(ve),{width:600,text:"",size:"large",style:{"max-width":"100%",margin:"1.25rem 0"}}),g("div",zl,[v(h(ve),{style:{width:"60%","margin-bottom":"0.5rem"},round:"",text:"",size:"small"})])])]),g("div",Tl,[v(h(ve),{style:{width:"30%","margin-bottom":"0.25rem"},text:"",height:10,size:"small",repeat:3})]),g("div",Il,[g("div",_l,[v(h(ve),{width:60,round:"",style:{"max-width":"100%"},text:"",size:"small"})]),g("div",Bl,[v(h(ve),{height:20,width:100,round:"",style:{"max-width":"100%"},text:"",size:"small"})])])]),g("div",$l,[v(h(ve),{style:{width:"100%"},round:"",text:"",size:"large",repeat:8})])],512)),[[U,e.show]])}),Ul={URL:"/core/notification",load:async e=>(await fa(Ul.URL,e).catch((e=>ga(e)))).data,read:async e=>(await ya(Ul.URL+"/read/"+e).catch((e=>ga(e)))).data,readAll:async e=>(await ya(Ul.URL+"/read-all").catch((e=>ga(e)))).data,unreadCount:async()=>(await fa(Ul.URL+"/total-unread").catch((e=>ga(e)))).data},Fl=class{constructor(){t(this,"stompClient",null),t(this,"socketUrl",Nt.backend.wsURL),t(this,"subscriptions",{})}connect(){const e=new Ft(this.socketUrl,null,{transports:["websocket","xhr-streaming"]});this.stompClient=new Ht({webSocketFactory:()=>e,debug:e=>{Wt.debug(e)}}),this.stompClient.onConnect=e=>{Wt.info("ws connected: "+e)},this.stompClient.activate()}subscribe(e,t){if(null!==this.stompClient)if(this.stompClient.connected){const a=this.stompClient.subscribe(e,(e=>{e.body&&t(e.body)}));this.subscriptions[e]=a,Wt.info(`Subscribed to: ${e}`)}else Wt.info(`WebSocket is not connected, retrying to subscribe to: ${e}`),setTimeout((()=>{this.subscribe(e,t)}),1e3);else Wt.warn("WebSocket client is not connected")}unsubscribe(e){const t=this.subscriptions[e];t?(t.unsubscribe(),delete this.subscriptions[e],Wt.info(`Unsubscribed from: ${e}`)):Wt.warn(`No subscription found for: ${e}`)}disconnect(){null!==this.stompClient&&this.stompClient.deactivate().then((()=>{Wt.info("ws disconnected")}))}};t(Fl,"instance",new Fl);const Hl=Fl.instance,Vl="/topic/comments",Ol="/notifications";var Dl=(e=>(e[e.CLOSE=0]="CLOSE",e[e.ALL=1]="ALL",e[e.PUBLISH=2]="PUBLISH",e[e.MODIFY=3]="MODIFY",e[e.FAVORITE=4]="FAVORITE",e[e.SHARE=5]="SHARE",e))(Dl||{});const jl={0:"关闭",1:"全部",2:"发布",3:"修改",4:"收藏",5:"分享"},Nl=n(null);F((()=>{const e=ea.value===Qt.DARK,{notification:t}=Q(["notification"],{configProviderProps:{theme:e?Y:null}});Nl.value=t}));const ql=new Proxy({},{get(e,t){if(!Nl.value){const e=ea.value===Qt.DARK,{notification:t}=Q(["notification"],{configProviderProps:{theme:e?Y:null}});Nl.value=t}return Nl.value[t]}}),Yl=Sa(l({__name:"NotificationButton",props:{unreadCount:{type:Number,default:0},notificationReceiveType:{type:Number,required:!0}},emits:["click","long-press"],setup(e,{emit:t}){const a=n(null);return(t,o)=>(m(),f("div",{class:"notification-btn",style:{cursor:"pointer"},ref_key:"buttonRef",ref:a},[v(h(he),{max:99,value:e.unreadCount,"show-zero":!1,show:e.notificationReceiveType!==h(Dl).CLOSE},{default:p((()=>[e.notificationReceiveType!==h(Dl).CLOSE?(m(),u(h(Ko),{key:0,size:24})):(m(),u(h(Go),{key:1,size:24}))])),_:1},8,["value","show"])],512))}}),[["__scopeId","data-v-3d75049d"]]);class Jl{static toTimeString(e,t="YYYY-MM-DD HH:mm:ss"){const a=new Date(parseInt(e)),o={YYYY:a.getFullYear().toString(),MM:(a.getMonth()+1).toString().padStart(2,"0"),DD:a.getDate().toString().padStart(2,"0"),HH:a.getHours().toString().padStart(2,"0"),mm:a.getMinutes().toString().padStart(2,"0"),ss:a.getSeconds().toString().padStart(2,"0")};return t.replace(/YYYY|MM|DD|HH|mm|ss/g,(e=>o[e]))}static getCurrentTimestamp(){return Date.now()}static dateToTimestamp(e,t="YYYY-MM-DD HH:mm:ss"){const a={"YYYY-MM-DD HH:mm:ss":/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/g,"YYYY-MM-DD":/^(\d{4})-(\d{2})-(\d{2})$/g}[t];if(!a)return Wt.warn(`不支持的日期格式: ${t}`),null;const o=e.match(a);if(!o)return Wt.warn(`日期字符串 ${e} 不符合格式 ${t}`),null;const n=parseInt(o[1],10),l=parseInt(o[2],10)-1,i=parseInt(o[3],10);let r=0,s=0,c=0;"YYYY-MM-DD HH:mm:ss"===t&&(r=parseInt(o[4],10),s=parseInt(o[5],10),c=parseInt(o[6],10));return new Date(n,l,i,r,s,c).getTime()}static formatDate(e,t="YYYY-MM-DD HH:mm:ss"){const a={YYYY:e.getFullYear().toString(),MM:(e.getMonth()+1).toString().padStart(2,"0"),DD:e.getDate().toString().padStart(2,"0"),HH:e.getHours().toString().padStart(2,"0"),mm:e.getMinutes().toString().padStart(2,"0"),ss:e.getSeconds().toString().padStart(2,"0")};return t.replace(/YYYY|MM|DD|HH|mm|ss/g,(e=>a[e]))}static getRelativeTime(e){const t=Date.now()-parseInt(e);if(t<1e3)return"刚刚";if(t<6e4)return Math.floor(t/1e3)+"秒前";if(t<36e5)return Math.floor(t/6e4)+"分钟前";if(t<864e5)return Math.floor(t/36e5)+"小时前";if(t<2592e6)return Math.floor(t/864e5)+"天前";if(t<31104e6){const e=Math.floor(t/2592e6);return 6===e?"半年前":e+"个月前"}return Math.floor(t/31104e6)+"年前"}}const Wl={class:"notification-list-container"},Kl="评论了：",Gl=Sa(l({__name:"NotificationList",props:{visible:{type:Boolean,default:!1}},emits:["notification-click"],setup(e,{expose:t,emit:a}){const o=e,l=a;i((()=>ea.value===Qt.DARK));const c=n(!1),d=n(),u=[{label:"",value:5},{label:"",value:10},{label:"",value:15}];u.forEach((e=>{e.label=`${e.value}/页`}));const p=n({page:1,pageSize:5,showSizePicker:!0,showQuickJumper:!1,pageSlot:5,pageSizes:u,size:"medium",showQuickJumpDropdown:!1,prefix:e=>v("span",null,[`第 ${e.page} 页 `]),suffix:e=>v("span",null,[`共 ${e.itemCount} 条`]),onUpdatePage:e=>{p.value.page=e,y()},onUpdatePageSize:e=>{p.value.pageSize=e,p.value.page=1,y()}}),g=[{title:"通知时间",key:"ctTm",width:162},{title:"通知内容",key:"content",ellipsis:!0,render(e){const t=e.commentId,a=e.content,o=a.indexOf(Kl);return v("span",null,[v(he,{style:"position: absolute;",dot:!e.isRead,offset:[-4,0]},null),v(we,{trigger:"click",placement:"top-start",style:"max-width:min(555px,84vw); margin-left:min(-180px,40vw)",flip:!1},{trigger:()=>v("span",{class:"cursor-pointer notification-content",onClick:e=>e.stopPropagation()},[t?a.substring(0,o+4)+Un.serializeContent(x(a.substring(o+4))):e.content]),default:()=>v("div",{class:"notification-popover-content"},[t?v("div",{style:"margin: 10px"},[a.substring(0,o+4),v(pl,{fileBucket:Cl,modelValue:x(a.substring(o+4)),extensions:cl,editable:!1},null)]):v("div",null,[e.content]),v(oe,{style:"margin-left:auto",class:"flex-column-end",text:!0,type:"primary",onClick:()=>{b(e)}},{default:()=>[z("让我看看"),v(Do,{size:16},null)]})])})])}}],w=e=>({}),b=e=>{l("notification-click",e)},y=()=>{c.value=!0,Ul.load({pageNo:p.value.page,pageSize:p.value.pageSize}).then((e=>{const t=null==e?void 0:e.data;t&&(t.rows.forEach((e=>{e.ctTm=Jl.toTimeString(e.ctTm)})),p.value.itemCount=t.totalRows,p.value.pageCount=t.totalPage,d.value=t.rows)})).finally((()=>{c.value=!1}))};s((()=>o.visible),(e=>{e&&y()}),{immediate:!0}),r((()=>{o.visible&&y()})),t({loadNotificationPage:y,resetPagination:()=>{p.value.page=1,p.value.pageSize=5}});const x=e=>{if(!e)return{type:"doc",content:[{type:"paragraph",content:[]}]};try{return Un.toJsonObject(e)}catch(t){return Wt.error("通知内容JSON解析失败:",t),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"内容无法显示"}]}]}}};return(e,t)=>(m(),f("div",Wl,[v(h(ge),{class:"notification-table",remote:!0,loading:c.value,data:d.value,"row-props":w,columns:g,bordered:!1},null,8,["loading","data"]),v(h(fe),{class:"notification-pagination",page:p.value.page,"page-size":p.value.pageSize,"show-size-picker":p.value.showSizePicker,"show-quick-jumper":p.value.showQuickJumper,"page-slot":p.value.pageSlot,"page-sizes":p.value.pageSizes,size:p.value.size,"show-quick-jump-dropdown":p.value.showQuickJumpDropdown,prefix:p.value.prefix,suffix:p.value.suffix,itemCount:p.value.itemCount,"onUpdate:page":p.value.onUpdatePage,"onUpdate:pageSize":p.value.onUpdatePageSize},null,8,["page","page-size","show-size-picker","show-quick-jumper","page-slot","page-sizes","size","show-quick-jump-dropdown","prefix","suffix","itemCount","onUpdate:page","onUpdate:pageSize"])]))}}),[["__scopeId","data-v-d44bf846"]]),Xl=Sa(l({__name:"NotificationReceiveTypeSelector",props:{modelValue:{type:Number,required:!0}},emits:["update:modelValue"],setup(e,{emit:t}){const a=t,o=[{label:jl[Dl.ALL],value:Dl.ALL},{label:jl[Dl.PUBLISH],value:Dl.PUBLISH},{label:jl[Dl.MODIFY],value:Dl.MODIFY},{label:jl[Dl.FAVORITE],value:Dl.FAVORITE},{label:jl[Dl.SHARE],value:Dl.SHARE},{label:jl[Dl.CLOSE],value:Dl.CLOSE}],n=e=>{a("update:modelValue",e)};return(t,a)=>(m(),u(h(de),{class:"notification-popselect",value:e.modelValue,options:o,"onUpdate:value":n,trigger:"click"},{default:p((()=>[v(h(oe),{text:"",size:"small"},{default:p((()=>{var t;return[z(" 接收类型： "+w(null==(t=o.find((t=>t.value===e.modelValue)))?void 0:t.label),1)]})),_:1})])),_:1},8,["value"]))}}),[["__scopeId","data-v-72f4bad8"]]),Ql={class:"notification-container"},Zl={style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},ei="评论了：",ti=Sa(l({__name:"NotificationBtnModal",emits:["locationComment"],setup(e,{emit:t}){const a=Hn(),o=Vn(),l=n(!1),s=n(0),c=n(null),d=i((()=>ea.value===Qt.DARK)),f=Gt.getLoginUser()||{},w=n((null==f?void 0:f.notificationReceiveType)??Dl.FAVORITE),b=e=>{w.value=e,io.updateNotificationReceiveType(e).then((t=>{if(null==t?void 0:t.data){const t=Gt.getLoginUser();t.notificationReceiveType=e,Gt.setLoginUser(t),va.success("通知接收类型已更新"),e!==Dl.CLOSE?M():s.value=0}}))},y=()=>{Ul.readAll().then((()=>{va.success("读完了！"),M()}))},x=()=>{l.value=!0,M()};r((()=>{Hl.connect(),M(),k()})),_((()=>{C(),Hl.disconnect()}));const k=()=>{Hl.subscribe(`/user/${f.id}${Ol}`,L)},C=()=>{Hl.unsubscribe(`/user/${f.id}${Ol}`)},L=e=>{const t=Kt.parse(e);t&&(w.value!==Dl.CLOSE&&ma("notification-received:"+t.id,(()=>{const e=t.commentId,a=t.content;let o,n=0;if(e){n=a.indexOf(ei);const e=a.substring(n+4);o=Un.toJsonObject(e)}const l=ql.create({title:e?a.substring(0,n+4):"发来通知~",content:()=>e?v(pl,{fileBucket:Cl,modelValue:o,extensions:cl,editable:!1},null):v("div",{class:d.value?"dark-notification-content":""},[t.content]),duration:5e3,keepAliveOnHover:!0,closable:!0,avatar:()=>v(te,{size:"small","object-fit":"cover",round:!0,src:ja.getResourceURL(t.publisherAvatar),class:d.value?"dark-notification-avatar":""},null),action:()=>v(oe,{text:!0,type:"primary",class:d.value?"dark-notification-button":"",onClick:()=>{l.destroy(),S(t)}},{default:()=>[z("怎么个事？"),v(Do,{size:16},null)]})})})),M())},M=()=>{Ul.unreadCount().then((e=>{void 0!==(null==e?void 0:e.data)&&(s.value=w.value===Dl.CLOSE?0:Number(e.data))})),l.value&&c.value&&(c.value.resetPagination(),c.value.loadNotificationPage())},S=e=>{const t=e.articleId,n=e.commentId;if(t!==a.getId){const e=ss.resolve({name:"Article",params:{articleId:t,commentId:n}}),a=window.open(e.href,"_blank");a&&a.focus()}else n&&(o.setId(n),A("locationComment",n));l.value=!1,e.isRead||Ul.read(e.id)},A=t;return(e,t)=>(m(),u(h(X),null,{default:p((()=>[g("div",Ql,[v(il,{onLongPress:y,onClick:x},{default:p((()=>[v(Yl,{"unread-count":Number(s.value),"notification-receive-type":w.value},null,8,["unread-count","notification-receive-type"])])),_:1}),v(h(ie),{class:"notification-modal",style:{width:"600px","max-width":"100%"},show:l.value,"onUpdate:show":t[1]||(t[1]=e=>l.value=e),title:"通知列表",preset:"dialog"},{header:p((()=>[g("div",Zl,[t[2]||(t[2]=g("span",null,"通知列表",-1)),v(Xl,{modelValue:w.value,"onUpdate:modelValue":[t[0]||(t[0]=e=>w.value=e),b]},null,8,["modelValue"])])])),default:p((()=>[v(Gl,{ref_key:"notificationListRef",ref:c,visible:l.value,onNotificationClick:S},null,8,["visible"])])),_:1},8,["show"])])])),_:1}))}}),[["__scopeId","data-v-9f38a6c7"]]),ai={URL:"/authentication",login:async e=>(await wa(ai.URL+"/login",e).catch((e=>ga(e)))).data,register:async e=>(await wa(ai.URL+"/register",e).catch((e=>ga(e)))).data,logout:async()=>(await ka(ai.URL+"/logout").catch((e=>ga(e)))).data},oi={class:"sky"},ni=Sa(l({__name:"ThemeToggle",setup(e){const t=i((()=>ea.value===Qt.DARK)),a=()=>{(()=>{const e="light"===ea.value?"dark":"light";ta.value=!0,ea.value=e,Gt.set(Xt,e),na(e),la(),setTimeout((()=>{ta.value=!1}),50)})()},o=()=>v("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"100%",height:"100%"},[v("circle",{cx:"12",cy:"12",r:"5",fill:"currentColor"},null),v("path",{fill:"none",d:"M12,3V5M12,19V21M21,12H19M5,12H3M18.364,5.636L16.95,7.05M7.05,16.95L5.636,18.364M18.364,18.364L16.95,16.95M7.05,7.05L5.636,5.636",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"},null)]),n=()=>v("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"100%",height:"100%"},[v("path",{fill:"currentColor",d:"M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z"},null)]);return(e,l)=>(m(),f("div",{class:b(["theme-toggle-scene",{"is-dark":t.value}]),onClick:a},[g("div",oi,[g("div",{class:b(["sun",{"sun-set":t.value}])},[v(o)],2),g("div",{class:b(["moon",{"moon-rise":t.value}])},[v(n)],2)])],2))}}),[["__scopeId","data-v-fbb97caf"]]),li={class:"avatar-container"},ii={class:"user-info"},ri={class:"info-row"},si={class:"info-row"},ci={class:"info-row"},di={class:"actions-row"},ui=Sa(l({__name:"UserAvatar",setup(e){const t=n({}),a=n(null);r((()=>{io.info().then((e=>{e.data&&(t.value=e.data,t.value.avatar=l(t.value.avatar),Gt.setLoginUser(e.data))}))}));const o=()=>{var e;null==(e=a.value)||e.click()},l=e=>ja.getResourceURL(e),i=async e=>{const a=e.target;if(a.files&&a.files.length>0){const e=a.files[0];io.changeAvatar(e).then((e=>{e.data&&(t.value.avatar=l(e.data),io.info().then((e=>{Gt.setLoginUser(e.data)})))}))}},s=()=>{ai.logout().then((()=>{ss.push("/login"),Gt.removeLoginUser(),Gt.remove(Dt),Gt.remove(Ot)}))};return(e,n)=>(m(),f("div",li,[v(h(we),{trigger:"click",placement:"bottom"},{trigger:p((()=>[v(il,{onLongPress:o},{default:p((()=>[v(h(te),{size:56,src:t.value.avatar,"object-fit":"cover",class:"cursor-pointer"},null,8,["src"])])),_:1})])),default:p((()=>[g("div",ii,[g("div",ri,[n[0]||(n[0]=g("strong",null,"手机号：",-1)),g("span",null,w(t.value.phone),1)]),g("div",si,[n[1]||(n[1]=g("strong",null,"用户名：",-1)),g("span",null,w(t.value.username),1)]),g("div",ci,[n[2]||(n[2]=g("strong",null,"职业：",-1)),g("span",null,w(t.value.job),1)]),g("div",di,[v(ni),v(h(oe),{type:"error",size:"tiny",onClick:s},{default:p((()=>n[3]||(n[3]=[z("退出登录")]))),_:1})])])])),_:1}),g("input",{type:"file",ref_key:"avatarFileInputRef",ref:a,onChange:i,class:"display-none"},null,544)]))}}),[["__scopeId","data-v-82e981e2"]]),mi={class:"user-info-group"},pi={class:"online-notification-container"},vi={class:"online-info"},hi=Sa(l({__name:"UserInfoGroup",emits:["locationComment"],setup(e,{emit:t}){const a=t;r((()=>{i()}));const o=$e();s(o,(()=>{i()}));const l=n(0),i=()=>{io.online().then((e=>{(null==e?void 0:e.data)&&(l.value=e.data)}))},c=e=>{a("locationComment",e)};return(e,t)=>(m(),f("div",mi,[g("div",pi,[v(ti,{onLocationComment:c}),g("div",vi,[z(w(l.value),1),v(h(nn),{size:20})])]),v(ui)]))}}),[["__scopeId","data-v-e7094eb5"]]),gi={class:"comment-title-container"},fi={class:"comment-header-top"},wi={class:"comment-header-bottom"},bi=Sa(l({__name:"CommentHeader",props:{breadcrumb:{},modelValue:{}},emits:["breadcrumbClick","locationComment","update:modelValue"],setup(e,{emit:t}){const a=t,o=e=>{a("update:modelValue",e)};return(e,t)=>(m(),f("div",gi,[g("div",fi,[v(h(be),null,{default:p((()=>[(m(!0),f(S,null,A(e.breadcrumb,((e,t)=>(m(),u(h(ye),{key:t,onClick:e=>{a("breadcrumbClick",t)},class:"breadcrumb-item"},{default:p((()=>[v(h(pe),{class:"breadcrumb-text cursor-pointer"},{default:p((()=>[z(w(e.publisher)+" ",1),e.id&&e.publisherAvatar?(m(),u(h(te),{key:0,"object-fit":"cover",size:22,round:"",src:h(ja).getResourceURL(e.publisherAvatar)},null,8,["src"])):M("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}),v(hi,{onLocationComment:t[0]||(t[0]=t=>e.$emit("locationComment"))})]),g("div",wi,[v(h(ue),{size:"small",value:e.modelValue,"onUpdate:value":o},{default:p((()=>[v(h(me),{value:"0"},{default:p((()=>t[1]||(t[1]=[z("热评")]))),_:1}),v(h(me),{value:"1"},{default:p((()=>t[2]||(t[2]=[z("最新")]))),_:1}),v(h(me),{value:"2"},{default:p((()=>t[3]||(t[3]=[z("回复")]))),_:1})])),_:1},8,["value"])])]))}}),[["__scopeId","data-v-13563250"]]),yi={class:"comment-controls-container"},xi={class:"comment-reply-info"},ki={class:"comment-interaction-btn"},Ci=Sa(l({__name:"CommentControls",props:{comment:{},showReplyListBtn:{type:Boolean}},emits:["showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn"],setup:(e,{emit:t})=>(e,t)=>(m(),f("div",yi,[g("div",xi,[P(v(h(oe),{class:"comment-reply-list-btn",style:{"margin-left":"3%"},text:"",type:"info",onClick:t[0]||(t[0]=t=>e.$emit("showReplyList",e.comment))},{default:p((()=>t[5]||(t[5]=[z(" 回复列表> ")]))),_:1},512),[[U,e.showReplyListBtn&&!e.comment.fixed]]),v(h(vn),{onClick:t[1]||(t[1]=t=>e.$emit("handleCommentReplyClick",e.comment)),class:"cursor-pointer",size:20}),z(" "+w(e.comment.replyCount),1)]),g("div",ki,[v(h(qo),{color:e.comment.isLike?"var(--blue)":"",class:"cursor-pointer",size:16,onClick:t[2]||(t[2]=t=>e.$emit("interactionBtn",e.comment,1))},null,8,["color"]),z(" "+w(e.comment.likeCount)+" ",1),v(h(Yo),{color:e.comment.isDislike?"var(--blue)":"",class:"cursor-pointer",size:16,onClick:t[3]||(t[3]=t=>e.$emit("interactionBtn",e.comment,0))},null,8,["color"]),z(" "+w(e.comment.dislikeCount)+" ",1),v(h(pn),{color:e.comment.isFavorite?"var(--blue)":"",class:"cursor-pointer",size:18,onClick:t[4]||(t[4]=t=>e.$emit("favoriteBtn",e.comment))},null,8,["color"]),z(" "+w(e.comment.favoriteCount),1)])]))}),[["__scopeId","data-v-c085b415"]]),Li={class:"user-info-row"},Mi={class:"user-detail-col"},Si={class:"user-nickname"},Ai={class:"user-extra-info"},Ri={class:"comment-content-row"},Ei={class:"comment-reply-row"},zi=Sa(l({__name:"CommentListItem",props:{comment:{},flashCommentId:{},showReplyListBtn:{type:Boolean},commentInputVisible:{},quickReplyLoading:{}},emits:["showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn","quickReplyComment","updateEditor"],setup(e,{emit:t}){const a=t,o=[...cl,"characterCount"];return(e,t)=>(m(),f("div",{class:b({"user-comment-container-fixed":e.comment.fixed,"user-comment-container":!e.comment.fixed,"comment-flash":e.flashCommentId===e.comment.id})},[g("div",Li,[v(h(te),{round:"",size:"large","object-fit":"cover",src:e.comment.publisherAvatar?h(ja).getResourceURL(e.comment.publisherAvatar):""},null,8,["src"]),g("div",Mi,[g("span",Si,w(e.comment.publisher),1),g("span",Ai,[z(w(e.comment.publisherJob)+" | ",1),g("span",{class:"time-clickable",onClick:t[0]||(t[0]=t=>{var a;void 0===(a=e.comment).showExactTime?a.showExactTime=!0:a.showExactTime=!a.showExactTime})},w(e.comment.showExactTime?e.comment.exactPublishedAt:e.comment.publishedAt),1),z(" | "+w(e.comment.ipLocation),1)])])]),g("div",Ri,[v(pl,{"file-bucket":h(Cl),modelValue:e.comment.contentObj,"onUpdate:modelValue":t[1]||(t[1]=t=>e.comment.contentObj=t),extensions:h(cl),editable:!1},null,8,["file-bucket","modelValue","extensions"])]),v(Ci,{comment:e.comment,showReplyListBtn:e.showReplyListBtn,onShowReplyList:t[2]||(t[2]=t=>e.$emit("showReplyList",e.comment)),onHandleCommentReplyClick:t[3]||(t[3]=t=>e.$emit("handleCommentReplyClick",e.comment)),onInteractionBtn:t[4]||(t[4]=(t,a)=>e.$emit("interactionBtn",t,a)),onFavoriteBtn:t[5]||(t[5]=t=>e.$emit("favoriteBtn",e.comment))},null,8,["comment","showReplyListBtn"]),P(g("div",Ei,[v(pl,{modelValue:e.comment.quickCommentReply,"onUpdate:modelValue":t[6]||(t[6]=t=>e.comment.quickCommentReply=t),class:"comment-reply-tiptap-editor",ref:t=>t&&((e,t)=>{t&&"editor"in t&&a("updateEditor",e,t.editor)})(e.comment.id,t),"editor-props":{attributes:{class:"ProseMirrorInput","data-comment-id":e.comment.id}},"file-bucket":h(Cl),placeholder:"说是你的自由，但是...","show-character-count":!0,extensions:o,toolbar:!0,onKeydown:t[7]||(t[7]=H(B((t=>e.$emit("quickReplyComment",e.comment)),["alt","prevent"]),["enter"]))},null,8,["modelValue","editor-props","file-bucket"]),v(h(oe),{class:"comment-reply-send-btn",text:"",type:"info",loading:!(!e.quickReplyLoading||!e.comment.id)&&e.quickReplyLoading.get(e.comment.id),onClick:t[8]||(t[8]=t=>e.$emit("quickReplyComment",e.comment))},{default:p((()=>[v(h(sn),{size:28})])),_:1},8,["loading"])],512),[[U,e.commentInputVisible===e.comment.id]])],2))}}),[["__scopeId","data-v-1e1a0c32"]]),Ti={class:"comment-scroll"},Ii={class:"comment-list-footer"},_i=Sa(l({__name:"CommentList",props:{commentList:{},flashCommentId:{},showReplyListBtn:{type:Boolean},commentInputVisible:{},commentScrollTrigger:{},commentLoading:{type:Boolean},commentNoMore:{type:Boolean},hasCommentPermission:{type:Boolean},quickReplyLoading:{}},emits:["loadMoreComments","showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn","quickReplyComment","updateEditor","updateCommentRef"],setup(e,{emit:t}){const a=e,o=t,n=(e,t)=>{o("interactionBtn",e,t)},l=(e,t)=>{o("updateEditor",e,t)},i=()=>{0===a.commentList.length&&a.commentNoMore||o("loadMoreComments")};return(e,t)=>(m(),f("div",{class:b(["comment-list-container",{"has-input-box":"-1"==e.commentInputVisible}])},[v(h(xe),{onLoad:i,distance:50,trigger:e.commentScrollTrigger},{default:p((()=>[g("div",Ti,[(m(!0),f(S,null,A(e.commentList,(a=>(m(),f("div",{key:a.id,ref_for:!0,ref:e=>{e&&((e,t)=>{o("updateCommentRef",e,t)})(a.id,e)}},[v(zi,{comment:a,"flash-comment-id":e.flashCommentId,"show-reply-list-btn":e.showReplyListBtn,"comment-input-visible":e.commentInputVisible,"quick-reply-loading":e.quickReplyLoading,onShowReplyList:t[0]||(t[0]=t=>e.$emit("showReplyList",t)),onHandleCommentReplyClick:t[1]||(t[1]=t=>e.$emit("handleCommentReplyClick",t)),onInteractionBtn:n,onFavoriteBtn:t[2]||(t[2]=t=>e.$emit("favoriteBtn",t)),onQuickReplyComment:t[3]||(t[3]=t=>e.$emit("quickReplyComment",t)),onUpdateEditor:l},null,8,["comment","flash-comment-id","show-reply-list-btn","comment-input-visible","quick-reply-loading"])])))),128)),g("div",Ii,[e.commentLoading?(m(),u(h(ke),{key:0,class:"display-flex"})):e.commentLoading||!e.commentNoMore&&0!==e.commentList.length?M("",!0):(m(),u(h(Ce),{key:1,description:e.hasCommentPermission?"没有更多评论了...":"您没有权限查看评论"},null,8,["description"]))])])])),_:1},8,["trigger"])],2))}}),[["__scopeId","data-v-29b50c04"]]),Bi=Sa(l({__name:"CommentMainInput",props:{commentReply:{},sendCommentLoading:{type:Boolean},disabled:{type:Boolean}},emits:["sendComment","update:commentReply"],setup(e,{expose:t,emit:a}){const o=a,l=n(),i=n(),r=[...cl,"characterCount"];return t({commentInputWrapperRef:l,sendTiptapEditorRef:i}),(e,t)=>(m(),u(h(Le),{bottom:0,style:{"z-index":"1500"},class:"comment-input-affix"},{default:p((()=>[g("div",{class:"comment-input-row",ref_key:"commentInputWrapperRef",ref:l},[v(pl,{ref_key:"sendTiptapEditorRef",ref:i,"model-value":e.commentReply,"onUpdate:modelValue":t[0]||(t[0]=e=>o("update:commentReply",e)),class:"comment-tiptap-editor","editor-props":{attributes:{class:"ProseMirrorInput","data-main-editor":"true"}},"file-bucket":h(Cl),placeholder:"说是你的自由，但是...","show-character-count":!0,extensions:r,toolbar:!0,onKeydown:t[1]||(t[1]=H(B((t=>e.$emit("sendComment")),["alt","prevent"]),["enter"]))},null,8,["model-value","file-bucket"]),v(h(oe),{text:"",type:"info",loading:e.sendCommentLoading,onClick:t[2]||(t[2]=t=>e.$emit("sendComment")),class:"comment-reply-send-btn",size:"small",disabled:e.disabled},{default:p((()=>[v(h(cn),{size:28})])),_:1},8,["loading","disabled"])],512)])),_:1}))}}),[["__scopeId","data-v-160c36db"]]),$i={URL:"/core/comments",search:async(e,t)=>{const a={signal:t};return(await fa($i.URL+"/search",e,a).catch((e=>ga(e)))).data},location:async e=>(await fa($i.URL+"/location/"+e).catch((e=>ga(e)))).data,loadById:async e=>(await fa($i.URL+"/"+e).catch((e=>ga(e)))).data,load:async e=>(await fa($i.URL+e).catch((e=>ga(e)))).data,save:async e=>(await wa($i.URL,e).catch((e=>ga(e)))).data},Pi={URL:"/core/favorite",save:async e=>(await wa(Pi.URL,e).catch((e=>ga(e)))).data,toggle:async e=>(await wa(Pi.URL+"/toggle",e).catch((e=>ga(e)))).data},Ui={URL:"/core/interaction",save:async e=>(await wa(Ui.URL,e).catch((e=>ga(e)))).data,toggle:async e=>(await wa(Ui.URL+"/toggle",e).catch((e=>ga(e)))).data};function Fi(e){const t=n(new Map),a=n(!1),o=n("-1"),l=n(void 0),i=n(new Map);return{quickReplyLoading:t,sendCommentLoading:a,commentInputVisible:o,commentReply:l,quickReplyTiptapEditorMap:i,updateEditor:(e,t)=>{i.value.set(e,t)},interactionBtn:(e,t)=>{const a={targetType:0,targetId:e.id,actionType:t};Ui.save(a).then((a=>{const o=null==a?void 0:a.data;if(o){e.likeCount=o.likeCount,e.dislikeCount=o.dislikeCount;const a=1===t;o.cancel?a?(va.info("赞取消"),e.isLike=!1):(va.info("踩取消"),e.isDislike=!1):a?(va.success("赞 :)"),e.isLike=!0):(va.warning("踩 :("),e.isDislike=!0)}}))},favoriteBtn:e=>{const t={targetType:0,targetId:e.id};Pi.save(t).then((t=>{const a=null==t?void 0:t.data;a&&(e.favoriteCount=a.count,a.cancel?(va.info("取消收藏"),e.isFavorite=!1):(va.success("已收藏"),e.isFavorite=!0))}))},clearAllQuickReplyContent:()=>(o.value="-1",({commentList:e})=>{e.forEach((e=>{e.quickCommentReply&&(e.quickCommentReply=void 0);const t=i.value.get(e.id);t&&t.commands.clearContent()}))}),handleCommentReplyClick:(e,{isLastBreadcrumb:t})=>{o.value!==e.id?(o.value=e.id,c((()=>{var a,o;if(t&&!e.fixed){const t=i.value.get(e.id);if(t&&(!e.quickCommentReply||!(null==(o=null==(a=e.quickCommentReply.content)?void 0:a[0])?void 0:o.content))){const a={type:"doc",content:[{type:"paragraph",content:[{type:"mention",attrs:{id:e.publisher,label:e.publisher,avatar:e.publisherAvatar||""}},{type:"text",text:" "}]}]};t.commands.setContent(a)}}}))):o.value="-1"},debouncedQuickReplyComment:(a,o)=>{ma(`comment-quick-reply-${a.id}`,(()=>{((a,{isLastBreadcrumb:o,onSuccess:n})=>{if(!wl(t.value,a.id))return;const l=i.value.get(a.id),r={value:a.quickCommentReply},s=fl(l,r,"啥也没有可不能发送哦~");if(!s.isValid)return;a.quickCommentReply=s.content||r.value,bl(t.value,!0,a.id);const c=Un.toJsonString(a.quickCommentReply);$i.save({content:c,articleId:e(),parentCommentId:o&&!a.fixed?a.parentCommentId:a.id}).then((e=>{va.success("发送成功");const t=null==e?void 0:e.data;t&&n&&n(t),l.commands.clearContent(),a.quickCommentReply=void 0})).finally((()=>{t.value.set(a.id,!1)}))})(a,o)}),300)},debouncedSendComment:(t,o)=>{ma("comment-send",(()=>{((t,{lastBreadcrumbComment:o,onSuccess:n})=>{if(!wl(a))return;const i=null==t?void 0:t.sendTiptapEditorRef,r=fl(i,l,"啥也没有可不能发送哦~");if(!r.isValid)return;l.value=r.content||l.value,bl(a,!0);const s=Un.toJsonString(l.value),c=o,d=e();$i.save({content:s,articleId:d,parentCommentId:c.id}).then((e=>{va.success("发送成功");const t=null==e?void 0:e.data;t&&n&&n(t),null==i||i.clearContent(),l.value=void 0})).finally((()=>{a.value=!1}))})(t,o)}),300)}}}function Hi(e){const t=n([]),a=n(new Map),o=n(!1),l=n(!1),r=n("0"),d=Vn(),u=$e(),m=Pe(),p=n(!1),v=n("0"),h=n([{id:"",publisher:"评论列表"}]),g=i((()=>3===h.value.length)),f=i((()=>h.value.length-1)),w=i((()=>h.value[h.value.length-1])),b=()=>{h.value=[{id:"",publisher:"评论列表"}]},y=e=>{h.value.push(e)},x=n(!0),k=()=>{t.value=[],a.value.clear(),o.value=!1,l.value=!1,C()},C=()=>{r.value=String(Date.now())};s(u,(()=>{L()?A():T()})),s((()=>d.getId),(e=>{e&&A(e)}));const L=()=>d.getId,M=n(""),S=e=>{c((()=>{const t=a.value.get(e);t&&(t.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout((()=>{t.scrollIntoView({behavior:"smooth",block:"center"}),(e=>{M.value=e,setTimeout((()=>{M.value=""}),1e3)})(e)}),10))}))},A=(e=L())=>{o.value=!0,e&&(d.setId(e),m.push({params:{...u.params,commentId:e}}),$i.location(e).then((t=>{const a=null==t?void 0:t.data;if(a){const t=a.parents;(e=>{k(),R(e)})(a.comments),b();const o=t.length;if(o>0){for(let a=o-1;a>=0;a--){const e=t[a];y(e)}const e=t[0];z(e)}S(e)}})).catch((e=>{e.response&&403===e.response.status&&k()})).finally((()=>{o.value=!1})))},R=e=>{e&&0!==e.length&&e.map((({...e})=>({...e,publishedAt:Jl.getRelativeTime(e.publishedAt),exactPublishedAt:Jl.toTimeString(e.publishedAt),fixed:!1}))).forEach((e=>{e.contentObj=Un.toJsonObject(e.content),e.quickCommentReply=void 0,t.value.push(e)}))},E=e=>{t.value.unshift({...e,publishedAt:Jl.getRelativeTime(e.publishedAt),exactPublishedAt:Jl.toTimeString(e.publishedAt),fixed:!0,contentObj:Un.toJsonObject(e.content)})},z=async(e,a=!1)=>{if(f.value>0&&(0==t.value.length||!t.value[0].fixed))if(a){const t=await $i.loadById(e.id);(null==t?void 0:t.data)&&E(t.data)}else E(e)},T=(e=!0)=>{if(!o.value||e){if(l.value&&!e){if(0!==t.value.length)return;l.value=!1}e&&k(),I(w.value)}},I=a=>{if(o.value||l.value)return;o.value=!0;pa("article-comments",(()=>{const n=t.value.length>0?t.value[t.value.length-1].id:"";l.value?o.value=!1:Promise.allSettled([$i.load(`?articleId=${e()}&id=${n}&parentCommentId=${null==a?void 0:a.id}&loadSize=5&sortType=${v.value}`).catch((e=>{if(e.response&&403===e.response.status)throw o.value=!1,e;throw e})),z(a,!0)]).then((e=>{var t,a,o;if("fulfilled"===e[0].status&&(null==(a=null==(t=e[0])?void 0:t.value)?void 0:a.data)){const t=(null==(o=e[0])?void 0:o.value).data;if(0==(null==t?void 0:t.length))return void(l.value=!0);t.length<5&&(l.value=!0),R(t),x.value=f.value<2,c((()=>{C()}))}else l.value=!0})).catch((e=>{l.value=!1})).finally((()=>{o.value=!1}))}),300)};return{commentList:t,commentRefs:a,commentLoading:o,commentNoMore:l,commentScrollTrigger:r,flashCommentId:M,breadcrumb:h,isLastBreadcrumb:g,lastBreadcrumbIndex:f,lastBreadcrumbComment:w,showReplyListBtn:x,hasCommentPermission:p,sortType:v,resetBreadcrumb:b,addBreadcrumb:y,resetCommentList:k,updateCommentRef:(e,t)=>{a.value.set(e,t)},locationComment:A,loadCurrentCommentList:T,loadCommentList:I,scrollToComment:S,setFirstFixedComment:z,addCommentList:R,getCommentId:L,commentStore:d,initCommentPermission:()=>{$i.load(`?articleId=${e()}&id=&parentCommentId=&loadSize=1`).then((e=>{(null==e?void 0:e.data)&&(p.value=!0)})).catch((e=>{e.response&&403===e.response.status&&(p.value=!1)}))}}}const Vi=Sa(l({__name:"CommentInfo",props:{articleId:{type:Function,required:!0}},emits:["sendEnd","quickReplyEnd"],setup(e,{expose:t,emit:a}){const o=e,l=a,i=n(0),d=n(),u=n(),p=Hi(o.articleId),g=Fi(o.articleId),w=()=>{c((()=>{var e;if(null==(e=u.value)?void 0:e.commentInputWrapperRef){i.value=u.value.commentInputWrapperRef.offsetHeight||0;const e=document.querySelector(".comment-list-container");e&&(e.style.paddingBottom="-1"===g.commentInputVisible.value?`${i.value+12}px`:"1.25rem")}}))};r((()=>{p.initCommentPermission(),b(),window.addEventListener("resize",b);const e=p.getCommentId();e?p.locationComment(e):p.loadCurrentCommentList()})),L((()=>{window.removeEventListener("resize",b)}));s((()=>g.commentInputVisible.value),(()=>c(w))),s((()=>p.hasCommentPermission.value),(e=>{e&&c(w)})),s(ea,(()=>{c((()=>{document.querySelectorAll(".ProseMirror, .editor-content, .tiptap-editor-wrapper").forEach((e=>{if(e instanceof HTMLElement){const t=e.style.backgroundColor;e.style.backgroundColor="transparent",e.offsetHeight,e.style.backgroundColor=t}}))}))})),s((()=>g.commentReply.value),(e=>{if(e&&e.content&&Array.isArray(e.content)){e.content.some((e=>!(!e.content||!Array.isArray(e.content))&&e.content.some((e=>!("text"!==e.type||!e.text||!e.text.trim())||"text"!==e.type))))||(g.commentReply.value=void 0)}}));const b=()=>{c((()=>{var e;if(d.value&&(null==(e=u.value)?void 0:e.commentInputWrapperRef)){const e=d.value.offsetWidth;u.value.commentInputWrapperRef.style.width=`${e}px`,w()}}))},y=e=>{p.resetCommentList(),e!==p.lastBreadcrumbIndex.value&&p.breadcrumb.value.splice(e+1);g.clearAllQuickReplyContent()({commentList:p.commentList.value}),p.loadCommentList(p.breadcrumb.value[e])},x=e=>{p.resetCommentList();g.clearAllQuickReplyContent()({commentList:p.commentList.value}),p.addBreadcrumb(e),p.loadCommentList(e)},k=e=>{g.handleCommentReplyClick(e,{isLastBreadcrumb:p.isLastBreadcrumb.value})},C=e=>{g.debouncedQuickReplyComment(e,{isLastBreadcrumb:p.isLastBreadcrumb.value,onSuccess:e=>{p.locationComment(e),g.commentInputVisible.value="-1"}}),l("quickReplyEnd")},M=()=>{g.debouncedSendComment(u.value,{lastBreadcrumbComment:p.lastBreadcrumbComment.value,onSuccess:e=>{p.locationComment(e)}}),l("sendEnd")},S=e=>{p.sortType.value=e,p.resetCommentList(),p.loadCurrentCommentList()};return t({loadCurrentCommentList:p.loadCurrentCommentList}),(e,t)=>(m(),f("div",{ref_key:"commentInfoRef",ref:d,class:"comment-info-container"},[v(bi,{breadcrumb:h(p).breadcrumb.value,"model-value":h(p).sortType.value,"onUpdate:modelValue":S,onBreadcrumbClick:y,onLocationComment:h(p).locationComment},null,8,["breadcrumb","model-value","onLocationComment"]),v(_i,{"comment-list":h(p).commentList.value,"flash-comment-id":h(p).flashCommentId.value,"show-reply-list-btn":h(p).showReplyListBtn.value,"comment-input-visible":h(g).commentInputVisible.value,"comment-scroll-trigger":h(p).commentScrollTrigger.value,"comment-loading":h(p).commentLoading.value,"comment-no-more":h(p).commentNoMore.value,"has-comment-permission":h(p).hasCommentPermission.value,"quick-reply-loading":h(g).quickReplyLoading.value,onLoadMoreComments:t[0]||(t[0]=e=>h(p).loadCurrentCommentList(!1)),onShowReplyList:x,onHandleCommentReplyClick:k,onInteractionBtn:h(g).interactionBtn,onFavoriteBtn:h(g).favoriteBtn,onQuickReplyComment:C,onUpdateEditor:h(g).updateEditor,onUpdateCommentRef:h(p).updateCommentRef},null,8,["comment-list","flash-comment-id","show-reply-list-btn","comment-input-visible","comment-scroll-trigger","comment-loading","comment-no-more","has-comment-permission","quick-reply-loading","onInteractionBtn","onFavoriteBtn","onUpdateEditor","onUpdateCommentRef"]),P(v(Bi,{"comment-reply":h(g).commentReply.value,"onUpdate:commentReply":t[1]||(t[1]=e=>h(g).commentReply.value=e),"send-comment-loading":h(g).sendCommentLoading.value,onSendComment:M,ref_key:"commentMainInputRef",ref:u},null,8,["comment-reply","send-comment-loading"]),[[U,h(p).hasCommentPermission.value&&"-1"==h(g).commentInputVisible.value]])],512))}}),[["__scopeId","data-v-099b34b5"]]);function Oi(){const e=Hn(),t=$e(),a=n({}),o=n(!0),l=()=>e.getId,i=()=>{o.value=!0;const e=l();e&&Fn.detail(e).then((e=>{const t=null==e?void 0:e.data;t&&(a.value={...t,contentObj:Un.toJsonObject(t.content),tags:t.tag.split(","),isOwner:!0===t.isOwner,publishedAt:Jl.getRelativeTime(t.publishedAt),lastModified:Jl.getRelativeTime(t.lastModified),exactPublishedAt:Jl.toTimeString(t.publishedAt),exactLastModified:Jl.toTimeString(t.lastModified)},o.value=!1),Wt.debug("article detail: ",a.value)})).catch((e=>{o.value=!1,e.response&&403===e.response.status?(va.error("哎呀，您没有权限查看这篇文章"),ss.push("/")):va.error("加载文章失败，请稍后重试")}))};return{article:a,articleLoading:o,getArticleId:l,loadArticleDetail:i,loadArticleDetailCount:()=>{const e=l();e&&Fn.detail(e).then((e=>{const t=null==e?void 0:e.data;t&&(a.value.likeCount=t.likeCount,a.value.dislikeCount=t.dislikeCount,a.value.favoriteCount=t.favoriteCount,a.value.commentCount=t.commentCount)}))},backHome:()=>{ss.push("/")},initialize:()=>{r((()=>{i()})),s(t,((e,t)=>{var a;e.params.articleId!==(null==(a=null==t?void 0:t.params)?void 0:a.articleId)&&i()})),s(ea,(()=>{c((()=>{if(!o.value){const e=document.querySelector(".article-content .ProseMirror");e instanceof HTMLElement&&(e.classList.add("theme-priority"),e.offsetHeight)}}))}))}}}const Di={class:"article-layout"},ji={key:0,class:"article-info-container"},Ni={class:"article-header"},qi={class:"article-header-content-wrapper"},Yi={class:"article-header-content"},Ji={class:"article-tag-container"},Wi={class:"flex-column-start"},Ki={class:"action-buttons-container"},Gi={class:"edit-button-container"},Xi={class:"interaction-container"},Qi={class:"comment-count-container"},Zi={class:"article-content"},er={style:{"padding-right":"1rem"}},tr=Sa(l({__name:"Article",setup(e){const t=Oi(),a=(o=t.article,{interactionBtn:(e,t)=>{const a={targetType:1,targetId:e,actionType:t};Ui.save(a).then((e=>{const a=null==e?void 0:e.data;if(a){o.value.likeCount=a.likeCount,o.value.dislikeCount=a.dislikeCount;const e=1===t;a.cancel?e?(va.info("赞取消"),o.value.isLike=!1):(va.info("踩取消"),o.value.isDislike=!1):e?(va.success("赞 :)"),o.value.isLike=!0):(va.warning("踩 :("),o.value.isDislike=!0)}}))},favoriteBtn:e=>{const t={targetType:1,targetId:e};Pi.save(t).then((e=>{const t=null==e?void 0:e.data;t&&(o.value.favoriteCount=t.count,t.cancel?(va.info("取消收藏"),o.value.isFavorite=!1):(va.success("已收藏"),o.value.isFavorite=!0))}))}});var o;const l=function(e){return{toggleTimeFormat:t=>{"publish"===t?void 0===e.value.showExactPublishTime?e.value.showExactPublishTime=!0:e.value.showExactPublishTime=!e.value.showExactPublishTime:void 0===e.value.showExactModifyTime?e.value.showExactModifyTime=!0:e.value.showExactModifyTime=!e.value.showExactModifyTime}}}(t.article),i=n(),r=n();t.initialize();const s=()=>{i.value.openEditArticleDialog(t.article.value)},c=()=>{t.loadArticleDetail(),r.value.loadCurrentCommentList()};return(e,o)=>(m(),f("div",Di,[v(Pl,{show:h(t).articleLoading.value},null,8,["show"]),h(t).articleLoading.value?M("",!0):(m(),f("div",ji,[g("div",Ni,[g("div",qi,[g("div",Yi,[g("h2",null,w(h(t).article.value.title),1),g("div",Ji,[(m(!0),f(S,null,A(h(t).article.value.tags,(e=>(m(),u(h(Me),{class:"article-tag",key:e,type:"primary"},{default:p((()=>[z(w(e),1)])),_:2},1024)))),128))])])]),g("div",Wi,[v(h(pe),{type:"info",class:"display-block time-clickable",onClick:o[0]||(o[0]=e=>h(l).toggleTimeFormat("publish"))},{default:p((()=>[z(" 发布时间："+w(h(t).article.value.showExactPublishTime?h(t).article.value.exactPublishedAt:h(t).article.value.publishedAt),1)])),_:1}),v(h(pe),{type:"info",class:"display-block time-clickable",onClick:o[1]||(o[1]=e=>h(l).toggleTimeFormat("modify"))},{default:p((()=>[z(" 最近修改："+w(h(t).article.value.showExactModifyTime?h(t).article.value.exactLastModified:h(t).article.value.lastModified),1)])),_:1}),v(h(pe),{type:"info",class:"display-block"},{default:p((()=>[z(" 拥有者："+w(h(t).article.value.publisher)+" | 等级："+w(h(t).article.value.operationLevel)+" | ip: "+w(h(t).article.value.ipLocation),1)])),_:1})]),g("div",Ki,[g("div",Gi,[h(t).article.value.isOwner?(m(),u(h(mn),{key:0,class:"cursor-pointer",size:28,onClick:s})):M("",!0),v(h(No),{class:"cursor-pointer",size:28,onClick:h(t).backHome},null,8,["onClick"])]),g("div",Xi,[v(h(qo),{color:h(t).article.value.isLike?"var(--blue)":"",size:16,class:"cursor-pointer",onClick:o[2]||(o[2]=e=>h(a).interactionBtn(h(t).article.value.id,1))},null,8,["color"]),z(" "+w(h(t).article.value.likeCount)+" ",1),v(h(Yo),{color:h(t).article.value.isDislike?"var(--blue)":"",size:16,class:"cursor-pointer",onClick:o[3]||(o[3]=e=>h(a).interactionBtn(h(t).article.value.id,0))},null,8,["color"]),z(" "+w(h(t).article.value.dislikeCount)+" ",1),v(h(pn),{color:h(t).article.value.isFavorite?"var(--blue)":"",size:18,class:"cursor-pointer",onClick:o[4]||(o[4]=e=>h(a).favoriteBtn(h(t).article.value.id))},null,8,["color"]),z(" "+w(h(t).article.value.favoriteCount),1)]),g("div",Qi,[v(h(Jo),{size:20}),z(w(h(t).article.value.commentCount),1)])])]),g("div",Zi,[v(h(Z),null,{default:p((()=>[g("div",er,[v(pl,{modelValue:h(t).article.value.contentObj,"onUpdate:modelValue":o[5]||(o[5]=e=>h(t).article.value.contentObj=e),editable:!1,"file-bucket":h(kl),"all-extensions":!0,"character-limit":h(dl),"use-thumbnail":!0},null,8,["modelValue","file-bucket","character-limit"])])])),_:1})])])),v(Ml,{ref_key:"articleModalRef",ref:i,onSuccess:c},null,512),v(Vi,{ref_key:"commentInfoRef",ref:r,articleId:h(t).getArticleId,onQuickReplyEnd:h(t).loadArticleDetailCount,onSendEnd:h(t).loadArticleDetailCount},null,8,["articleId","onQuickReplyEnd","onSendEnd"])]))}}),[["__scopeId","data-v-016c23d7"]]),ar={},or={class:"article-header"},nr={class:"flex-between-center"},lr={class:"article-content"},ir=Sa(l({__name:"ArticleCard",props:{article:{type:Object,required:!0},index:{type:Number,required:!0},cardColor:{type:String,required:!0},isDragging:{type:Boolean,default:!1},draggedArticle:{type:Object,default:null},dragOverCardId:{type:String,default:null},dragOverPosition:{type:String,default:null},isSingleCardRow:{type:Boolean,default:!1},dragStyle:{type:Object,default:()=>({})}},emits:["toggleScope","startLongPress","cancelLongPress","download","setEditor"],setup(e,{emit:t}){const a=e,o=t,n=i((()=>{var e;const t=a.isDragging&&(null==(e=a.draggedArticle)?void 0:e.id)===a.article.id,o=a.isDragging&&a.dragOverCardId===a.article.id;return{dragging:t,"drag-over-before":o&&"before"===a.dragOverPosition&&!a.isSingleCardRow,"drag-over-after":o&&"after"===a.dragOverPosition&&!a.isSingleCardRow,"drag-over-before-vertical":o&&"before"===a.dragOverPosition&&a.isSingleCardRow,"drag-over-after-vertical":o&&"after"===a.dragOverPosition&&a.isSingleCardRow}})),l=i((()=>{var e;return{backgroundColor:a.cardColor,...a.isDragging&&(null==(e=a.draggedArticle)?void 0:e.id)===a.article.id?a.dragStyle:{}}})),r=i((()=>{const e=a.article.publishedScope===vl.PERSONAL;return a.article.isOwner?`点击切换为${e?"公开":"个人"}可见`:(e?"个人":"公开")+"可见"})),s=()=>{const e=ss.resolve({name:"Article",params:{articleId:a.article.id}}),t=window.open(e.href,"_blank");null==t||t.focus()},c=e=>{o("startLongPress",e,a.article,e.currentTarget)},d=e=>{o("startLongPress",e,a.article,e.currentTarget)};return(t,a)=>(m(),u(h(Se),{class:b(["card-item cursor-pointer",n.value]),"data-article-id":e.article.id,onClick:B(s,["ctrl"]),"header-style":"padding-bottom:0.25rem;border-bottom: var(--border-1);",style:x(l.value)},{header:p((()=>[g("div",or,[e.article.isOwner?(m(),u(h(ae),{key:0},{trigger:p((()=>[g("div",{class:"scope-icon-wrapper clickable",onClick:a[0]||(a[0]=B((a=>t.$emit("toggleScope",e.article)),["stop"]))},[(m(),u(I(e.article.publishedScope==h(vl).PERSONAL?h(Qo):h(Zo)),{size:18}))])])),default:p((()=>[z(" "+w(r.value),1)])),_:1})):M("",!0),g("div",{class:"article-title",onClick:B(s,["stop"])},w(e.article.title),1)])])),"header-extra":p((()=>[v(h(te),{round:"",size:45,src:e.article.publisherAvatar,"object-fit":"cover",class:"article-avatar",onMousedown:B(c,["stop"]),onTouchstart:B(d,["stop"]),onMouseup:a[1]||(a[1]=B((e=>t.$emit("cancelLongPress")),["stop"])),onMouseleave:a[2]||(a[2]=B((e=>t.$emit("cancelLongPress")),["stop"])),onTouchcancel:a[3]||(a[3]=B((e=>t.$emit("cancelLongPress")),["stop"])),onContextmenu:a[4]||(a[4]=B((()=>{}),["prevent"]))},null,8,["src"])])),default:p((()=>[g("div",nr,[g("div",null,[(m(!0),f(S,null,A(e.article.tags,(e=>(m(),u(h(Me),{type:"primary",class:"card-tag",key:e},{default:p((()=>[z(w(e),1)])),_:2},1024)))),128))]),g("div",null,[v(h(dn),{size:24,class:"cursor-pointer",onClick:a[5]||(a[5]=B((a=>t.$emit("download",e.article.id)),["stop"]))})])]),g("div",lr,[v(h(Z),{style:{"padding-right":"0.5rem"}},{default:p((()=>[v(pl,{ref:a=>a&&t.$emit("setEditor",e.article.id,a),modelValue:e.article.contentObj,"onUpdate:modelValue":a[6]||(a[6]=t=>e.article.contentObj=t),editable:!1,"file-bucket":h(kl),"all-extensions":!0,"character-limit":h(dl)},null,8,["modelValue","file-bucket","character-limit"])])),_:1})])])),_:1},8,["class","data-article-id","style"]))}}),[["__scopeId","data-v-dc274bb4"]]),rr={class:"trash-bin-text"},sr=Sa(l({__name:"TrashBin",props:{visible:{type:Boolean},isActive:{type:Boolean}},setup:e=>(e,t)=>(m(),u(V,{name:"trash-bin-fade"},{default:p((()=>[e.visible?(m(),f("div",{key:0,class:b(["trash-bin",{"trash-bin-active":e.isActive}])},[(m(),f("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"48",height:"48",style:x({color:e.isActive?"#ff4444":"#666666"})},t[0]||(t[0]=[g("path",{fill:"currentColor",d:"M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"},null,-1)]),4)),g("span",rr,w(e.isActive?"释放删除":"拖拽到此处删除"),1)],2)):M("",!0)])),_:1}))}),[["__scopeId","data-v-4296f384"]]);function cr(e,t,a){const o=e.cloneNode(!0);!function(e,t){const a=window.getComputedStyle(e);ur(e,t,a);const o=e.querySelectorAll("*"),n=t.querySelectorAll("*");for(let l=0;l<o.length&&l<n.length;l++){const e=o[l];ur(e,n[l],window.getComputedStyle(e))}}(e,o);const n=document.createElement("div");return n.style.cssText=`\n    position: fixed;\n    left: ${a.x}px;\n    top: ${a.y}px;\n    width: ${.5*t.width}px;\n    height: ${.5*t.height}px;\n    transform: translate(-50%, -50%) scale(1);\n    opacity: 0.8;\n    z-index: 9999;\n    pointer-events: none !important;\n    transition: none;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n    border-radius: 0.5rem;\n    overflow: hidden;\n    transform-origin: center center;\n  `,o.style.cssText=`\n    width: ${t.width}px;\n    height: ${t.height}px;\n    transform: scale(0.5);\n    transform-origin: top left;\n    margin: 0;\n    position: relative;\n    pointer-events: none !important;\n  `,function(e){e.style.pointerEvents="none !important",e.style.userSelect="none !important",e.style.setProperty("-webkit-user-select","none","important"),e.style.setProperty("-moz-user-select","none","important"),e.style.setProperty("-ms-user-select","none","important");const t=["onclick","onmousedown","onmouseup","ontouchstart","ontouchend","onscroll","onwheel"];t.forEach((t=>e.removeAttribute(t)));e.querySelectorAll("*").forEach((e=>{const a=e;a.style.pointerEvents="none !important",a.style.userSelect="none !important",a.style.setProperty("-webkit-user-select","none","important"),a.style.setProperty("-moz-user-select","none","important"),a.style.setProperty("-ms-user-select","none","important"),t.forEach((e=>a.removeAttribute(e)))}));e.querySelectorAll('.n-scrollbar, .article-content, [style*="overflow"]').forEach((e=>{const t=e;t.style.overflow="hidden !important",t.style.pointerEvents="none !important",t.classList.contains("article-content")&&(t.style.height="auto !important",t.style.maxHeight="none !important")}));e.querySelectorAll(".n-scrollbar-rail").forEach((e=>e.remove()));e.querySelectorAll(".n-scrollbar-content").forEach((e=>{const t=e;t.style.overflow="visible !important",t.style.height="auto !important",t.style.maxHeight="none !important",t.style.pointerEvents="none !important"}));e.querySelectorAll("iframe, script, noscript, object, embed").forEach((e=>e.remove()))}(o),n.appendChild(o),document.body.appendChild(n),n}function dr(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function ur(e,t,a){let o="";["background","background-color","background-image","background-size","background-position","color","font-family","font-size","font-weight","font-style","border","border-radius","border-color","border-width","border-style","padding","margin","width","height","max-width","max-height","min-width","min-height","display","position","top","left","right","bottom","flex","flex-direction","flex-wrap","justify-content","align-items","text-align","text-decoration","text-transform","line-height","opacity","visibility","box-shadow","text-shadow","transform","transition"].forEach((e=>{const t=a.getPropertyValue(e);t&&"none"!==t&&"auto"!==t&&"initial"!==t&&(o+=`${e}: ${t} !important; `)})),o+="\n    pointer-events: none !important;\n    user-select: none !important;\n  ",t.classList.contains("article-content")?o+="\n      overflow: hidden !important;\n      height: auto !important;\n      max-height: none !important;\n    ":t.classList.contains("n-scrollbar-content")?o+="\n      overflow: visible !important;\n      height: auto !important;\n      max-height: none !important;\n    ":o+="\n      overflow: hidden !important;\n    ",o&&(t.style.cssText=(t.style.cssText||"")+o)}function mr(e={}){const t=n(!1),a=n(null),o=n(!1),l=n(!1),r=n(null),s=n(null),c=n(!1),d=n({x:0,y:0});let u=null,m=null;let p={x:0,y:0},v=!1,h=null,g=!1;const f=()=>{m&&(clearTimeout(m),m=null),v=!1},w=(n,l,i)=>{var r;v=!1,t.value=!0,a.value=l,l.isOwner&&(o.value=!0);const{clientX:s,clientY:c}=b(n);d.value={x:s,y:c};const m=i.closest(".card-item");if(m){const e=m.getBoundingClientRect();u=cr(m,e,d.value)}h=function(e){const{onMove:t,onEnd:a}=e,o=e=>{t(e)},n=()=>{a(),l()};document.addEventListener("mousemove",o,{passive:!1}),document.addEventListener("mouseup",n),document.addEventListener("touchmove",o,{passive:!1}),document.addEventListener("touchend",n),document.addEventListener("touchcancel",n);const l=()=>{document.removeEventListener("mousemove",o),document.removeEventListener("mouseup",n),document.removeEventListener("touchmove",o),document.removeEventListener("touchend",n),document.removeEventListener("touchcancel",n)};return l}({onMove:y,onEnd:S}),null==(r=e.onDragStart)||r.call(e,l)},b=e=>{if(e instanceof MouseEvent)return{clientX:e.clientX,clientY:e.clientY};if(e instanceof TouchEvent){const t=e.touches.length>0?e.touches[0]:e.changedTouches[0];if(t)return{clientX:t.clientX,clientY:t.clientY}}return{clientX:0,clientY:0}},y=e=>{if(!t.value)return;e.preventDefault();const{clientX:a,clientY:o}=b(e);d.value={x:a,y:o},u&&(u.style.left=`${a}px`,u.style.top=`${o}px`),k()};let x=0;const k=()=>{var e;const t=Date.now();t-x>50&&(x=t,(null==(e=a.value)?void 0:e.isOwner)&&C(),L())},C=()=>{const e=document.querySelector(".trash-bin");if(!e)return;const t=e.getBoundingClientRect(),{x:a,y:o}=d.value;l.value=a>=t.left&&a<=t.right&&o>=t.top&&o<=t.bottom},L=()=>{const e=document.querySelectorAll(".card-item:not(.dragging)");r.value=null,s.value=null,c.value=!1,e.forEach((e=>{var t;const o=e.getBoundingClientRect(),{x:n,y:l}=d.value;if(n>=o.left&&n<=o.right&&l>=o.top&&l<=o.bottom){const i=e.dataset.articleId;if(i&&i!==(null==(t=a.value)?void 0:t.id)){r.value=i;const t=M(e);c.value=t,s.value=t?l<o.top+o.height/2?"before":"after":n<o.left+o.width/2?"before":"after"}}}))},M=e=>{const t=document.querySelectorAll(".card-item:not(.dragging)"),a=e.getBoundingClientRect(),o=document.querySelector(".article-container");if((o?o.getBoundingClientRect().width:window.innerWidth)<=768)return!0;let n=0;return t.forEach((e=>{const t=e.getBoundingClientRect();!(t.bottom<=a.top||t.top>=a.bottom)&&n++})),1===n},S=()=>{var n,i,c,d;t.value&&!g&&(g=!0,h&&(h(),h=null),l.value&&(null==(n=a.value)?void 0:n.isOwner)?null==(i=e.onDelete)||i.call(e,a.value):r.value&&s.value&&a.value&&(null==(c=e.onReorder)||c.call(e,a.value.id,r.value,s.value)),u&&(dr(u),u=null),t.value=!1,a.value=null,o.value=!1,l.value=!1,r.value=null,s.value=null,null==(d=e.onDragEnd)||d.call(e),setTimeout((()=>{g=!1}),100))},A=i((()=>({})));return _((()=>{f(),u&&dr(u),h&&h()})),{isDragging:t,draggedArticle:a,showTrashBin:o,isOverTrashBin:l,dragPosition:d,dragStyle:A,dragOverCardId:r,dragOverPosition:s,isSingleCardRow:c,isLongPressActive:i((()=>v)),startLongPress:(e,t,a)=>{e.preventDefault(),e.stopPropagation(),f();const{clientX:o,clientY:n}=b(e);p={x:o,y:n},v=!0;const l=e=>{if(!v)return;const{clientX:t,clientY:a}=b(e);Math.sqrt(Math.pow(t-p.x,2)+Math.pow(a-p.y,2))>10&&(f(),r())},i=()=>{f(),r()},r=()=>{v=!1,document.removeEventListener("mousemove",l),document.removeEventListener("touchmove",l),document.removeEventListener("mouseup",i),document.removeEventListener("touchend",i),document.removeEventListener("touchcancel",i)};document.addEventListener("mousemove",l,{passive:!1}),document.addEventListener("touchmove",l,{passive:!1}),document.addEventListener("mouseup",i),document.addEventListener("touchend",i),document.addEventListener("touchcancel",i),m=setTimeout((()=>{v&&(r(),w(e,t,a))}),500)},cancelLongPress:()=>{v&&!t.value&&f()},forceReset:()=>{f(),h&&(h(),h=null),u&&(dr(u),u=null),t.value=!1,a.value=null,o.value=!1,l.value=!1,r.value=null,s.value=null,g=!1}}}const pr={class:"infinite-load-info"},vr=Sa(l({__name:"ArticleList",props:{searchCondition:{type:Object,required:!0}},emits:["reset"],setup(e,{expose:t,emit:a}){const o=e,{articleList:l,loading:d,noMore:w,cardColSpan:b,articleTiptapEditorMap:y,containerRef:x,scrollContainerRef:k,getCardColor:C,updateColSpan:L,resetList:R,loadArticles:E,handleToggleScope:z,handleDeleteArticle:T,handleReorderArticles:I}=function(e){const t=n([]),a=n(!1),o=n(!1),l=n(6),r=n(new Map),d=n(0),u=n(1),m=n(null),p=n(null),v=Ae(),h=["#ffd6d6","#ffe8d1","#fff8c4","#d5edd7","#d0e8fa","#ded6f2","#ebcfe9","#f8d4de"],g=["#8c3a3a","#7d6339","#75763a","#366d5a","#355678","#534878","#664766","#6a4251"],f=i((()=>ea.value===Qt.DARK)),w=i((()=>{var e,a;const o=u.value,n=(null==(e=m.value)?void 0:e.clientHeight)||0;let l;if(0===t.value.length)l=Math.ceil(n/470)*o,l=Math.max(o,l);else{const e=Math.ceil(((null==(a=m.value)?void 0:a.clientHeight)||0)/470)*o;l=Math.ceil(e/2),l=Math.ceil(l/o)*o,l=Math.max(o,l)}return l}));s(u,((e,a)=>{if(e!==a){const a=t.value.length%e;0!==a&&b(!0,void 0,e-a)}}));const b=(n=!1,l,i)=>{const r=void 0!==i?i:w.value;if(a.value||o.value)return Promise.resolve();a.value=!0;const s=t.value.length>0?t.value[t.value.length-1]:null,u=null==s?void 0:s.id;return new Promise(((t,n)=>{c((()=>{const i={...e.searchCondition,id:u,loadSize:r};Fn.search(i,l).then((e=>{if(!e||!e.data)return void t();const a=e.data;return 0===a.length?(o.value=!0,void t()):(a.length<r&&(o.value=!0),y(a)?void(d.value+=a.length):(o.value=!0,void t()))})).catch((e=>{"CanceledError"!==e.name&&"canceled"!==e.message?(o.value=!1,(null==l?void 0:l.aborted)?t():n(e)):t()})).finally((()=>{(null==l?void 0:l.aborted)||(a.value=!1)}))}))}))},y=e=>{const a=new Set(t.value.map((e=>e.id))),o=e.filter((e=>!a.has(e.id)));if(0===o.length)return!1;const n=o.map((e=>{var t;return{...e,contentObj:Un.toJsonObject(e.content),publisherAvatar:x(e.publisherAvatar),tags:(null==(t=e.tag)?void 0:t.split(","))||[]}}));return t.value=[...t.value,...n],!0},x=e=>ja.getResourceURL(e);return{articleList:t,loading:a,noMore:o,cardColSpan:l,articleTiptapEditorMap:r,containerRef:m,scrollContainerRef:p,currentLoadedArticlesCount:d,getCardColor:(e,t)=>{const a=f.value?g:h;return a[t%a.length]},calculatedLoadSize:w,updateColSpan:()=>{const e=window.innerWidth;let t=24,a=1;e>=1680?(t=6,a=4):e>=1260?(t=8,a=3):e>=840?(t=12,a=2):(t=24,a=1),l.value=t,u.value=a},resetList:()=>{t.value=[],o.value=!1,d.value=0,b()},loadArticles:b,handleToggleScope:e=>{const t=e.publishedScope===vl.PERSONAL?vl.PUBLIC:vl.PERSONAL;v.warning({title:"切换发布范围",content:`确定要将文章《${e.title}》切换为${hl[t]}可见吗？`,positiveText:"确定",negativeText:"取消",onPositiveClick:()=>{Fn.togglePublishedScope(e.id).then((a=>{200===a.code?(e.publishedScope=t,va.success(`文章已切换为${hl[t]}可见`)):va.error(a.message||"操作失败")}))}})},handleDeleteArticle:e=>{v.warning({title:"删除文章",content:`确定要删除文章《${e.title}》吗？此操作不可恢复。`,positiveText:"删了",negativeText:"算了",onPositiveClick:()=>{Fn.delete(e.id).then((a=>{if(200===a.code){const a=t.value.findIndex((t=>t.id===e.id));a>-1&&t.value.splice(a,1),va.success("文章已删除")}else va.error(a.message||"删除失败")})).catch((()=>{va.error("删除失败，请稍后再试")}))}})},handleReorderArticles:(e,a,o)=>{const n=t.value.findIndex((t=>t.id===e)),l=t.value.findIndex((e=>e.id===a));if(-1===n||-1===l)return;const[i]=t.value.splice(n,1);let r=l;r="after"===o?n<l?l:l+1:l,t.value.splice(r,0,i),va.success("移动成功！")}}}(o),{isDragging:_,draggedArticle:B,showTrashBin:$,isOverTrashBin:P,dragStyle:U,dragOverCardId:F,dragOverPosition:H,isSingleCardRow:V,startLongPress:O,cancelLongPress:D,isLongPressActive:j}=mr({onDragStart:e=>{},onDelete:T,onReorder:I});s((()=>o.searchCondition),((e,t)=>{Wt.debug("搜索条件变化:",{oldCondition:t,newCondition:e,articleListLength:l.value.length});Object.keys(e).some((a=>(["searchKey","tag"].includes(a)||!!["owner","interaction","favorite"].includes(a))&&e[a]!==t[a]))?(Wt.info("搜索条件发生实质性变化，重置文章列表"),R(),E()):Wt.debug("搜索条件未发生实质性变化，不执行重置")}),{deep:!0}),r((()=>{L(),window.addEventListener("resize",N)}));const N=()=>{L()},q=()=>{E(!0)},Y=(e,t,a)=>{O(e,t,a)},J=e=>{const t=y.value.get(e);Fn.md(e,t)},W=(e,t)=>{y.value.set(e,t)};t({loadArticles:E,resetList:R});const K=()=>{},G=()=>{},X=()=>{j&&D()};return(e,t)=>(m(),f("div",{class:"article-container",ref_key:"containerRef",ref:x,onTouchstart:K,onTouchend:G,onTouchcancel:X},[v(h(xe),{onLoad:q,distance:100,class:"infinite-scroll-container",ref_key:"scrollContainerRef",ref:k},{default:p((()=>[v(h(Re),{gutter:20,style:{width:"100%","box-sizing":"border-box",margin:"0 auto",padding:"0 0.25rem",flex:"1","overflow-y":"auto"}},{default:p((()=>[(m(!0),f(S,null,A(h(l),((e,t)=>(m(),u(h(Ee),{key:e.id,span:h(b)},{default:p((()=>[v(ir,{article:e,index:t,"card-color":h(C)(e.id,t),"is-dragging":h(_),"dragged-article":h(B)||void 0,"drag-over-card-id":h(F)||void 0,"drag-over-position":h(H)||void 0,"is-single-card-row":h(V),"drag-style":h(U),onToggleScope:h(z),onStartLongPress:Y,onCancelLongPress:h(D),onDownload:J,onSetEditor:W},null,8,["article","index","card-color","is-dragging","dragged-article","drag-over-card-id","drag-over-position","is-single-card-row","drag-style","onToggleScope","onCancelLongPress"])])),_:2},1032,["span"])))),128))])),_:1}),g("div",pr,[h(d)?(m(),u(h(ke),{key:0,class:"display-flex"})):M("",!0),h(w)?(m(),u(h(Ce),{key:1,description:"没有更多文章了..."})):M("",!0)])])),_:1},512),v(sr,{visible:h($),"is-active":h(P)},null,8,["visible","is-active"])],544))}}),[["__scopeId","data-v-133450a3"]]);const hr=l({__name:"Danmaku",props:{danmus:{type:Array,required:!0,default:()=>[]},channels:{type:Number,default:0},autoplay:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},useSlot:{type:Boolean,default:!1},debounce:{type:Number,default:100},speeds:{type:Number,default:200},randomChannel:{type:Boolean,default:!1},fontSize:{type:Number,default:18},top:{type:Number,default:4},right:{type:Number,default:0},isSuspend:{type:Boolean,default:!1},extraStyle:{type:String,default:""}},emits:["list-end","play-end","dm-over","dm-out","update:danmus"],setup(e,{expose:t,emit:a}){const o=e,l=a,s=n(document.createElement("div")),c=n(document.createElement("div")),d=n(0),u=n(0),p=n(0),v=n(0),h=n(48),w=n(0),y=n(!1),x=n(!1),k=E({});const C=function(e,t,a="modelValue"){return i({get:()=>e[a],set:e=>{t(`update:${a}`,e)}})}(o,l,"danmus"),M=E({channels:i((()=>o.channels||v.value)),autoplay:i((()=>o.autoplay)),loop:i((()=>o.loop)),useSlot:i((()=>o.useSlot)),debounce:i((()=>o.debounce)),randomChannel:i((()=>o.randomChannel))}),S=E({height:i((()=>h.value)),fontSize:i((()=>o.fontSize)),speeds:i((()=>{const e=o.speeds,t=d.value;if(t<320)return.25*e;if(t<960)return.5*e;if(t>=1920)return e;return e*(.5+(t-960)/960*.5)})),top:i((()=>o.top)),right:i((()=>o.right))}),{initCore:A,resize:z}=function(e,t,a,o,n){function l(){if(a.value=e.value.offsetWidth,o.value=e.value.offsetHeight,0===a.value||0===o.value)throw new Error("获取不到容器宽高")}return{initCore:l,resize:function(){l();const e=t.value.getElementsByClassName("dm");for(let t=0;t<e.length;t++){const o=e[t];o.style.setProperty("--dm-scroll-width",`-${a.value+o.offsetWidth}px`),o.style.left=`${a.value}px`,o.style.animationDuration=a.value/n.speeds+"s"}}}}(s,c,d,u,S),{draw:I,insert:_,add:B,push:$}=function(e,t,a,o,n,l,i,r,s,c,d,u){function m(l){try{if(l&&l.content){let e=l.content;if("string"==typeof e)try{e=JSON.parse(e)}catch(m){Wt.warn("弹幕JSON解析失败，使用原始文本:",m)}return g({id:l.commentId||Date.now().toString(),content:e})}const h=r.loop?t.value%e.value.length:t.value,f=l||e.value[h];let w=document.createElement("div");r.useSlot&&u?w=p(f,h).$el:(w.innerHTML=f,w.setAttribute("style",d),w.style.fontSize=`${i.fontSize}px`,w.style.lineHeight="3rem"),w.classList.add("dm"),n.value.appendChild(w),w.style.opacity="0";const b=w.offsetHeight,y=w.offsetWidth;i.height||(i.height=48),r.channels||(s.value=Math.floor(o.value/(i.height+i.top)));let x=v(w);if(x>=0){const l=i.height,s=()=>{x*(l+i.top)+b>=o.value&&(x--,s())};s(),Wt.debug("danmaku height top: ",l,i.top),w.classList.add("move"),w.dataset.index=`${h}`,w.dataset.channel=x.toString(),w.style.opacity="1";const d=x*(l+i.top)+"px";w.style.top=d,w.style.left=`${a.value}px`,w.style.animationDuration=a.value/i.speeds+"s",w.addEventListener("animationend",(()=>{Number(w.dataset.index)!==e.value.length-1||r.loop||c("play-end",w.dataset.index),n.value&&n.value.removeChild(w)})),t.value++,w.style.width=y+i.right+"px",w.style.setProperty("--dm-scroll-width",`-${a.value+y}px`)}else n.value.removeChild(w)}catch(h){Wt.error("添加弹幕时发生错误:",h)}}function p(e,t){return O({render:()=>R("div",{},[u&&u({danmu:e,index:t})])}).mount(document.createElement("div"))}function v(e){let t=[...Array(r.channels).keys()];r.randomChannel&&(t=t.sort((()=>.5-Math.random())));for(const a of t){const t=l[a];if(!t||!t.length)return l[a]=[e],e.addEventListener("animationend",(()=>l[a].splice(0,1))),a%r.channels;for(let o=0;o<t.length;o++){const n=h(t[o])-10;if(n<=.75*(e.offsetWidth-t[o].offsetWidth)||n<=0)break;if(o===t.length-1)return l[a].push(e),e.addEventListener("animationend",(()=>l[a].splice(0,1))),a%r.channels}}return-1}function h(e){const t=e.offsetWidth||parseInt(e.style.width),a=e.getBoundingClientRect().right||n.value.getBoundingClientRect().right+t;return n.value.getBoundingClientRect().right-a}function g(a){if(t.value===e.value.length)return e.value.push(a),e.value.length-1;{const o=t.value%e.value.length;return e.value.splice(o,0,a),o+1}}return{draw:function(){if(e.value.length)if(t.value>e.value.length-1){const e=n.value.children.length;r.loop&&(e<t.value&&(c("list-end"),t.value=0),m())}else m()},insert:m,add:g,push:function(t){return e.value.push(t),e.value.length-1},getChannelIndex:v,getDanRight:h,getSlotComponent:p}}(C,w,d,u,c,k,S,M,v,l,o.extraStyle,D().dm),{play:P,clear:U,stop:F,pause:H,show:V,hide:j,getPlayState:N}=function(e,t,a,o,n,l,i,r,s){function c(){clearInterval(i.value),i.value=0}function d(){c(),a.value=0}return{play:function(){if(o.value=!1,!i.value){const e=s instanceof Object?s.value:s;i.value=window.setInterval((()=>r()),e)}},clearTimer:c,clear:d,stop:function(){Object.assign(l,{}),e.value.innerHTML="",o.value=!0,n.value=!1,d()},pause:function(){o.value=!0},show:function(){n.value=!1},hide:function(){n.value=!0},getPlayState:function(){return!o.value}}}(c,0,w,x,y,k,p,I,M.debounce),{initSuspendEvents:q}=function(e,t){return{initSuspendEvents:function(){let a=[];e.value.addEventListener("mouseover",(e=>{let o=e.target;o.className.includes("dm")||(o=o.closest(".dm")||o),o.className.includes("dm")&&(a.includes(o)||(t("dm-over",{el:o}),o.classList.add("pause"),a.push(o)))})),e.value.addEventListener("mouseout",(e=>{let o=e.target;o.className.includes("dm")||(o=o.closest(".dm")||o),o.className.includes("dm")&&(t("dm-out",{el:o}),o.classList.remove("pause"),a.forEach((e=>{e.classList.remove("pause")})),a=[])}))}}}(c,l);function Y(){A(),o.isSuspend&&q(),M.autoplay&&P()}return r((()=>{Y()})),L((()=>{U()})),t({container:s,dmContainer:c,hidden:y,paused:x,danmuList:C,getPlayState:N,resize:z,play:P,pause:H,stop:F,show:V,hide:j,reset:function(){h.value=0,Y()},add:B,push:$,insert:_}),(e,t)=>(m(),f("div",{ref_key:"container",ref:s,class:"vue-danmaku"},[g("div",{ref_key:"dmContainer",ref:c,class:b(["danmus",{show:!y.value},{paused:x.value}])},null,2),T(e.$slots,"default")],512))}});function gr(e){var t,a,o,n;if(!e)return"";let l="";try{if("text"===e.type&&e.text){let o=e.text;if(e.marks&&e.marks.length>0)for(const n of e.marks)"bold"===n.type?o=`<strong>${o}</strong>`:"italic"===n.type?o=`<em>${o}</em>`:"underline"===n.type?o=`<u>${o}</u>`:"strike"===n.type?o=`<s>${o}</s>`:"code"===n.type?o=`<code>${o}</code>`:"link"===n.type&&(null==(t=n.attrs)?void 0:t.href)?o=`<a href="${n.attrs.href}" target="_blank">${o}</a>`:"textStyle"===n.type&&(null==(a=n.attrs)?void 0:a.color)&&(o=`<span style="color: ${n.attrs.color}">${o}</span>`);l+=o}else if("mention"===e.type&&(null==(o=e.attrs)?void 0:o.label))try{const t=e.attrs.avatar?ja.getResourceURL(e.attrs.avatar):"",a=['data-type="mention"',`data-id="${e.attrs.id||""}"`,`data-label="${e.attrs.label}"`,`data-avatar="${e.attrs.avatar||""}"`].join(" "),o=`<span class="mention-name">@${e.attrs.label}</span>`;l+=`<span class="mention" ${a} contenteditable="false">${o}${t?`<img src="${t}" alt="${e.attrs.label}" class="mention-avatar" loading="eager" decoding="async" referrerpolicy="no-referrer" onerror="this.style.display='none';" />`:""}</span>`}catch(i){l+=`<span class="mention" data-type="mention" data-label="${e.attrs.label}"><span class="mention-name">@${e.attrs.label}</span></span>`}else if("image"===e.type&&(null==(n=e.attrs)?void 0:n.src))try{const t=e.attrs.src;if(t.startsWith("http://")||t.startsWith("https://"))l+=`<img src="${t}" class="danmaku-image"\n                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"\n                     data-original-src="${t}"\n                     data-is-original="true"\n                     onerror="this.replaceWith(document.createTextNode('[图片]'));" />`;else{let e,a;if(t.includes(Na))e=ja.getResourceURL(t),a=t;else{const o=t.split("/"),n=o.pop();a=`${o.join("/")}${Na}/${n}`,e=ja.getResourceURL(a)}const o=ja.getResourceURL(t.replace(Na,""));l+=`<img src="${e}" class="danmaku-image"\n                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"\n                     data-original-src="${t}"\n                     data-thumbnail-src="${a}"\n                     data-is-original="false"\n                     onerror="if (!this.dataset.tried) { this.dataset.tried = 'true'; this.src = '${o}'; this.dataset.isOriginal = 'true'; }\n                     else { this.replaceWith(document.createTextNode('[图片]')); }" />`}}catch(i){l+='<span class="image-placeholder">[图片]</span>'}else if("hardBreak"===e.type)l+=" ";else if(e.type&&["paragraph","heading","blockquote"].includes(e.type)){if(e.content&&Array.isArray(e.content)){l+=e.content.map((e=>gr(e))).join("")+" "}}else e.content&&Array.isArray(e.content)&&(l+=e.content.map((e=>gr(e))).join(""))}catch(i){if("text"===e.type&&e.text)return e.text;if("image"===e.type)return"[图片]"}return l}function fr(e){try{return e&&"object"==typeof e?e.content&&Array.isArray(e.content)&&0!==e.content.length?gr(e):"<span>[空内容]</span>":"<span>[无法显示内容]</span>"}catch(t){return"<span>[渲染失败]</span>"}}const wr=["innerHTML"],br=l({__name:"DanmakuRenderer",props:{content:{type:Object,required:!0}},emits:["image-preview-open","image-preview-close"],setup(e,{emit:t}){const a=e,o=t,{content:l}=j(a),s=n(null),c=i((()=>fr(l.value)));return r((()=>{const e=e=>{const t=e.target;if("IMG"===t.tagName&&t.classList.contains("danmaku-image")){const a=t,n=a.dataset.originalSrc;n&&(e.stopPropagation(),e.preventDefault(),function(e,t,a){null==event||event.preventDefault(),null==event||event.stopPropagation(),a("image-preview-open");const o=document.createElement("div");o.classList.add("modal-overlay");const n=document.createElement("img");n.alt="图片预览",o.appendChild(n),document.body.appendChild(o),o.classList.add("modal-overlay-active");const l="true"===e.dataset.isOriginal,i=e.dataset.thumbnailSrc||t,r=t.startsWith("http://")||t.startsWith("https://");if(r||!i.includes(Na)||l)n.src=e.src,n.style.opacity="1";else{n.src=e.src,n.style.opacity="0.5";const a=document.createElement("div");a.classList.add("loading-spinner"),o.appendChild(a);const l=new Image,i=Date.now(),r=500;l.onload=()=>{const e=Date.now()-i,t=()=>{a.style.display="none",n.src=l.src,n.style.opacity="1",n.dataset.originalFullUrl=l.src};e<r?setTimeout(t,r-e):t()},l.src=ja.getResourceURL(t.replace(Na,""))}const s=()=>{o.classList.remove("modal-overlay-active"),o.addEventListener("transitionend",(()=>{if(!o.classList.contains("modal-overlay-active")){if(!r&&n.dataset.originalFullUrl&&i.includes(Na)&&!l){e.src=n.dataset.originalFullUrl,e.dataset.isOriginal="true";const a=t.replace(Na,"");e.dataset.originalSrc=a}document.body.removeChild(o),document.removeEventListener("keydown",c),a("image-preview-close")}}),{once:!0})};o.addEventListener("click",s,{once:!0});const c=e=>{"Escape"===e.key&&s()};document.addEventListener("keydown",c)}(a,n,o))}};s.value&&(s.value.addEventListener("click",e),s.value._danmakuImageClickHandler=e)})),_((()=>{s.value&&s.value._danmakuImageClickHandler&&(s.value.removeEventListener("click",s.value._danmakuImageClickHandler),delete s.value._danmakuImageClickHandler)})),(e,t)=>(m(),f("div",{class:"danmaku-renderer",ref_key:"rendererRef",ref:s,innerHTML:c.value},null,8,wr))}}),yr={class:"comment-container"},xr=["onDblclick"],kr={class:"comment-danmaku-publisher"},Cr={class:"comment-danmaku-content"},Lr=Sa(l({__name:"CommentDanmaku",props:{searchCondition:{type:Object,required:!0},loop:{type:Boolean,default:!1},pause:{type:Boolean,default:!1}},emits:["search","update:loop","update:pause"],setup(e,{expose:t,emit:a}){const o=e,l=n([]),i=n(),c=E({isSuspend:!0,useSlot:!0,speeds:160,debounce:200,top:10,right:0,channels:0,randomChannel:!0,fontSize:14,loop:!1,pause:!1,autoplay:!1});s((()=>o.loop),(e=>{c.loop=e})),s((()=>o.pause),(e=>{c.pause=e,i.value&&(e?i.value.pause():i.value.play())}));const d=e=>ja.getResourceURL(e),u=e=>{const t=Kt.parse(e);t&&ma("comment-received:"+t.commentId,(()=>{if(!l.value.some((e=>e.id===t.commentId)))if(t.commentId&&t.articleId&&t.publisher)try{const e={id:t.commentId,articleId:t.articleId,publisher:t.publisher,publisherAvatar:d(t.publisherAvatar||""),content:t.content||"",contentObj:t.content?Un.toJsonObject(t.content):{type:"doc",content:[{type:"paragraph",content:[]}]}};i.value&&i.value.insert(e)}catch(e){Wt.error("处理弹幕消息时出错:",e)}else Wt.warn("消息缺少必要字段:",t)}))},b=()=>{Hl.subscribe(Vl,u)},y=()=>{Hl.unsubscribe(Vl)},x=()=>{i.value&&i.value.pause()},k=()=>{i.value&&!c.pause&&i.value.play()};return t({clearDanmaku:()=>{l.value=[],i.value&&i.value.reset()},addCommentList:e=>{const t=new Set(l.value.map((e=>e.id))),a=e.filter((e=>!t.has(e.id)));if(0!==a.length)try{const e=a.map((e=>({...e,publisherAvatar:d(e.publisherAvatar||""),content:e.content||"",contentObj:e.content?Un.toJsonObject(e.content):{type:"doc",content:[{type:"paragraph",content:[]}]}}))),t=10;for(let a=0;a<e.length;a+=t){const o=e.slice(a,a+t);setTimeout((()=>{l.value.push(...o)}),50*a)}l.value.length>200&&(l.value=l.value.slice(-200)),i.value&&i.value.play()}catch(o){Wt.error("处理评论列表时出错:",o)}},subscribeComment:b,unsubscribeComment:y,resize:()=>{i.value&&i.value.resize()},play:()=>{i.value&&i.value.play()},pause:()=>{i.value&&i.value.pause()}}),r((()=>{b()})),_((()=>{y()})),(e,t)=>(m(),f("div",yr,[v(hr,N({ref_key:"danmakuRef",ref:i,class:"comment-danmaku",danmus:l.value,"onUpdate:danmus":t[0]||(t[0]=e=>l.value=e)},c),{dm:p((({danmu:e})=>[g("span",{class:"comment-danmaku-item cursor-pointer",onDblclick:t=>(e=>{const t=ss.resolve({name:"Article",params:{articleId:e.articleId,commentId:e.id}}),a=window.open(t.href,"_blank");a&&a.focus()})(e)},[g("span",kr,[v(h(te),{round:"",size:28,src:e.publisherAvatar,"object-fit":"cover",lazy:!0},null,8,["src"]),g("span",null,w(e.publisher)+": ",1)]),g("span",Cr,[v(br,{content:e.contentObj,onImagePreviewOpen:x,onImagePreviewClose:k},null,8,["content"])])],40,xr)])),_:1},16,["danmus"])]))}}),[["__scopeId","data-v-e4604f80"]]),Mr=Sa(l({__name:"CreateButton",emits:["click"],setup(e,{emit:t}){const a=t,o=n(!1);n(!1);const l=n(null),i=n(null),s=()=>{a("click")},c=()=>{o.value||(i.value&&(clearTimeout(i.value),i.value=null),o.value=!0,l.value=window.setTimeout((()=>{o.value=!1,p()}),1500))},d=()=>{o.value||p()},p=()=>{i.value&&(clearTimeout(i.value),i.value=null);const e=5e3+1e4*Math.random();i.value=window.setTimeout((()=>{v()}),e)},v=()=>{o.value?p():(o.value=!0,l.value=window.setTimeout((()=>{o.value=!1,p()}),1500))};return r((()=>{p()})),_((()=>{l.value&&(clearTimeout(l.value),l.value=null),i.value&&(clearTimeout(i.value),i.value=null)})),(e,t)=>(m(),u(h(rn),{size:36,color:"var(--blue)",onClick:s,class:b(["cursor-pointer create-button",{"is-rotating":o.value}]),ref:"createButtonRef",onMouseenter:c,onMouseleave:d},null,8,["class"]))}}),[["__scopeId","data-v-ed9878be"]]),Sr={class:"search-container"},Ar=Sa(l({__name:"SearchBar",props:{placeholder:{type:String,default:"感兴趣的内容"},modelValue:{type:Object,required:!0}},emits:["update:modelValue","search"],setup(e,{emit:t}){const a=e,o=t,l=n({...a.modelValue});s((()=>a.modelValue),(e=>{l.value={...e},d()}),{deep:!0});const i=()=>{o("update:modelValue",{...l.value})},r=n(""),c=[{name:"owner",label:"我的"},{name:"interaction",label:"互动"},{name:"favorite",label:"收藏"}],d=()=>{r.value="";for(const e of c)if(l.value[e.name]){r.value=e.name;break}};d();const u=()=>{i(),g(),o("search")},g=()=>{Gt.set(Dt,l.value)};return(t,a)=>(m(),f("div",Sr,[v(h(le),{style:{"border-radius":"0.5rem"},value:l.value.searchKey,"onUpdate:value":a[0]||(a[0]=e=>l.value.searchKey=e),size:"large",placeholder:e.placeholder,clearable:"",onInput:u},{suffix:p((()=>[v(h(ln),{onClick:u,class:"cursor-pointer",size:20})])),_:1},8,["value","placeholder"]),v(h(ze),{value:r.value,"justify-content":"space-evenly"},{default:p((()=>[(m(),f(S,null,A(c,((e,t)=>v(h(Te),{key:t,name:e.name,label:e.label,onClick:t=>{return a=e.name,r.value===a?r.value="":r.value=a,l.value[a]=!l.value[a],Object.keys(l.value).forEach((e=>{e!==a&&"searchKey"!==e&&"tag"!==e&&(l.value[e]=!1)})),i(),g(),void o("search");var a}},null,8,["name","label","onClick"]))),64))])),_:1},8,["value"])]))}}),[["__scopeId","data-v-771c8282"]]),Rr={key:0,class:"tag-bar-container"},Er=Sa(l({__name:"TagBar",props:{modelValue:{type:String,default:""}},emits:["update:modelValue","tagSelected"],setup(e,{expose:t,emit:a}){const o=e,l=a,c=n([]),d=n(o.modelValue),v=n([]),g=i((()=>c.value.find((e=>e.name===d.value))));s((()=>o.modelValue),(e=>{Wt.debug("外部标签值变化:",{oldValue:d.value,newValue:e,hotTags:c.value}),d.value=e}));return r((()=>{(async()=>{try{const e=await Fn.getHotTags(10);e.data&&(c.value=e.data,Wt.debug("热门标签加载成功:",c.value))}catch(e){Wt.error("加载热门标签失败:",e)}})()})),t({tagSelectionHistory:v,currentTagInfo:g,hotTags:c}),(e,t)=>c.value.length>0?(m(),f("div",Rr,[(m(!0),f(S,null,A(c.value,(e=>(m(),u(h(Me),{key:e.name,type:d.value===e.name?"primary":"default",class:"hot-tag",bordered:!1,onClick:t=>(e=>{const t=d.value;d.value===e?d.value="":d.value=e,v.value.push({timestamp:Date.now(),from:t,to:d.value}),v.value.length>10&&v.value.shift(),Wt.debug("标签选择变化:",{prevTag:t,currentTag:d.value,hotTags:c.value}),l("update:modelValue",d.value),l("tagSelected",d.value)})(e.name)},{default:p((()=>[z(w(e.name)+" ("+w(e.count)+") ",1)])),_:2},1032,["type","onClick"])))),128))])):M("",!0)}}),[["__scopeId","data-v-a6b87578"]]),zr={class:"toggle-button-container"},Tr={class:"toggle-card-front"},Ir={class:"toggle-card-back"},_r=Sa(l({__name:"ToggleButton",props:{value:{type:Boolean,required:!0}},emits:["update:value","toggle"],setup(e,{emit:t}){const a=e,o=t,l=n(!1),s=n(null),c=n(null),d=i((()=>a.value?"评":"文")),u=i((()=>a.value?"文":"评")),y=()=>{o("update:value",!a.value),o("toggle"),s.value&&(clearTimeout(s.value),s.value=null),l.value=!0,s.value=window.setTimeout((()=>{l.value=!1,C()}),800)},x=()=>{l.value||(c.value&&(clearTimeout(c.value),c.value=null),l.value=!0,s.value=window.setTimeout((()=>{l.value=!1,C()}),800))},k=()=>{l.value||C()},C=()=>{c.value&&(clearTimeout(c.value),c.value=null);const e=8e3+12e3*Math.random();c.value=window.setTimeout((()=>{l.value?C():(l.value=!0,s.value=window.setTimeout((()=>{l.value=!1,C()}),800))}),e)};return r((()=>{C()})),_((()=>{s.value&&(clearTimeout(s.value),s.value=null),c.value&&(clearTimeout(c.value),c.value=null)})),(e,t)=>(m(),f("div",zr,[g("div",{class:b(["toggle-card",{"is-flipping":l.value}]),onMouseenter:x,onMouseleave:k,onClick:y,ref:"toggleButtonRef"},[g("div",Tr,[v(h(pe),{type:"info",class:"cursor-pointer",size:32},{default:p((()=>[z(w(d.value),1)])),_:1})]),g("div",Ir,[v(h(pe),{type:"info",class:"cursor-pointer",size:32},{default:p((()=>[z(w(u.value),1)])),_:1})])],34)]))}}),[["__scopeId","data-v-5e65680c"]]);function Br(){const e=Ie(),t=n(!1),a=n(!1);let o=null;const l=n([]),r=n({searchKey:"",owner:!1,interaction:!1,favorite:!1,tag:""}),s=()=>{Gt.set(Dt,r.value),Wt.debug("保存搜索条件:",r.value)},c=i((()=>Object.entries(r.value).some((([e,t])=>"tag"===e?!!t:"boolean"==typeof t?t:"searchKey"===e&&!!t)))),d=(n,i,s,d=!1)=>{a.value?Wt.warn("搜索被阻止：正在进行其他搜索"):(o&&(o.abort(),o=null),o=new AbortController,l.value.push({timestamp:Date.now(),condition:{...r.value},type:n?"article":"comment"}),l.value.length>10&&l.value.shift(),Wt.debug("触发搜索:",{isCardVisible:n,condition:r.value,loadMore:d}),ma("unified_search",(()=>{a.value=!0;try{n?(async(n,l=!1)=>{if(!l&&n.value){a.value=!0,t.value=!0;try{await n.value.resetList()}catch(i){"CanceledError"!==i.name&&"canceled"!==i.message&&e.error("加载文章失败，请稍后重试")}finally{t.value=!1,a.value=!1,o=null}}})(i,d):(async n=>{if(c.value){a.value=!0,t.value=!0;try{const e=await $i.search(r.value,null==o?void 0:o.signal);n.value&&n.value.addCommentList(e.data)}catch(l){"CanceledError"!==l.name&&"canceled"!==l.message&&e.error("加载评论失败，请稍后重试")}finally{t.value=!1,a.value=!1,o=null}}else n.value&&(n.value.danmakuLoop=!1)})(s)}catch(l){Wt.error("搜索过程中发生错误:",l),e.error("搜索失败，请稍后重试")}finally{setTimeout((()=>{a.value=!1}),500)}}),200))};return{isLoading:t,isSearching:a,searchCondition:r,hasSearchCondition:c,searchHistory:l,getSearchPlaceholder:e=>e?"感兴趣的文章":"有意思的评论",loadSearchCondition:()=>{const e=Gt.get(Dt);e&&(r.value=e,Wt.debug("加载搜索条件:",e))},saveSearchCondition:s,search:d,handleTagSelected:(e,t,a,o)=>{r.value.tag=e,Wt.debug("标签选择:",{tagName:e,isCardVisible:t,currentCondition:r.value}),s(),d(t,a,o)},cleanup:()=>{o&&(o.abort(),o=null),l.value=[],Wt.debug("清理搜索状态")}}}const $r={beforeEnter:e=>{e instanceof HTMLElement&&(e.style.opacity="0",e.style.transform="translateY(2px)")},enter:(e,t)=>{e instanceof HTMLElement?(e.offsetHeight,e.style.transition="opacity 0.2s ease, transform 0.2s ease",e.style.opacity="1",e.style.transform="translateY(0)",setTimeout(t,200)):t()},leave:(e,t)=>{e instanceof HTMLElement?(e.style.transition="opacity 0.2s ease, transform 0.2s ease",e.style.opacity="0",e.style.transform="translateY(-2px)",setTimeout(t,200)):t()}},Pr={class:"common-layout"},Ur={class:"common-layout-top"},Fr={class:"left-controls-container"},Hr={class:"control-item"},Vr={class:"control-item"},Or={class:"middle-controls-container"},Dr={class:"tag-bar-wrapper"},jr={class:"common-layout-content"},Nr=Sa(l({__name:"Home",setup(e){const t=q((()=>function(e,t){let a=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),n=(null==o?void 0:o.nonce)||(null==o?void 0:o.getAttribute("nonce"));a=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in ar)return;ar[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${a}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script"),o.crossOrigin="",o.href=e,n&&o.setAttribute("nonce",n),document.head.appendChild(o),t?new Promise(((t,a)=>{o.addEventListener("load",t),o.addEventListener("error",(()=>a(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function o(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then((t=>{for(const e of t||[])"rejected"===e.status&&o(e.reason);return e().catch(o)}))}((()=>import("./ArticleModal-DvIGHa1r.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56])))),a=Br(),o=function(){const e=n(!1),t=n(!1);return{danmakuLoop:e,danmakuPause:t,handleDanmakuPauseChange:(e,t)=>{t.value&&(e?t.value.pause():t.value.play())},resetDanmakuState:()=>{e.value=!1,t.value=!1},handleDanmakuSubscription:(e,t)=>{e.value&&(t?e.value.subscribeComment():(e.value.unsubscribeComment(),e.value.clearDanmaku()))},handleDanmakuResize:(e,t)=>{!t&&e.value&&e.value.resize()}}}(),l=function(){const e=n(!0),t=n(0),a=n(),o=n(),l=n();return{isCardVisible:e,onlineCount:t,articleModalRef:a,articleListRef:o,commentDanmakuRef:l,initializeState:()=>{Hl.connect();const t=Gt.get(Ot);null!=t&&(e.value=Boolean(t))},toggleCardVisibility:(t,a,o)=>{Gt.set(Ot,Boolean(e.value)),e.value?(o(l,!1),t(),setTimeout((()=>{a()}),100)):(o(l,!0),setTimeout((()=>{a()}),100))},resetArticleList:()=>{o.value&&o.value.resetList()},openCreateArticleDialog:()=>{a.value.openCreateArticleDialog()},createResizeCallback:t=>()=>{t(l,e.value)},cleanup:e=>{window.removeEventListener("resize",e)}}}(),i=l.createResizeCallback(o.handleDanmakuResize);r((async()=>{l.initializeState(),a.loadSearchCondition(),await c(),setTimeout((()=>{s()}),50),window.addEventListener("resize",i)})),_((()=>{a.cleanup(),l.cleanup(i)}));const s=(e=!1)=>{a.search(l.isCardVisible.value,l.articleListRef,l.commentDanmakuRef,e)},d=()=>{l.toggleCardVisibility(l.resetArticleList,s,o.handleDanmakuSubscription)},w=()=>{l.resetArticleList(),s()};return(e,n)=>(m(),f("div",Pr,[g("div",Ur,[v(V,{name:"fade-slide"},{default:p((()=>[P(g("div",Fr,[g("div",Hr,[v(h(pe),{type:"info",class:"control-label"},{default:p((()=>n[7]||(n[7]=[z("循环：")]))),_:1}),v(h(_e),{value:h(o).danmakuLoop.value,"onUpdate:value":n[0]||(n[0]=e=>h(o).danmakuLoop.value=e),size:"small"},null,8,["value"])]),g("div",Vr,[v(h(pe),{type:"info",class:"control-label"},{default:p((()=>n[8]||(n[8]=[z("暂停：")]))),_:1}),v(h(_e),{value:h(o).danmakuPause.value,"onUpdate:value":[n[1]||(n[1]=e=>h(o).danmakuPause.value=e),n[2]||(n[2]=e=>h(o).handleDanmakuPauseChange(e,h(l).commentDanmakuRef))],size:"small"},null,8,["value"])])],512),[[U,!h(l).isCardVisible.value]])])),_:1}),g("div",Or,[v(_r,{value:h(l).isCardVisible.value,"onUpdate:value":n[3]||(n[3]=e=>h(l).isCardVisible.value=e),onToggle:d},null,8,["value"]),v(Ar,{modelValue:h(a).searchCondition.value,"onUpdate:modelValue":n[4]||(n[4]=e=>h(a).searchCondition.value=e),placeholder:h(a).getSearchPlaceholder(h(l).isCardVisible.value),onSearch:s},null,8,["modelValue","placeholder"]),v(Mr,{onClick:h(l).openCreateArticleDialog},null,8,["onClick"])]),v(h(t),{ref:e=>h(l).articleModalRef.value=e,onSuccess:w},null,512),v(hi)]),g("div",Dr,[v(Er,{modelValue:h(a).searchCondition.value.tag,"onUpdate:modelValue":n[5]||(n[5]=e=>h(a).searchCondition.value.tag=e),onTagSelected:n[6]||(n[6]=e=>h(a).handleTagSelected(e,h(l).isCardVisible.value,h(l).articleListRef,h(l).commentDanmakuRef))},null,8,["modelValue"])]),g("div",jr,[v(V,{onBeforeEnter:h($r).beforeEnter,onEnter:h($r).enter,onLeave:h($r).leave,duration:{enter:200,leave:200},mode:"out-in"},{default:p((()=>[h(l).isCardVisible.value?(m(),u(vr,{key:"article","search-condition":h(a).searchCondition.value,ref:e=>h(l).articleListRef.value=e,onReset:h(l).resetArticleList},null,8,["search-condition","onReset"])):(m(),u(Lr,{key:"comment","search-condition":h(a).searchCondition.value,loop:h(o).danmakuLoop.value,pause:h(o).danmakuPause.value,ref:e=>h(l).commentDanmakuRef.value=e},null,8,["search-condition","loop","pause"]))])),_:1},8,["onBeforeEnter","onEnter","onLeave"])])]))}}),[["__scopeId","data-v-9cae50d3"]]),qr="/assets/logo-CmFy1pvx.png";const Yr={class:"static-elements"},Jr={key:0,class:"clouds-container"},Wr={key:1,class:"stars-container"},Kr={class:"dynamic-elements"},Gr={key:0,class:"dandelions-container"},Xr={key:1,class:"fireflies-container"},Qr=Sa(l({__name:"BackgroundAnimation",props:{particleCount:{type:Number,default:30,description:"动态元素的数量（蒲公英/萤火虫）"},cloudCount:{type:Number,default:9,description:"云朵数量"},enableClouds:{type:Boolean,default:!0,description:"是否显示云朵"},enableDandelions:{type:Boolean,default:!0,description:"是否显示蒲公英（浅色模式）"},enableStars:{type:Boolean,default:!0,description:"是否显示星星（暗色模式）"},enableFireflies:{type:Boolean,default:!0,description:"是否显示萤火虫（暗色模式）"},customLightGradient:{type:String,default:"",description:"自定义浅色主题背景渐变"},customDarkGradient:{type:String,default:"",description:"自定义暗色主题背景渐变"},zIndex:{type:Number,default:0,description:"背景层级（z-index）"}},emits:["theme-change"],setup(e,{expose:t,emit:a}){const o=e,n=a,{isDarkTheme:l,backgroundStyle:c}=function(e){const t=i((()=>ea.value===Qt.DARK)),a=i((()=>t.value?{background:(null==e?void 0:e.customDarkGradient)||"linear-gradient(to top, #0a0a0f, #121218 60%, #1c1c26 100%)",zIndex:(null==e?void 0:e.zIndex)||0}:{background:(null==e?void 0:e.customLightGradient)||"linear-gradient(to top, var(--creamy-white-3), var(--creamy-white-2) 70%, rgba(232, 240, 242, 0.8) 100%)",zIndex:(null==e?void 0:e.zIndex)||0}));return{isDarkTheme:t,backgroundStyle:a}}({customLightGradient:o.customLightGradient,customDarkGradient:o.customDarkGradient,zIndex:o.zIndex}),{getCloudStyle:d,generateCloudPseudoElementsCSS:u}={getCloudStyle:e=>{const t=e%5;let a,o,n,l,i,r,s,c,d;0===t?(a=Math.floor(150*Math.random())+350,o=Math.floor(100*Math.random())+200,n=15,l=.65,i=[{top:"25%",left:"-10%",width:"60%",height:"60%",borderRadius:"70% 60% 65% 75%"},{top:"10%",left:"30%",width:"70%",height:"70%",borderRadius:"65% 75% 60% 70%"},{top:"35%",left:"75%",width:"45%",height:"45%",borderRadius:"65% 55% 70% 60%"},{top:"60%",left:"25%",width:"55%",height:"55%",borderRadius:"70% 65% 75% 60%"},{top:"45%",left:"52%",width:"48%",height:"48%",borderRadius:"60% 75% 65% 70%"}]):1===t?(a=Math.floor(200*Math.random())+400,o=Math.floor(80*Math.random())+140,n=18,l=.6,i=[{top:"30%",left:"5%",width:"50%",height:"55%",borderRadius:"80% 70% 75% 65%"},{top:"20%",left:"40%",width:"60%",height:"70%",borderRadius:"75% 80% 65% 70%"},{top:"35%",left:"60%",width:"40%",height:"60%",borderRadius:"70% 65% 80% 75%"},{top:"25%",left:"80%",width:"35%",height:"65%",borderRadius:"65% 75% 70% 80%"}]):2===t?(a=Math.floor(120*Math.random())+280,o=Math.floor(90*Math.random())+160,n=14,l=.7,i=[{top:"20%",left:"10%",width:"55%",height:"55%",borderRadius:"65% 70% 60% 75%"},{top:"15%",left:"45%",width:"65%",height:"65%",borderRadius:"75% 65% 70% 60%"},{top:"50%",left:"25%",width:"50%",height:"50%",borderRadius:"60% 75% 65% 70%"}]):3===t?(a=Math.floor(140*Math.random())+300,o=Math.floor(120*Math.random())+180,n=16,l=.63,i=[{top:"10%",left:"5%",width:"40%",height:"40%",borderRadius:"75% 65% 70% 60%"},{top:"5%",left:"35%",width:"45%",height:"45%",borderRadius:"70% 60% 75% 65%"},{top:"15%",left:"70%",width:"35%",height:"35%",borderRadius:"65% 70% 60% 75%"},{top:"50%",left:"10%",width:"38%",height:"38%",borderRadius:"70% 75% 65% 60%"},{top:"45%",left:"40%",width:"42%",height:"42%",borderRadius:"65% 60% 75% 70%"},{top:"40%",left:"75%",width:"30%",height:"30%",borderRadius:"75% 65% 60% 70%"}]):(a=Math.floor(180*Math.random())+320,o=Math.floor(70*Math.random())+130,n=13,l=.66,i=[{top:"25%",left:"0%",width:"45%",height:"45%",borderRadius:"65% 70% 75% 60%"},{top:"20%",left:"35%",width:"50%",height:"50%",borderRadius:"75% 65% 60% 70%"},{top:"30%",left:"65%",width:"40%",height:"40%",borderRadius:"70% 60% 75% 65%"}]),0===t?(c=40,d=65):1===t?(c=10,d=30):2===t?(c=25,d=50):3===t?(c=35,d=60):(c=5,d=25);const u=a/window.innerWidth*100;let m,p;return r=Math.random()*(100-u),s=c+Math.random()*(d-c),m=0===t||3===t?8*Math.random()-4:1===t?2*Math.random()-1:4*Math.random()-2,p=4===t||1===t?6:0===t||3===t?5:4,{width:`${a}px`,height:`${o}px`,left:`${r}%`,top:`${s}%`,opacity:l,filter:`blur(${n}px)`,transform:`rotate(${m}deg)`,zIndex:p,"--cloud-type":t,"--pseudo-elements":JSON.stringify(i),border:"none",outline:"none",boxShadow:"none",backgroundColor:"transparent"}},generateCloudPseudoElementsCSS:()=>{const e=document.createElement("style");return document.querySelectorAll(".cloud").forEach(((t,a)=>{const o=t,n=o.style.getPropertyValue("--pseudo-elements");if(n)try{const t=JSON.parse(n),l=`cloud-${a}`;o.classList.add(l);let i="";t.forEach(((e,t)=>{const a=(.2*Math.random()+.4).toFixed(2),o=Math.floor(10*Math.random())+12;if(Math.floor(15*Math.random()),0===t)i+=`.${l}::before { \n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left}; \n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.8) 0%,\n                  rgba(255, 255, 255, ${a}) 40%,\n                  rgba(255, 255, 255, 0.15) 75%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${o}px);\n                box-shadow: none;\n                opacity: ${a};\n                border: none;\n                outline: none;\n              }\n`;else if(1===t)i+=`.${l}::after { \n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left}; \n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.75) 0%,\n                  rgba(255, 255, 255, ${a}) 35%,\n                  rgba(255, 255, 255, 0.1) 70%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${o}px);\n                box-shadow: none;\n                opacity: ${a};\n                border: none;\n                outline: none;\n              }\n`;else{const a=(.2*Math.random()+.35).toFixed(2),o=Math.floor(8*Math.random())+10;i+=`.${l}::before { \n                content: ''; \n                position: absolute;\n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left};\n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.7) 0%,\n                  rgba(255, 255, 255, ${a}) 30%,\n                  rgba(255, 255, 255, 0.08) 65%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${o}px);\n                box-shadow: none;\n                opacity: ${a};\n                z-index: ${t};\n                border: none;\n                outline: none;\n              }\n`}})),e.textContent+=i}catch(l){}})),document.head.appendChild(e),e}},{getStarStyle:p}={getStarStyle:e=>{const t=Math.floor(2*Math.random())+1;return{width:`${t}px`,height:`${t}px`,left:`${Math.floor(100*Math.random())}%`,top:`${Math.floor(100*Math.random())}%`,opacity:.5*Math.random()+.3,animation:e%5==0?"twinkle 3s infinite":"none"}}},{getDandelionSeedStyle:v}={getDandelionSeedStyle:e=>{const t=Math.floor(3*Math.random())+2,a=Math.floor(100*Math.random()),o=Math.floor(10*Math.random())-5,n=80+Math.floor(40*Math.random()),l=Math.floor(10*Math.random())+5,i=Math.floor(40*Math.random())-20,r=Math.floor(20*Math.random())+20;return{width:`${t}px`,height:`${t}px`,left:`${a}%`,bottom:`${o}%`,animationDelay:`${Math.floor(15*Math.random())}s`,filter:`blur(${1.5*Math.random()+.5}px)`,"--seed-type":e%3,"--animation-duration":`${r}s`,"--float-height":`${n}vh`,"--float-side":`${i}vw`,"--float-side-wave":`${l}vw`,"--rotation":Math.floor(360*Math.random())-180+"deg","--max-opacity":.2*Math.random()+.7,"--fluff-count":"1","--core-size":`${t}px`}}},{getFireflyStyle:w}={getFireflyStyle:e=>{const t=Math.floor(4*Math.random())+3,a=Math.floor(100*Math.random()),o=Math.floor(70*Math.random())+10,n=Math.floor(3*Math.random())+1,l=Math.floor(5*Math.random())+3,i=Math.floor(4*Math.random())+2,r=2*n+l+i,s=Math.floor(8*Math.random())+3,c=Math.floor(10*Math.random()),d=(30*Math.random()+10)*(Math.random()>.5?1:-1),u=(30*Math.random()+10)*(Math.random()>.5?1:-1),m=["#80ff72","#c4ff0e","#e8ff75","#00ffaa"],p=m[e%m.length],v=Math.floor(2*Math.random())+1;return{width:`${t}px`,height:`${t}px`,left:`${a}%`,top:`${o}%`,backgroundColor:p,"--glow-color":p,"--glow-size":`${Math.floor(6*Math.random())+7}px`,"--pulse-duration":`${v}s`,"--move-x":`${d}px`,"--move-y":`${u}px`,"--appear-duration":`${n}s`,"--move-duration":`${l}s`,"--pause-duration":`${i}s`,"--total-duration":`${r+s}s`,"--cycle-delay":`${s}s`,animationDelay:`${c}s`}}};return s(l,(e=>{n("theme-change",e?"dark":"light")})),r((()=>{u()})),t({isDarkTheme:l}),(t,a)=>(m(),f("div",{class:b(["background-animation",{"dark-theme":h(l)}]),style:x(h(c))},[g("div",Yr,[!h(l)&&e.enableClouds?(m(),f("div",Jr,[(m(!0),f(S,null,A(Math.ceil(e.cloudCount),(e=>(m(),f("div",{key:`cloud-${e}`,class:"cloud",style:x(h(d)(e))},null,4)))),128))])):M("",!0),h(l)&&e.enableStars?(m(),f("div",Wr,[(m(!0),f(S,null,A(e.particleCount,(e=>(m(),f("div",{key:`star-${e}`,class:"star",style:x(h(p)(e))},null,4)))),128))])):M("",!0)]),g("div",Kr,[!h(l)&&e.enableDandelions?(m(),f("div",Gr,[(m(!0),f(S,null,A(e.particleCount,(e=>(m(),f("div",{key:`dandelion-${e}`,class:"dandelion-seed",style:x(h(v)(e))},a[0]||(a[0]=[g("div",{class:"main-stem"},null,-1)]),4)))),128))])):M("",!0),h(l)&&e.enableFireflies?(m(),f("div",Xr,[(m(!0),f(S,null,A(e.particleCount,(e=>(m(),f("div",{key:`firefly-${e}`,class:"firefly",style:x(h(w)(e))},null,4)))),128))])):M("",!0)])],6))}}),[["__scopeId","data-v-9f5b47f0"]]),Zr={class:"layout-container"},es={class:"header-container"},ts={class:"card-container"},as=["data-sitekey"],os={class:"login-form-btn"},ns=["data-sitekey"],ls={class:"register-form-btn"},is={class:"footer-container"},rs=Sa(l({__name:"Login",setup(e){const t=n(!1),a=()=>{t.value=!t.value},o=n(),l=n({phone:"",password:""}),s={phone:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少需要6个字符",trigger:"blur"}]},c=n(),d=n({username:"",phone:"",password:"",reenteredPassword:"",job:""}),u={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:10,message:"用户名长度在 3 到 10 个字符之间",trigger:"blur"}],...s,reenteredPassword:[{required:!0,message:"请再次输入密码",trigger:["input","blur"]},{validator:(e,t)=>!!d.value.password&&d.value.password.startsWith(t)&&d.value.password.length>=t.length,message:"两次密码输入不一致",trigger:"input"},{validator:(e,t)=>t===d.value.password,message:"两次密码输入不一致",trigger:["blur","password-input"]}],job:[{required:!0,message:"请输入职业",trigger:"blur"},{min:2,max:8,message:"职业名长度在 2 到 8 个字符之间",trigger:"blur"}]},w=Nt.cloudflareTurnstileSecret;r((()=>{const e=document.createElement("script");e.src="https://challenges.cloudflare.com/turnstile/v0/api.js",e.defer=!0,e.async=!0,document.head.appendChild(e)}));const b=()=>{o.value.validate((e=>{var t;if(!e){const e=(null==(t=document.querySelector('input[name="cf-turnstile-response"]'))?void 0:t.value)||"";if(!e)return void va.warning("请先通过验证哦~");ai.login({...l.value,cftt:e}).then((e=>{(null==e?void 0:e.data)&&(Gt.setLoginUser(e.data),Gt.set(Ot,!0),ss.push("/"))}))}}))},y=()=>{c.value.validate((e=>{var t;if(!e){const e=(null==(t=document.querySelector('input[name="cf-turnstile-response"]'))?void 0:t.value)||"";if(!e)return void va.warning("请先通过验证哦~");ai.register({...d.value,cftt:e}).then((e=>{e.data&&(Gt.setLoginUser(e.data),Gt.set(Ot,!0),ss.push("/"))}))}}))},k=e=>l.value.phone=e.replace(/\D/g,""),C=e=>d.value.phone=e.replace(/\D/g,""),L=i((()=>({paddingTop:"2rem",width:"22.5rem",transition:"transform 0.6s, box-shadow 0.3s",transformStyle:"preserve-3d",transform:t.value?"rotateY(180deg)":"none",backgroundColor:"var(--creamy-white-1)",boxShadow:"0 8px 30px rgba(0, 0, 0, 0.12)",backdropFilter:"blur(5px)",border:"1px solid rgba(255, 255, 255, 0.2)",opacity:.8})));return(e,n)=>(m(),f("div",Zr,[v(Qr,{particleCount:40}),g("div",es,[v(h(Be),{width:"200","preview-disabled":"",src:h(qr),loading:!0,"fallback-src":h(qr)},null,8,["src","fallback-src"])]),g("div",ts,[v(h(Se),{hoverable:"",style:x(L.value)},{default:p((()=>[P(v(h(re),{"label-placement":"left",model:l.value,rules:s,ref_key:"loginFormRef",ref:o,"label-width":80,class:"login-form"},{default:p((()=>[v(h(se),{label:"账号",path:"phone"},{default:p((()=>[v(h(le),{class:"login-form-ipt",maxlength:"11",value:l.value.phone,"onUpdate:value":n[0]||(n[0]=e=>l.value.phone=e),onInput:k,placeholder:"请输入手机号",onKeyup:H(b,["enter"])},null,8,["value"])])),_:1}),v(h(se),{label:"密码",path:"password"},{default:p((()=>[v(h(le),{class:"login-form-ipt",minlength:"6",value:l.value.password,"onUpdate:value":n[1]||(n[1]=e=>l.value.password=e),type:"password",placeholder:"请输入密码","show-password-on":"click",onKeyup:H(b,["enter"])},null,8,["value"])])),_:1}),g("div",{class:"cf-turnstile","data-sitekey":h(w)},null,8,as),g("div",os,[v(h(oe),{class:"login-btn",type:"info",onClick:b},{default:p((()=>n[7]||(n[7]=[z("登录")]))),_:1}),v(h(oe),{class:"flip-btn",onClick:a},{default:p((()=>n[8]||(n[8]=[z("注册")]))),_:1})])])),_:1},8,["model"]),[[U,!t.value]]),P(v(h(re),{"label-placement":"left",model:d.value,rules:u,ref_key:"registerFormRef",ref:c,"label-width":80,class:"register-form"},{default:p((()=>[v(h(se),{label:"用户名",path:"username"},{default:p((()=>[v(h(le),{class:"register-form-ipt",value:d.value.username,"onUpdate:value":n[2]||(n[2]=e=>d.value.username=e),placeholder:"请输入用户名",onKeyup:H(y,["enter"])},null,8,["value"])])),_:1}),v(h(se),{label:"手机号",path:"phone"},{default:p((()=>[v(h(le),{class:"register-form-ipt",maxlength:"11",value:d.value.phone,"onUpdate:value":n[3]||(n[3]=e=>d.value.phone=e),onInput:C,placeholder:"请输入手机号",onKeyup:H(y,["enter"])},null,8,["value"])])),_:1}),v(h(se),{label:"密码",path:"password"},{default:p((()=>[v(h(le),{class:"register-form-ipt",value:d.value.password,"onUpdate:value":n[4]||(n[4]=e=>d.value.password=e),type:"password",placeholder:"请输入密码","show-password-on":"click",onKeyup:H(y,["enter"])},null,8,["value"])])),_:1}),v(h(se),{label:"确认密码",path:"reenteredPassword"},{default:p((()=>[v(h(le),{class:"register-form-ipt",value:d.value.reenteredPassword,"onUpdate:value":n[5]||(n[5]=e=>d.value.reenteredPassword=e),type:"password",disabled:!d.value.password,placeholder:"请确认密码","show-password-on":"click",onKeyup:H(y,["enter"])},null,8,["value","disabled"])])),_:1}),v(h(se),{label:"职业",path:"job"},{default:p((()=>[v(h(le),{class:"register-form-ipt",value:d.value.job,"onUpdate:value":n[6]||(n[6]=e=>d.value.job=e),placeholder:"请输入职业",onKeyup:H(y,["enter"])},null,8,["value"])])),_:1}),g("div",{class:"cf-turnstile","data-sitekey":h(w)},null,8,ns),g("div",ls,[v(h(oe),{class:"login-btn",type:"info",onClick:y},{default:p((()=>n[9]||(n[9]=[z("注册并登录")]))),_:1}),v(h(oe),{class:"flip-btn",onClick:a},{default:p((()=>n[10]||(n[10]=[z("登录")]))),_:1})])])),_:1},8,["model"]),[[U,t.value]])])),_:1},8,["style"])]),g("div",is,[v(ni)])]))}}),[["__scopeId","data-v-bb701e4a"]]),ss=Ue({history:Fe("/"),routes:[{path:"/login",name:"Login",component:rs,meta:{requiresAuth:!1}},{path:"/",name:"Home",component:Nr,meta:{requiresAuth:!0}},{path:"/article/:articleId/:commentId?",name:"Article",component:tr,meta:{requiresAuth:!0}}]}),cs="Shenmo";ss.beforeEach(((e,t,a)=>{var o;document.title=`${cs} - ${e.name}`;const n=function(e){const t=document.cookie.split("; ");for(const a of t){const t=a.split("=");if(t[0]===e)return t[1]}return null}("wentk"),l=Hn(),i=Vn();if(l.setId(""),i.setId(""),(null==(o=e.meta)?void 0:o.requiresAuth)&&!n)a({name:"Login"});else if("Login"===e.name&&n)a({name:"Home"});else{if("Article"===e.name){const{articleId:t,commentId:a}=e.params;l.setId(String(t)),null!=a&&i.setId(String(a)),Fn.title(l.getId).then((e=>{document.title=`${cs} - ${e.data}`}))}a()}}));O(ia).use(ss).use(o()).mount("#app");export{Ml as _};
