{"version": 3, "sources": ["../../@tiptap/extension-document/src/document.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAMa,IAAA,WAAW,KAAK,OAAO;EAClC,MAAM;EACN,SAAS;EACT,SAAS;AACV,CAAA;", "names": []}