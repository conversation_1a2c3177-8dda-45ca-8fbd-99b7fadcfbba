<template>
  <div
    v-if="show"
    ref="popoverRef"
    :style="{
      position: 'fixed',
      left: position.x + 'px',
      top: position.y + 'px',
      zIndex: 1000,
      padding: 0,
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      backgroundColor: 'var(--n-color)',
      border: '1px solid var(--n-border-color)',
      minWidth: '200px',
      maxWidth: '300px'
    }"
  >
    <MentionList
      ref="mentionListRef"
      :items="items"
      :command="command"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import MentionList from './MentionList.vue'
import type { SearchUser } from '@/types/user.types'

interface Props {
  show: boolean
  items: SearchUser[]
  command: (item: { id: string; label: string; avatar?: string }) => void
  clientRect?: () => DOMRect | null
}

const props = defineProps<Props>()

const popoverRef = ref()
const mentionListRef = ref()

const position = ref({ x: 0, y: 0 })

// 监听clientRect变化，更新位置
watch(
  () => props.clientRect,
  async () => {
    if (props.clientRect && props.show) {
      await nextTick()
      updatePosition()
    }
  },
  { immediate: true }
)

// 监听show变化，更新位置
watch(
  () => props.show,
  async (newShow) => {
    if (newShow && props.clientRect) {
      await nextTick()
      updatePosition()
    }
  }
)

const updatePosition = () => {
  if (!props.clientRect) return
  
  const rect = props.clientRect()
  if (rect) {
    position.value = {
      x: rect.left,
      y: rect.bottom + 4
    }
  }
}

// 键盘事件处理
const onKeyDown = (event: { event: KeyboardEvent }) => {
  return mentionListRef.value?.onKeyDown(event)
}

// 暴露方法
defineExpose({
  onKeyDown,
  updatePosition
})
</script>
