import Mention from '@tiptap/extension-mention'
import { VueRenderer } from '@tiptap/vue-3'

import fileApi from '@/api/file'
import userApi from '@/api/user'
import MentionList from '@/components/tiptap/extensions/mention/MentionList.vue'
import type { ResponseData } from '@/types/response_data.types'

// 定义 @mention 节点的属性类型
export interface MentionAttributes {
  id: string
  label: string
  avatar?: string
}

const mention = Mention.extend({
  name: 'mention',

  addAttributes() {
    return {
      ...this.parent?.(),
      avatar: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-avatar'),
        renderHTML: (attributes: MentionAttributes) => {
          return attributes.avatar ? { 'data-avatar': attributes.avatar } : {}
        },
      },
    }
  },

  renderHTML({ node, HTMLAttributes }) {
    const attrs = node.attrs as MentionAttributes
    const children: Array<[string, Record<string, string>, string]> = [
      ['span', { class: 'mention-name' }, `@${attrs.label}`],
    ]

    if (attrs.avatar) {
      const avatarUrl = fileApi.getResourceURL(attrs.avatar)
      children.push([
        'img',
        {
          class: 'mention-avatar',
          src: avatarUrl,
          alt: attrs.label,
          loading: 'lazy',
        },
        '',
      ])
    }

    return [
      'span',
      {
        ...HTMLAttributes,
        'data-type': 'mention',
        'data-id': attrs.id,
        'data-label': attrs.label,
        'data-avatar': attrs.avatar || '',
        class: 'mention',
        contenteditable: 'false',
      },
      ...children,
    ]
  },
})

const mentionExtension = mention.configure({
  suggestion: {
    char: '@',
    items: async ({ query }) => {
      console.log('Mention items called with query:', query)
      if (!query) return []
      const res: ResponseData = await userApi.searchUser(query)
      console.log('Mention search result:', res.data)
      return res.data
    },

    render: () => {
      let component: VueRenderer
      let popup: HTMLElement | null = null

      return {
        onStart: (props) => {
          console.log('Mention onStart called', props)

          component = new VueRenderer(MentionList, {
            props,
            editor: props.editor,
          })

          if (!props.clientRect) {
            console.log('No clientRect provided')
            return
          }

          // 创建弹出容器，使用与斜杠菜单一致的样式
          popup = document.createElement('div')
          popup.className = 'mention-menu'
          popup.style.cssText = `
            position: fixed;
            z-index: 10000;
            white-space: nowrap;
            pointer-events: all;
            max-height: 15rem;
            padding: 0.25rem;
            overflow: hidden auto;
            border: 1px solid var(--border-color, #e5e7eb);
            border-radius: 0.5rem;
            background-color: var(--bg-color, white);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 10%), 0 2px 4px -2px rgba(0, 0, 0, 10%);
          `

          // 添加组件到弹出容器
          if (component.element) {
            console.log('Component element exists, appending to popup')
            popup.appendChild(component.element)
          } else {
            console.log('Component element does not exist!')
          }

          // 添加到body
          document.body.appendChild(popup)
          console.log('Popup appended to body')

          // 位置感知和定位
          updatePosition()

          function updatePosition() {
            if (!popup || !props.clientRect) return

            const rect = props.clientRect()
            if (!rect) return

            const viewportHeight = window.innerHeight
            const popupHeight = popup.offsetHeight || 240 // 默认最大高度
            const spaceBelow = viewportHeight - rect.bottom
            const spaceAbove = rect.top

            // 判断应该显示在上方还是下方
            const showAbove = spaceBelow < popupHeight && spaceAbove > spaceBelow

            if (showAbove) {
              // 显示在上方
              popup.className = 'mention-menu mention-menu-above'
              popup.style.transformOrigin = 'bottom center'
              popup.style.animation = 'slide-up-fade-in 0.15s ease-out'
              popup.style.left = rect.left + 'px'
              popup.style.top = (rect.top - popupHeight - 4) + 'px'
              console.log('Popup positioned above at:', rect.left, rect.top - popupHeight - 4)
            } else {
              // 显示在下方
              popup.className = 'mention-menu mention-menu-below'
              popup.style.transformOrigin = 'top center'
              popup.style.animation = 'slide-down-fade-in 0.15s ease-out'
              popup.style.left = rect.left + 'px'
              popup.style.top = (rect.bottom + 4) + 'px'
              console.log('Popup positioned below at:', rect.left, rect.bottom + 4)
            }
          }

          console.log('Popup element:', popup)
          console.log('Popup in DOM:', document.body.contains(popup))
        },

        onUpdate(props) {
          console.log('Mention onUpdate called', props)

          if (component) {
            component.updateProps(props)
          }

          if (!props.clientRect || !popup) {
            return
          }

          // 使用相同的位置感知逻辑更新位置
          const rect = props.clientRect()
          if (!rect) return

          const viewportHeight = window.innerHeight
          const popupHeight = popup.offsetHeight || 240
          const spaceBelow = viewportHeight - rect.bottom
          const spaceAbove = rect.top

          const showAbove = spaceBelow < popupHeight && spaceAbove > spaceBelow

          if (showAbove) {
            popup.className = 'mention-menu mention-menu-above'
            popup.style.left = rect.left + 'px'
            popup.style.top = (rect.top - popupHeight - 4) + 'px'
          } else {
            popup.className = 'mention-menu mention-menu-below'
            popup.style.left = rect.left + 'px'
            popup.style.top = (rect.bottom + 4) + 'px'
          }
        },

        onKeyDown(props) {
          if (props.event.key === 'Escape') {
            return true
          }

          return component.ref?.onKeyDown(props)
        },

        onExit() {
          console.log('Mention onExit called')

          if (popup && popup.parentNode) {
            popup.parentNode.removeChild(popup)
          }
          if (component) {
            component.destroy()
          }
        },
      }
    },
  },
})

export default mentionExtension
