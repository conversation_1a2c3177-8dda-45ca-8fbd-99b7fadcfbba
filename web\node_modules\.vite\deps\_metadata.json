{"hash": "8ecd20ab", "configHash": "2ef55ced", "lockfileHash": "92464989", "browserHash": "0fa2d000", "optimized": {"@stomp/stompjs": {"src": "../../@stomp/stompjs/esm6/index.js", "file": "@stomp_stompjs.js", "fileHash": "edd178a7", "needsInterop": false}, "@tiptap/core": {"src": "../../@tiptap/core/dist/index.js", "file": "@tiptap_core.js", "fileHash": "3a6aea20", "needsInterop": false}, "@tiptap/extension-blockquote": {"src": "../../@tiptap/extension-blockquote/dist/index.js", "file": "@tiptap_extension-blockquote.js", "fileHash": "50d3c4ad", "needsInterop": false}, "@tiptap/extension-bold": {"src": "../../@tiptap/extension-bold/dist/index.js", "file": "@tiptap_extension-bold.js", "fileHash": "61565eee", "needsInterop": false}, "@tiptap/extension-bubble-menu": {"src": "../../@tiptap/extension-bubble-menu/dist/index.js", "file": "@tiptap_extension-bubble-menu.js", "fileHash": "548d0b92", "needsInterop": false}, "@tiptap/extension-bullet-list": {"src": "../../@tiptap/extension-bullet-list/dist/index.js", "file": "@tiptap_extension-bullet-list.js", "fileHash": "96abfc55", "needsInterop": false}, "@tiptap/extension-code": {"src": "../../@tiptap/extension-code/dist/index.js", "file": "@tiptap_extension-code.js", "fileHash": "e9cd993e", "needsInterop": false}, "@tiptap/extension-code-block-lowlight": {"src": "../../@tiptap/extension-code-block-lowlight/dist/index.js", "file": "@tiptap_extension-code-block-lowlight.js", "fileHash": "54bc7bdc", "needsInterop": false}, "@tiptap/extension-color": {"src": "../../@tiptap/extension-color/dist/index.js", "file": "@tiptap_extension-color.js", "fileHash": "d9d1088c", "needsInterop": false}, "@tiptap/extension-document": {"src": "../../@tiptap/extension-document/dist/index.js", "file": "@tiptap_extension-document.js", "fileHash": "98e4c6a1", "needsInterop": false}, "@tiptap/extension-dropcursor": {"src": "../../@tiptap/extension-dropcursor/dist/index.js", "file": "@tiptap_extension-dropcursor.js", "fileHash": "a0c0d3ff", "needsInterop": false}, "@tiptap/extension-floating-menu": {"src": "../../@tiptap/extension-floating-menu/dist/index.js", "file": "@tiptap_extension-floating-menu.js", "fileHash": "d9db2664", "needsInterop": false}, "@tiptap/extension-focus": {"src": "../../@tiptap/extension-focus/dist/index.js", "file": "@tiptap_extension-focus.js", "fileHash": "dbb1e294", "needsInterop": false}, "@tiptap/extension-gapcursor": {"src": "../../@tiptap/extension-gapcursor/dist/index.js", "file": "@tiptap_extension-gapcursor.js", "fileHash": "152649bb", "needsInterop": false}, "@tiptap/extension-heading": {"src": "../../@tiptap/extension-heading/dist/index.js", "file": "@tiptap_extension-heading.js", "fileHash": "ca3b4cdd", "needsInterop": false}, "@tiptap/extension-highlight": {"src": "../../@tiptap/extension-highlight/dist/index.js", "file": "@tiptap_extension-highlight.js", "fileHash": "d49b17c3", "needsInterop": false}, "@tiptap/extension-history": {"src": "../../@tiptap/extension-history/dist/index.js", "file": "@tiptap_extension-history.js", "fileHash": "4d406f1c", "needsInterop": false}, "@tiptap/extension-horizontal-rule": {"src": "../../@tiptap/extension-horizontal-rule/dist/index.js", "file": "@tiptap_extension-horizontal-rule.js", "fileHash": "10de1282", "needsInterop": false}, "@tiptap/extension-image": {"src": "../../@tiptap/extension-image/dist/index.js", "file": "@tiptap_extension-image.js", "fileHash": "f51bf82d", "needsInterop": false}, "@tiptap/extension-italic": {"src": "../../@tiptap/extension-italic/dist/index.js", "file": "@tiptap_extension-italic.js", "fileHash": "c77b8ff8", "needsInterop": false}, "@tiptap/extension-link": {"src": "../../@tiptap/extension-link/dist/index.js", "file": "@tiptap_extension-link.js", "fileHash": "325ea35d", "needsInterop": false}, "@tiptap/extension-list-item": {"src": "../../@tiptap/extension-list-item/dist/index.js", "file": "@tiptap_extension-list-item.js", "fileHash": "4e44278c", "needsInterop": false}, "@tiptap/extension-mention": {"src": "../../@tiptap/extension-mention/dist/index.js", "file": "@tiptap_extension-mention.js", "fileHash": "9cd1e91c", "needsInterop": false}, "@tiptap/extension-ordered-list": {"src": "../../@tiptap/extension-ordered-list/dist/index.js", "file": "@tiptap_extension-ordered-list.js", "fileHash": "582e6d0c", "needsInterop": false}, "@tiptap/extension-paragraph": {"src": "../../@tiptap/extension-paragraph/dist/index.js", "file": "@tiptap_extension-paragraph.js", "fileHash": "c71748a6", "needsInterop": false}, "@tiptap/extension-placeholder": {"src": "../../@tiptap/extension-placeholder/dist/index.js", "file": "@tiptap_extension-placeholder.js", "fileHash": "26241df1", "needsInterop": false}, "@tiptap/extension-strike": {"src": "../../@tiptap/extension-strike/dist/index.js", "file": "@tiptap_extension-strike.js", "fileHash": "faabcfda", "needsInterop": false}, "@tiptap/extension-task-item": {"src": "../../@tiptap/extension-task-item/dist/index.js", "file": "@tiptap_extension-task-item.js", "fileHash": "0448c520", "needsInterop": false}, "@tiptap/extension-task-list": {"src": "../../@tiptap/extension-task-list/dist/index.js", "file": "@tiptap_extension-task-list.js", "fileHash": "553b6fd0", "needsInterop": false}, "@tiptap/extension-text": {"src": "../../@tiptap/extension-text/dist/index.js", "file": "@tiptap_extension-text.js", "fileHash": "8a67ebf6", "needsInterop": false}, "@tiptap/extension-text-align": {"src": "../../@tiptap/extension-text-align/dist/index.js", "file": "@tiptap_extension-text-align.js", "fileHash": "924a5d49", "needsInterop": false}, "@tiptap/extension-text-style": {"src": "../../@tiptap/extension-text-style/dist/index.js", "file": "@tiptap_extension-text-style.js", "fileHash": "9f405430", "needsInterop": false}, "@tiptap/extension-typography": {"src": "../../@tiptap/extension-typography/dist/index.js", "file": "@tiptap_extension-typography.js", "fileHash": "a3d40bed", "needsInterop": false}, "@tiptap/extension-underline": {"src": "../../@tiptap/extension-underline/dist/index.js", "file": "@tiptap_extension-underline.js", "fileHash": "67184af8", "needsInterop": false}, "@tiptap/pm/state": {"src": "../../@tiptap/pm/state/dist/index.js", "file": "@tiptap_pm_state.js", "fileHash": "6a0785d2", "needsInterop": false}, "@tiptap/pm/view": {"src": "../../@tiptap/pm/view/dist/index.js", "file": "@tiptap_pm_view.js", "fileHash": "ca03b43f", "needsInterop": false}, "@tiptap/suggestion": {"src": "../../@tiptap/suggestion/dist/index.js", "file": "@tiptap_suggestion.js", "fileHash": "73183031", "needsInterop": false}, "@tiptap/vue-3": {"src": "../../@tiptap/vue-3/dist/index.js", "file": "@tiptap_vue-3.js", "fileHash": "073ddd70", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "6cb9cdc4", "needsInterop": false}, "lowlight": {"src": "../../lowlight/index.js", "file": "lowlight.js", "fileHash": "286097b0", "needsInterop": false}, "naive-ui": {"src": "../../naive-ui/es/index.mjs", "file": "naive-ui.js", "fileHash": "2a413c84", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "2797487f", "needsInterop": false}, "smooth-scroll-into-view-if-needed": {"src": "../../smooth-scroll-into-view-if-needed/dist/index.js", "file": "smooth-scroll-into-view-if-needed.js", "fileHash": "53bb348c", "needsInterop": false}, "sockjs-client": {"src": "../../sockjs-client/lib/entry.js", "file": "sockjs-client.js", "fileHash": "ec55f6a4", "needsInterop": true}, "tiptap-markdown": {"src": "../../tiptap-markdown/dist/tiptap-markdown.es.js", "file": "tiptap-markdown.js", "fileHash": "84eec6fb", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "a12e99fb", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "f2d06980", "needsInterop": false}}, "chunks": {"chunk-CYF2IF63": {"file": "chunk-CYF2IF63.js"}, "chunk-ZPQY4RR2": {"file": "chunk-ZPQY4RR2.js"}, "chunk-V6ZM6662": {"file": "chunk-V6ZM6662.js"}, "chunk-S5KADFJU": {"file": "chunk-S5KADFJU.js"}, "chunk-E3PGEGIX": {"file": "chunk-E3PGEGIX.js"}, "chunk-MFZPBBD5": {"file": "chunk-MFZPBBD5.js"}, "chunk-TCXDHPQV": {"file": "chunk-TCXDHPQV.js"}, "chunk-YS6A6Z55": {"file": "chunk-YS6A6Z55.js"}, "chunk-REYSTJ5T": {"file": "chunk-REYSTJ5T.js"}, "chunk-445NV6YP": {"file": "chunk-445NV6YP.js"}, "chunk-ONIL7SAW": {"file": "chunk-ONIL7SAW.js"}, "chunk-ZMSOBIYE": {"file": "chunk-ZMSOBIYE.js"}}}