<template>
  <div class="mention-list-container">
    <div v-if="items.length" class="mention-list">
      <button
        v-for="(item, index) in items"
        :key="index"
        type="button"
        :class="['mention-button', { 'mention-button-active': index === selectedIndex }]"
        :data-active="index === selectedIndex ? 'true' : undefined"
        @click="selectItem(index)"
      >
        <div class="mention-button-icon">
          <img
            v-if="item.avatar"
            :src="fileApi.getResourceURL(item.avatar)"
            :alt="item.username"
            class="mention-avatar"
          />
          <div v-else class="mention-avatar mention--avatar-fallback">
            {{ item.username.charAt(0).toUpperCase() }}
          </div>
        </div>
        <div class="mention-name">
          {{ item.username }}
        </div>
      </button>
    </div>
    <div v-else class="mention-empty">
      ...
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

import fileApi from '@/api/file'
import type { SearchUser } from '@/types/user.types'
const props = defineProps<{
  items: SearchUser[]
  command: (item: { id: string; label: string; avatar?: string }) => void
}>()

const selectedIndex = ref(0)

// 监听 items 变化，重置 selectedIndex
watch(
  () => props.items,
  () => {
    selectedIndex.value = 0
  },
)

// 键盘事件处理
const onKeyDown = ({ event }: { event: KeyboardEvent }) => {
  if (event.key === 'ArrowUp') {
    upHandler()
    return true
  }

  if (event.key === 'ArrowDown') {
    downHandler()
    return true
  }

  if (event.key === 'Enter') {
    enterHandler()
    return true
  }

  return false
}

// 上箭头处理
const upHandler = () => {
  selectedIndex.value = (selectedIndex.value + props.items.length - 1) % props.items.length
}

// 下箭头处理
const downHandler = () => {
  selectedIndex.value = (selectedIndex.value + 1) % props.items.length
}

// 回车处理
const enterHandler = () => {
  selectItem(selectedIndex.value)
}

// 选择项处理
const selectItem = (index: number) => {
  const item: SearchUser = props.items[index] as SearchUser
  if (item) {
    const commandData = { id: item.id, label: item.username, avatar: item.avatar }
    props.command(commandData)
  }
}

// 暴露 onKeyDown 方法
defineExpose({
  onKeyDown,
})
</script>

<style scoped>
/* 与斜杠菜单完全一致的样式 */
.mention-list-container {
  /* 容器不需要额外样式，由外层mention-menu控制 */
}

.mention-list {
  /* 列表容器样式 */
}

.mention-empty {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 0.875rem;
  height: 1.25rem;
  padding: 0 0.75rem;
  color: var(--text-muted, #6b7280);
}

/* 按钮样式 - 完全去掉padding属性 */
.mention-button {
  appearance: none;
  user-select: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  line-height: 1;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  outline: none;
  width: 100%;
  height: 1.9rem;
  border-radius: 0.375rem;
  background-color: transparent;
  color: var(--text-color, #374151);
  transition: all 0.15s ease;
}

.mention-button:hover,
.mention-button:focus,
.mention-button[data-active] {
  color: var(--text-active, #1f2937);
  background-color: var(--bg-hover, #f3f4f6);
}

.mention-button-icon {
  margin-right: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 使用与mention元素一致的样式 */
.mention-avatar {
  max-width: initial;
  min-width: initial;
  display: inline-block;
  margin: 0;
  box-shadow: none;
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  vertical-align: middle;
  transition: none;
  object-fit: cover;
  flex-shrink: 0;
}

.mention--avatar-fallback {
  background-color: var(--purple, #6a00f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.65rem;
  font-weight: 600;
}

.mention-name {
  font-size: 0.9rem;
  color: var(--purple);
  font-weight: 500;
  flex-shrink: 0;
  flex-grow: 1;
  text-align: start;
}
</style>
