// 气泡菜单样式 - 与斜杠菜单保持一致
.editor-bubble-menu {
  background-color: var(--bg-color, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 10%),
    0 2px 4px -2px rgba(0, 0, 0, 10%);
  display: flex;
  flex-wrap: wrap;
  z-index: 100;
  max-width: min(16rem, 100%);
  transform-origin: center bottom;
  animation: slide-up-fade-in 0.15s ease-out;
}

// 浮动菜单样式 - 与斜杠菜单保持一致
.editor-floating-menu {
  background-color: var(--bg-color, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 10%),
    0 2px 4px -2px rgba(0, 0, 0, 10%);
  display: flex;
  z-index: 100;
  transform-origin: top center;
  animation: slide-down-fade-in 0.15s ease-out;
}

// 工具栏样式
.tiptap-editor-wrapper {
  .editor-toolbar {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 4px;
    padding: 4px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
  }

  .editor-toolbar-bgc {
    background-color: white;
  }

  /* 全屏模式下工具栏背景色 */
  &.tiptap-fullscreen .editor-toolbar-bgc {
    background-color: inherit !important;
  }
}

/* 为暗色模式添加编辑器气泡菜单样式 - 与斜杠菜单保持一致 */
.dark-theme .editor-bubble-menu,
.dark-theme .editor-floating-menu {
  --bg-color: #1f2937;
  --border-color: #374151;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 30%),
    0 2px 4px -2px rgba(0, 0, 0, 20%);
}

/* 为暗色主题的工具栏添加适当的背景颜色 */
.dark-theme .tiptap-editor-wrapper .editor-toolbar-bgc {
  background-color: var(--white-2);
}

// 动画定义 - 与斜杠菜单保持一致
@keyframes slide-down-fade-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-up-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 确保暗模式下全屏工具栏的背景色 */
.dark-theme .tiptap-editor-wrapper.tiptap-fullscreen .editor-toolbar-bgc {
  background-color: var(--white-2) !important;
}
