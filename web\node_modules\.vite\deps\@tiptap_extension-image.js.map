{"version": 3, "sources": ["../../@tiptap/extension-image/src/image.ts"], "sourcesContent": ["import {\n  mergeAttributes,\n  Node,\n  nodeInputRule,\n} from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean,\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean,\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: { src: string, alt?: string, title?: string }) => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64\n          ? 'img[src]'\n          : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage: options => ({ commands }) => {\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [,, alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAiDO,IAAM,aAAa;AAMb,IAAA,QAAQ,KAAK,OAAqB;EAC7C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,QAAQ;MACR,aAAa;MACb,gBAAgB,CAAA;;;EAIpB,SAAM;AACJ,WAAO,KAAK,QAAQ;;EAGtB,QAAK;AACH,WAAO,KAAK,QAAQ,SAAS,WAAW;;EAG1C,WAAW;EAEX,gBAAa;AACX,WAAO;MACL,KAAK;QACH,SAAS;MACV;MACD,KAAK;QACH,SAAS;MACV;MACD,OAAO;QACL,SAAS;MACV;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK,KAAK,QAAQ,cACd,aACA;MACL;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,OAAO,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;;EAG7E,cAAW;AACT,WAAO;MACL,UAAU,aAAW,CAAC,EAAE,SAAQ,MAAM;AACpC,eAAO,SAAS,cAAc;UAC5B,MAAM,KAAK;UACX,OAAO;QACR,CAAA;;;;EAKP,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;QACX,eAAe,WAAQ;AACrB,gBAAM,CAAA,EAAA,EAAI,KAAK,KAAK,KAAK,IAAI;AAE7B,iBAAO,EAAE,KAAK,KAAK,MAAK;;OAE3B;;;AAGN,CAAA;", "names": []}