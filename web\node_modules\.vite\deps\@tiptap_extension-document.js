import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  Node
} from "./chunk-YS6A6Z55.js";
import "./chunk-REYSTJ5T.js";
import "./chunk-445NV6YP.js";
import "./chunk-ONIL7SAW.js";
import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-ZMSOBIYE.js";

// node_modules/@tiptap/extension-document/dist/index.js
var import_dist = __toESM(require_dist());
var import_dist2 = __toESM(require_dist2());
var import_dist3 = __toESM(require_dist3());
var Document = Node.create({
  name: "doc",
  topNode: true,
  content: "block+"
});
export {
  Document,
  Document as default
};
//# sourceMappingURL=@tiptap_extension-document.js.map
